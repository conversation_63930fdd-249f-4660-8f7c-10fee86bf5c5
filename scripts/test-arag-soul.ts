#!/usr/bin/env tsx
// ARAG-Soul 框架测试脚本

import { config } from 'dotenv';
import { aragSoulService } from '../src/lib/services/arag-soul';

// 加载环境变量
config({ path: '.env.local' });

async function testAragSoul() {
  try {
    console.log('🧪 开始测试 ARAG-Soul 框架...');
    console.log('⏰ 时间:', new Date().toISOString());
    
    // 检查环境变量
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY 环境变量未设置');
    }
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL 环境变量未设置');
    }
    
    console.log('✅ 环境变量检查通过');
    
    // 测试用户ID（需要在数据库中存在）
    const testUserId = 'test-user-id';
    
    console.log(`🎯 开始为用户 ${testUserId} 生成候选人矩阵...`);
    
    // 使用流式执行来监控进度
    console.log('📊 执行进度:');
    for await (const step of aragSoulService.generateCandidateMatrixStream(testUserId)) {
      if (step.error) {
        console.error(`❌ 步骤失败: ${step.step}`, step.error);
        break;
      } else {
        console.log(`✅ ${step.step} - ${step.timestamp.toISOString()}`);
      }
    }
    
    console.log('🎉 ARAG-Soul 框架测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('用户不存在')) {
        console.log('💡 提示: 请先运行 npm run db:seed 创建测试用户');
      } else if (error.message.includes('GEMINI_API_KEY')) {
        console.log('💡 提示: 请在 .env.local 中设置 GEMINI_API_KEY');
      } else if (error.message.includes('DATABASE_URL')) {
        console.log('💡 提示: 请在 .env.local 中设置 DATABASE_URL');
      }
    }
    
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  testAragSoul();
}

export { testAragSoul };
