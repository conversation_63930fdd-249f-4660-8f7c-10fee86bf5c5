#!/usr/bin/env tsx
// 本地队列处理脚本

import { config } from 'dotenv';
import { MatchingServiceV2 } from '../src/lib/services/matching-v2';

// 加载环境变量
config({ path: '.env' });

async function processQueue() {
  try {
    console.log('🚀 启动队列处理器...');
    console.log('⏰ 时间:', new Date().toISOString());

    // 检查环境变量
    console.log('🔍 检查环境变量...');
    console.log('DATABASE_URL 存在:', !!process.env.DATABASE_URL);
    console.log('OPENROUTER_API_KEY 存在:', !!process.env.OPENROUTER_API_KEY);

    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL 环境变量未设置');
    }

    // 解析 DATABASE_URL 检查格式
    try {
      const url = new URL(process.env.DATABASE_URL);
      console.log('数据库连接信息:');
      console.log('- 主机:', url.hostname);
      console.log('- 端口:', url.port);
      console.log('- 数据库:', url.pathname.slice(1));
      console.log('- 用户名:', url.username);
      console.log('- 密码长度:', url.password.length);
    } catch (urlError) {
      console.error('DATABASE_URL 格式错误:', urlError);
      throw new Error('DATABASE_URL 格式不正确');
    }

    await MatchingServiceV2.processQueuedRequests();

    console.log('✅ 队列处理完成');
  } catch (error) {
    console.error('❌ 队列处理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  processQueue();
}

export { processQueue };
