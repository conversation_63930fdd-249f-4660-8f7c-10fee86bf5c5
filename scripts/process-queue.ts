#!/usr/bin/env tsx
// 本地队列处理脚本

import { config } from 'dotenv';
import { MatchingServiceV2 } from '../src/lib/services/matching-v2';

// 加载环境变量
config({ path: '.env' });

async function processQueue() {
  try {
    console.log('🚀 启动队列处理器...');
    console.log('⏰ 时间:', new Date().toISOString());
    
    await MatchingServiceV2.processQueuedRequests();
    
    console.log('✅ 队列处理完成');
  } catch (error) {
    console.error('❌ 队列处理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  processQueue();
}

export { processQueue };
