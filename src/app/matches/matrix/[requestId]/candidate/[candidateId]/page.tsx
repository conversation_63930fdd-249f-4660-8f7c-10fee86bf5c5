'use client';

import { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Heart, X, Star, ThumbsUp, ThumbsDown, Play, Pause, MessageCircle, Calendar, MapPin } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface CandidateDetailData {
  id: string;
  requestId: string;
  candidateId: string;
  rank: number;
  compatibilityScore: number;
  reasoning: string;
  highlights: string[];
  challenges: string[];
  personalitySummary: string;
  relationshipInsight?: {
    strengths: string[];
    challenges: string[];
    suggestions: string[];
    communicationTips: string[];
  };
  conversationSimulation?: {
    scenario: string;
    messages: Array<{
      speaker: 'user' | 'candidate';
      content: string;
      emotion?: string;
    }>;
    analysis: string;
  };
  datePlan?: {
    title: string;
    description: string;
    location: string;
    reasoning: string;
    activities: Array<{
      name: string;
      duration: string;
      description: string;
    }>;
    time_planning: {
      start_time: string;
      end_time: string;
      total_duration: string;
    };
    budget_suggestion: {
      range: string;
      details: string;
    };
  };
  userDecision: 'pending' | 'liked' | 'skipped';
  candidateInfo?: {
    name: string;
    age: number;
    gender: string;
    interests: string[];
  };
}

export default function CandidateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [candidate, setCandidate] = useState<CandidateDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showAllMessages, setShowAllMessages] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const requestId = params.requestId as string;
  const candidateId = params.candidateId as string;

  useEffect(() => {
    fetchCandidateDetail();
  }, [requestId, candidateId]);

  const fetchCandidateDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/matches/matrix/${requestId}/candidates/${candidateId}/detail`);
      
      if (response.ok) {
        const data = await response.json();
        setCandidate(data.candidate);
      } else {
        console.error('获取候选人详情失败');
      }
    } catch (error) {
      console.error('获取候选人详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDecision = async (decision: 'liked' | 'skipped') => {
    try {
      const response = await fetch(`/api/matches/matrix/${requestId}/candidates/${candidateId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ decision }),
      });

      if (response.ok) {
        const result = await response.json();
        setCandidate(prev => prev ? { ...prev, userDecision: decision } : null);
        
        if (result.mutualMatch) {
          alert('🎉 恭喜！你们互相喜欢了！');
        }
      }
    } catch (error) {
      console.error('决策失败:', error);
    }
  };

  // 对话播放逻辑
  const startConversation = () => {
    if (!candidate?.conversationSimulation) return;
    
    setIsPlaying(true);
    setCurrentMessageIndex(0);
    setShowAllMessages(false);
    
    intervalRef.current = setInterval(() => {
      setCurrentMessageIndex(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= candidate.conversationSimulation!.messages.length) {
          setIsPlaying(false);
          setShowAllMessages(true);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          return prev;
        }
        return nextIndex;
      });
    }, 2000);
  };

  const pauseConversation = () => {
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const showFullConversation = () => {
    setShowAllMessages(true);
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">候选人信息不存在</div>
      </div>
    );
  }

  const isTopMatch = candidate.rank === 1;

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 导航栏 */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push(`/matches/matrix/${requestId}`)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          返回候选人矩阵
        </Button>

        {candidate.userDecision === 'pending' && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleDecision('skipped')}
              className="flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              跳过
            </Button>
            <Button
              onClick={() => handleDecision('liked')}
              className="flex items-center gap-2 bg-pink-500 hover:bg-pink-600"
            >
              <Heart className="w-4 h-4" />
              喜欢
            </Button>
          </div>
        )}
      </div>

      {/* 候选人基本信息 */}
      <Card className={`mb-6 ${isTopMatch ? 'border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50' : ''}`}>
        <CardHeader className="text-center">
          {isTopMatch && (
            <div className="flex justify-center mb-2">
              <Badge className="bg-pink-500 text-white px-3 py-1">
                <Star className="w-4 h-4 mr-1" />
                首席推荐
              </Badge>
            </div>
          )}
          <CardTitle className="text-2xl">
            {candidate.candidateInfo?.name || '候选人'} 
            {candidate.candidateInfo?.age && `, ${candidate.candidateInfo.age}岁`}
          </CardTitle>
          <div className="flex justify-center items-center gap-2 mt-2">
            <div className="text-3xl font-bold text-pink-600">{candidate.compatibilityScore}</div>
            <div className="text-sm text-gray-600">匹配度</div>
          </div>
          <CardDescription className="mt-2">
            {candidate.personalitySummary}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* 关系亮点 */}
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">关系亮点</h4>
            <div className="flex flex-wrap gap-2">
              {candidate.highlights.map((highlight, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {highlight}
                </Badge>
              ))}
            </div>
          </div>

          {/* 兼容性推理 */}
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">兼容性分析</h4>
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              <ReactMarkdown>{candidate.reasoning}</ReactMarkdown>
            </div>
          </div>

          {/* 决策状态 */}
          {candidate.userDecision !== 'pending' && (
            <div className="text-center py-2">
              <Badge variant={candidate.userDecision === 'liked' ? 'default' : 'secondary'}>
                {candidate.userDecision === 'liked' ? '已喜欢 ❤️' : '已跳过 ⏭️'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详细内容 - 根据候选人类型显示不同的标签页 */}
      {isTopMatch && candidate.relationshipInsight ? (
        // 首席推荐：显示完整的三个标签页
        <Tabs defaultValue="relationship" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="relationship">关系洞察</TabsTrigger>
            <TabsTrigger value="conversation">模拟对话</TabsTrigger>
            <TabsTrigger value="date">约会计划</TabsTrigger>
          </TabsList>

          <TabsContent value="relationship" className="space-y-4">
            <RelationshipInsightTab insight={candidate.relationshipInsight} challenges={candidate.challenges} />
          </TabsContent>

          <TabsContent value="conversation" className="space-y-4">
            {candidate.conversationSimulation && (
              <ConversationTab
                simulation={candidate.conversationSimulation}
                currentMessageIndex={currentMessageIndex}
                isPlaying={isPlaying}
                showAllMessages={showAllMessages}
                onStart={startConversation}
                onPause={pauseConversation}
                onShowAll={showFullConversation}
              />
            )}
          </TabsContent>

          <TabsContent value="date" className="space-y-4">
            {candidate.datePlan && <DatePlanTab datePlan={candidate.datePlan} />}
          </TabsContent>
        </Tabs>
      ) : (
        // 潜力候选人：只显示基础分析
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">详细分析</CardTitle>
            <CardDescription>
              {isTopMatch ? '首席推荐的完整分析' : '潜力候选人的基础分析'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 潜在挑战 */}
            {candidate.challenges && candidate.challenges.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <ThumbsDown className="w-4 h-4 text-amber-600" />
                  需要注意的方面
                </h4>
                <div className="space-y-2">
                  {candidate.challenges.map((challenge: string, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg">
                      <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-sm text-gray-700">{challenge}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 详细推理 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">兼容性详细分析</h4>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700 prose prose-sm max-w-none">
                  <ReactMarkdown>
                    {candidate.reasoning}
                  </ReactMarkdown>
                </div>
              </div>
            </div>

            {/* 提示信息 */}
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                💡 <strong>提示：</strong>
                {isTopMatch
                  ? '作为首席推荐，我们为您准备了完整的关系洞察、对话模拟和约会计划。'
                  : '这是一位潜力候选人。如果您对TA感兴趣，可以先表达喜欢，我们会为您生成更详细的分析。'
                }
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// 关系洞察标签页
function RelationshipInsightTab({ insight, challenges }: { insight: any; challenges: string[] }) {
  return (
    <div className="space-y-6">
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-green-800">
            <ThumbsUp className="w-5 h-5" />
            关系优势
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {insight.strengths?.map((strength: string, index: number) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">{strength}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-amber-800">
            <ThumbsDown className="w-5 h-5" />
            潜在挑战
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {challenges?.map((challenge: string, index: number) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
                <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">{challenge}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-blue-800">
            <Star className="w-5 h-5" />
            相处建议
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {insight.suggestions?.map((suggestion: string, index: number) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">{suggestion}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {insight.communicationTips && insight.communicationTips.length > 0 && (
        <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2 text-purple-800">
              <MessageCircle className="w-5 h-5" />
              沟通技巧
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {insight.communicationTips.map((tip: string, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{tip}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// 对话模拟标签页
function ConversationTab({ simulation, currentMessageIndex, isPlaying, showAllMessages, onStart, onPause, onShowAll }: any) {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            {simulation.scenario}
          </CardTitle>
          <div className="flex gap-2">
            {!isPlaying && !showAllMessages && (
              <Button onClick={onStart} size="sm">
                <Play className="w-4 h-4 mr-1" />
                开始对话
              </Button>
            )}
            {isPlaying && (
              <Button onClick={onPause} size="sm" variant="outline">
                <Pause className="w-4 h-4 mr-1" />
                暂停
              </Button>
            )}
            {!showAllMessages && (
              <Button onClick={onShowAll} size="sm" variant="outline">
                查看全部
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {simulation.messages.slice(0, showAllMessages ? undefined : currentMessageIndex + 1).map((message: any, index: number) => (
            <div
              key={index}
              className={`p-3 rounded-lg ${
                message.speaker === 'user'
                  ? 'bg-blue-100 ml-8'
                  : 'bg-white mr-8 border'
              }`}
            >
              <div className="text-xs text-gray-500 mb-1">
                {message.speaker === 'user' ? '你' : '对方'}
                {message.emotion && ` (${message.emotion})`}
              </div>
              <div className="text-sm">{message.content}</div>
            </div>
          ))}
        </div>
        
        {showAllMessages && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
            <h5 className="text-sm font-medium mb-1">对话分析</h5>
            <p className="text-sm text-gray-600">{simulation.analysis}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 约会计划标签页
function DatePlanTab({ datePlan }: { datePlan: any }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {datePlan.title}
          </CardTitle>
          <CardDescription>{datePlan.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium mb-1 flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                约会地点
              </h5>
              <p className="text-gray-600">{datePlan.location}</p>
            </div>
            <div>
              <h5 className="font-medium mb-1">时间安排</h5>
              <p className="text-gray-600">
                {datePlan.time_planning?.start_time} - {datePlan.time_planning?.end_time}
              </p>
              <p className="text-xs text-gray-500">总时长：{datePlan.time_planning?.total_duration}</p>
            </div>
            <div className="md:col-span-2">
              <h5 className="font-medium mb-1">预算建议</h5>
              <p className="text-gray-600">{datePlan.budget_suggestion?.range}</p>
              <p className="text-xs text-gray-500">{datePlan.budget_suggestion?.details}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">活动安排</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {datePlan.activities?.map((activity: any, index: number) => (
              <div key={index} className="border-l-4 border-pink-200 pl-4 py-2">
                <h6 className="font-medium text-gray-900">{activity.name}</h6>
                <p className="text-sm text-gray-600 mb-1">{activity.description}</p>
                <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                  时长：{activity.duration}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">推荐理由</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
            <p className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
              {datePlan.reasoning}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
