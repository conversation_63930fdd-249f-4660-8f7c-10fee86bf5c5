'use client';

import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Heart, X, Star, ThumbsUp, ThumbsDown, Play, Pause, MessageCircle, Calendar, MapPin } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface CandidateDetailData {
  id: string;
  requestId: string;
  candidateId: string;
  rank: number;
  compatibilityScore: number;
  reasoning: string;
  highlights: string[];
  challenges: string[];
  personalitySummary: string;
  relationshipInsight?: {
    strengths: string[];
    challenges: string[];
    suggestions: string[];
    communicationTips: string[];
  };
  conversationSimulation?: {
    scenario: string;
    messages: Array<{
      speaker: 'user' | 'candidate';
      content: string;
      emotion?: string;
    }>;
    analysis: string;
  };
  datePlan?: {
    title: string;
    description: string;
    location: string;
    activities: string[];
    duration: string;
    budget: string;
    reasoning: string;
  };
  userDecision: 'pending' | 'liked' | 'skipped';
  candidateInfo?: {
    name: string;
    age: number;
    gender: string;
    interests: string[];
  };
}

export default function CandidateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [candidate, setCandidate] = useState<CandidateDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showAllMessages, setShowAllMessages] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const requestId = params.requestId as string;
  const candidateId = params.candidateId as string;

  useEffect(() => {
    fetchCandidateDetail();
  }, [requestId, candidateId]);

  const fetchCandidateDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/matches/matrix/${requestId}/candidates/${candidateId}/detail`);
      
      if (response.ok) {
        const data = await response.json();
        setCandidate(data.candidate);
      } else {
        console.error('获取候选人详情失败');
      }
    } catch (error) {
      console.error('获取候选人详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDecision = async (decision: 'liked' | 'skipped') => {
    try {
      const response = await fetch(`/api/matches/matrix/${requestId}/candidates/${candidateId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ decision }),
      });

      if (response.ok) {
        const result = await response.json();
        setCandidate(prev => prev ? { ...prev, userDecision: decision } : null);
        
        if (result.mutualMatch) {
          alert('🎉 恭喜！你们互相喜欢了！');
        }
      }
    } catch (error) {
      console.error('决策失败:', error);
    }
  };

  // 对话播放逻辑
  const startConversation = () => {
    if (!candidate?.conversationSimulation) return;
    
    setIsPlaying(true);
    setCurrentMessageIndex(0);
    setShowAllMessages(false);
    
    intervalRef.current = setInterval(() => {
      setCurrentMessageIndex(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= candidate.conversationSimulation!.messages.length) {
          setIsPlaying(false);
          setShowAllMessages(true);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          return prev;
        }
        return nextIndex;
      });
    }, 2000);
  };

  const pauseConversation = () => {
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const showFullConversation = () => {
    setShowAllMessages(true);
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">候选人信息不存在</div>
      </div>
    );
  }

  const isTopMatch = candidate.rank === 1;

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 导航栏 */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push(`/matches/matrix/${requestId}`)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          返回候选人矩阵
        </Button>

        {candidate.userDecision === 'pending' && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleDecision('skipped')}
              className="flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              跳过
            </Button>
            <Button
              onClick={() => handleDecision('liked')}
              className="flex items-center gap-2 bg-pink-500 hover:bg-pink-600"
            >
              <Heart className="w-4 h-4" />
              喜欢
            </Button>
          </div>
        )}
      </div>

      {/* 候选人基本信息 */}
      <Card className={`mb-6 ${isTopMatch ? 'border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50' : ''}`}>
        <CardHeader className="text-center">
          {isTopMatch && (
            <div className="flex justify-center mb-2">
              <Badge className="bg-pink-500 text-white px-3 py-1">
                <Star className="w-4 h-4 mr-1" />
                首席推荐
              </Badge>
            </div>
          )}
          <CardTitle className="text-2xl">
            {candidate.candidateInfo?.name || '候选人'} 
            {candidate.candidateInfo?.age && `, ${candidate.candidateInfo.age}岁`}
          </CardTitle>
          <div className="flex justify-center items-center gap-2 mt-2">
            <div className="text-3xl font-bold text-pink-600">{candidate.compatibilityScore}</div>
            <div className="text-sm text-gray-600">匹配度</div>
          </div>
          <CardDescription className="mt-2">
            {candidate.personalitySummary}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* 关系亮点 */}
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">关系亮点</h4>
            <div className="flex flex-wrap gap-2">
              {candidate.highlights.map((highlight, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {highlight}
                </Badge>
              ))}
            </div>
          </div>

          {/* 兼容性推理 */}
          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">兼容性分析</h4>
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              <ReactMarkdown>{candidate.reasoning}</ReactMarkdown>
            </div>
          </div>

          {/* 决策状态 */}
          {candidate.userDecision !== 'pending' && (
            <div className="text-center py-2">
              <Badge variant={candidate.userDecision === 'liked' ? 'default' : 'secondary'}>
                {candidate.userDecision === 'liked' ? '已喜欢 ❤️' : '已跳过 ⏭️'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 首席推荐的详细内容 */}
      {isTopMatch && candidate.relationshipInsight && (
        <Tabs defaultValue="relationship" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="relationship">关系洞察</TabsTrigger>
            <TabsTrigger value="conversation">模拟对话</TabsTrigger>
            <TabsTrigger value="date">约会计划</TabsTrigger>
          </TabsList>
          
          <TabsContent value="relationship" className="space-y-4">
            <RelationshipInsightTab insight={candidate.relationshipInsight} challenges={candidate.challenges} />
          </TabsContent>
          
          <TabsContent value="conversation" className="space-y-4">
            {candidate.conversationSimulation && (
              <ConversationTab 
                simulation={candidate.conversationSimulation}
                currentMessageIndex={currentMessageIndex}
                isPlaying={isPlaying}
                showAllMessages={showAllMessages}
                onStart={startConversation}
                onPause={pauseConversation}
                onShowAll={showFullConversation}
              />
            )}
          </TabsContent>
          
          <TabsContent value="date" className="space-y-4">
            {candidate.datePlan && <DatePlanTab datePlan={candidate.datePlan} />}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

// 关系洞察标签页
function RelationshipInsightTab({ insight, challenges }: { insight: any; challenges: string[] }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">关系优势</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {insight.strengths.map((strength: string, index: number) => (
              <li key={index}>{strength}</li>
            ))}
          </ul>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">潜在挑战</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {challenges.map((challenge: string, index: number) => (
              <li key={index}>{challenge}</li>
            ))}
          </ul>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">相处建议</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {insight.suggestions.map((suggestion: string, index: number) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}

// 对话模拟标签页
function ConversationTab({ simulation, currentMessageIndex, isPlaying, showAllMessages, onStart, onPause, onShowAll }: any) {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            {simulation.scenario}
          </CardTitle>
          <div className="flex gap-2">
            {!isPlaying && !showAllMessages && (
              <Button onClick={onStart} size="sm">
                <Play className="w-4 h-4 mr-1" />
                开始对话
              </Button>
            )}
            {isPlaying && (
              <Button onClick={onPause} size="sm" variant="outline">
                <Pause className="w-4 h-4 mr-1" />
                暂停
              </Button>
            )}
            {!showAllMessages && (
              <Button onClick={onShowAll} size="sm" variant="outline">
                查看全部
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {simulation.messages.slice(0, showAllMessages ? undefined : currentMessageIndex + 1).map((message: any, index: number) => (
            <div
              key={index}
              className={`p-3 rounded-lg ${
                message.speaker === 'user'
                  ? 'bg-blue-100 ml-8'
                  : 'bg-white mr-8 border'
              }`}
            >
              <div className="text-xs text-gray-500 mb-1">
                {message.speaker === 'user' ? '你' : '对方'}
                {message.emotion && ` (${message.emotion})`}
              </div>
              <div className="text-sm">{message.content}</div>
            </div>
          ))}
        </div>
        
        {showAllMessages && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
            <h5 className="text-sm font-medium mb-1">对话分析</h5>
            <p className="text-sm text-gray-600">{simulation.analysis}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 约会计划标签页
function DatePlanTab({ datePlan }: { datePlan: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          {datePlan.title}
        </CardTitle>
        <CardDescription>{datePlan.description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <h5 className="font-medium mb-1 flex items-center gap-1">
              <MapPin className="w-3 h-3" />
              地点
            </h5>
            <p>{datePlan.location}</p>
          </div>
          <div>
            <h5 className="font-medium mb-1">时长</h5>
            <p>{datePlan.duration}</p>
          </div>
          <div>
            <h5 className="font-medium mb-1">预算</h5>
            <p>{datePlan.budget}</p>
          </div>
        </div>
        
        <div>
          <h5 className="font-medium mb-2">活动安排</h5>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {datePlan.activities.map((activity: string, index: number) => (
              <li key={index}>{activity}</li>
            ))}
          </ul>
        </div>
        
        <div className="p-3 bg-purple-50 rounded-lg">
          <h5 className="text-sm font-medium mb-1">推荐理由</h5>
          <p className="text-sm text-gray-600">{datePlan.reasoning}</p>
        </div>
      </CardContent>
    </Card>
  );
}
