// 队列处理 Worker API

import { NextRequest, NextResponse } from 'next/server';
import { MatchingServiceV2 } from '@/lib/services/matching-v2';

// 处理匹配队列
export async function POST(request: NextRequest) {
  try {
    // 验证请求来源（可以添加密钥验证）
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.WORKER_SECRET_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔄 Worker: 开始处理匹配队列');
    
    // 处理队列中的任务
    await MatchingServiceV2.processQueuedRequests();
    
    return NextResponse.json({
      success: true,
      message: '队列处理完成',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Worker: 队列处理失败:', error);
    return NextResponse.json({
      error: 'PROCESSING_FAILED',
      message: error instanceof Error ? error.message : '队列处理失败'
    }, { status: 500 });
  }
}

// 健康检查
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'queue-processor',
    timestamp: new Date().toISOString()
  });
}
