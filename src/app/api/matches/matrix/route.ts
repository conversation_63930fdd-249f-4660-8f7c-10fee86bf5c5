// V2.0 智能候选人矩阵 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { matchQueue, matchRequests, type NewMatchQueue, type NewMatchRequest } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { MatchingServiceV2 } from '@/lib/services/matching-v2';

// 异步处理匹配请求
async function processMatchRequestAsync(requestId: string, requesterId: string) {
  try {
    console.log(`🚀 开始异步处理匹配请求: ${requestId}`);
    await MatchingServiceV2.processSingleRequest(requestId, requesterId);
    console.log(`✅ 异步处理完成: ${requestId}`);
  } catch (error) {
    console.error(`❌ 异步处理失败: ${requestId}`, error);
  }
}

// 生成智能候选人矩阵请求
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 检查每日限制（复用 v1.0 的限制逻辑）
    const { MatchingService } = await import('@/lib/services/matching');
    const limitCheck = await MatchingService.checkDailyMatchLimit(user.id);
    
    if (!limitCheck.canMatch) {
      return NextResponse.json({
        error: 'DAILY_LIMIT_EXCEEDED',
        message: '今日匹配次数已用完',
        limitInfo: limitCheck
      }, { status: 429 });
    }

    // 生成请求ID
    const requestId = uuidv4();

    // 创建匹配请求记录
    const matchRequestData: NewMatchRequest = {
      id: requestId,
      requesterId: user.id,
      status: 'processing'
    };

    const [matchRequest] = await db.insert(matchRequests).values(matchRequestData).returning();

    // 创建任务队列记录
    const queueData: NewMatchQueue = {
      matchRequestId: requestId,
      requesterId: user.id,
      status: 'pending'
    };

    await db.insert(matchQueue).values(queueData);

    console.log(`📝 创建匹配矩阵请求: ${requestId} for user: ${user.id}`);

    // 立即触发异步处理
    processMatchRequestAsync(requestId, user.id).catch(error => {
      console.error(`异步处理失败 ${requestId}:`, error);
    });

    // 立即返回请求ID，实际处理将异步进行
    return NextResponse.json({
      success: true,
      requestId: requestId,
      status: 'processing',
      message: '专属红娘正在为您筛选候选人，请稍候...',
      estimatedTime: '2-3分钟'
    }, { status: 202 });

  } catch (error) {
    console.error('创建匹配矩阵请求失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 获取匹配矩阵结果
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const requestId = url.searchParams.get('requestId');

    if (!requestId) {
      return NextResponse.json({
        error: 'BAD_REQUEST',
        message: '缺少 requestId 参数'
      }, { status: 400 });
    }

    // 查询匹配请求状态
    const matchRequest = await db
      .select()
      .from(matchRequests)
      .where(eq(matchRequests.id, requestId))
      .limit(1);

    if (matchRequest.length === 0) {
      return NextResponse.json({
        error: 'NOT_FOUND',
        message: '匹配请求不存在'
      }, { status: 404 });
    }

    const request_data = matchRequest[0];

    // 检查权限
    if (request_data.requesterId !== user.id) {
      return NextResponse.json({
        error: 'FORBIDDEN',
        message: '无权访问此匹配请求'
      }, { status: 403 });
    }

    // 根据状态返回不同响应
    switch (request_data.status) {
      case 'processing':
        return NextResponse.json({
          status: 'processing',
          message: '正在生成匹配矩阵，请稍候...',
          requestId: requestId
        });

      case 'completed':
        if (!request_data.finalReport) {
          return NextResponse.json({
            error: 'DATA_ERROR',
            message: '匹配结果数据异常'
          }, { status: 500 });
        }

        return NextResponse.json({
          status: 'completed',
          requestId: requestId,
          matrix: request_data.finalReport,
          generatedAt: request_data.createdAt
        });

      case 'failed':
        return NextResponse.json({
          status: 'failed',
          message: request_data.errorMessage || '匹配生成失败',
          requestId: requestId
        }, { status: 500 });

      default:
        return NextResponse.json({
          error: 'UNKNOWN_STATUS',
          message: '未知的请求状态'
        }, { status: 500 });
    }

  } catch (error) {
    console.error('获取匹配矩阵结果失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
