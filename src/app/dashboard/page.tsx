'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Heart, Brain, Users, Eye, Plus, Star } from 'lucide-react';
import Link from 'next/link';

interface MatchMatrix {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  createdAt: string;
  candidatesCount?: number;
}

interface ReceivedMatch {
  id: string;
  requestId: string;
  rank: number;
  compatibilityScore: number;
  reasoning: string;
  highlights: string[];
  personalitySummary: string;
  userDecision: string;
  createdAt: string;
  requester: {
    id: string;
    name: string;
    age: number;
    gender: string;
    interests: string[];
    bio: string;
  };
}

export default function DashboardPage() {
  const router = useRouter();
  const supabase = createClient();
  const [matchMatrices, setMatchMatrices] = useState<MatchMatrix[]>([]);
  const [receivedMatches, setReceivedMatches] = useState<ReceivedMatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('matrices');

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }
    await fetchDashboardData();
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // 获取用户的匹配矩阵
      const matricesResponse = await fetch('/api/matches/matrix/list');
      if (matricesResponse.ok) {
        const matricesData = await matricesResponse.json();
        setMatchMatrices(matricesData.matrices || []);
      }

      // 获取收到的匹配请求
      const receivedResponse = await fetch('/api/matches/received');
      if (receivedResponse.ok) {
        const receivedData = await receivedResponse.json();
        setReceivedMatches(receivedData.receivedMatches || []);
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMatrix = async () => {
    try {
      const response = await fetch('/api/matches/matrix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        router.push(`/matches/matrix/${result.requestId}`);
      } else {
        alert('生成匹配矩阵失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建匹配矩阵失败:', error);
      alert('生成匹配矩阵失败，请稍后重试');
    }
  };

  const handleRespondToMatch = async (matchId: string, decision: 'liked' | 'skipped') => {
    try {
      const response = await fetch('/api/matches/received', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          matchCandidateId: matchId,
          decision
        }),
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.mutualMatch && result.contactInfo) {
          alert(result.contactInfo.message);
        }
        
        // 刷新数据
        fetchDashboardData();
      }
    } catch (error) {
      console.error('回应匹配失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">我的匹配中心</h1>
        <p className="text-gray-600">管理您的匹配矩阵和收到的匹配请求</p>
      </div>

      {/* 快速操作 */}
      <div className="mb-8">
        <Card className="bg-gradient-to-r from-pink-50 to-purple-50 border-pink-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">生成新的匹配矩阵</h3>
                <p className="text-gray-600">AI 将为您分析并推荐最合适的候选人</p>
              </div>
              <Button onClick={handleCreateMatrix} className="bg-pink-600 hover:bg-pink-700">
                <Plus className="w-4 h-4 mr-2" />
                生成矩阵
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="matrices" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            我的匹配矩阵 ({matchMatrices.length})
          </TabsTrigger>
          <TabsTrigger value="received" className="flex items-center gap-2">
            <Heart className="w-4 h-4" />
            收到的喜欢 ({receivedMatches.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="matrices" className="space-y-4">
          <MatchMatricesTab matrices={matchMatrices} />
        </TabsContent>

        <TabsContent value="received" className="space-y-4">
          <ReceivedMatchesTab 
            matches={receivedMatches} 
            onRespond={handleRespondToMatch}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// 匹配矩阵标签页
function MatchMatricesTab({ matrices }: { matrices: MatchMatrix[] }) {
  if (matrices.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有匹配矩阵</h3>
          <p className="text-gray-600 mb-4">点击上方按钮生成您的第一个匹配矩阵</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {matrices.map((matrix) => (
        <Card key={matrix.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                  <Brain className="w-6 h-6 text-pink-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    匹配矩阵 #{matrix.id.slice(-8)}
                  </h3>
                  <p className="text-sm text-gray-600">
                    创建于 {new Date(matrix.createdAt).toLocaleDateString()}
                  </p>
                  {matrix.candidatesCount && (
                    <p className="text-xs text-gray-500">
                      包含 {matrix.candidatesCount} 个候选人
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Badge variant={
                  matrix.status === 'completed' ? 'default' :
                  matrix.status === 'processing' ? 'secondary' : 'destructive'
                }>
                  {matrix.status === 'completed' ? '已完成' :
                   matrix.status === 'processing' ? '处理中' : '失败'}
                </Badge>
                
                {matrix.status === 'completed' && (
                  <Link href={`/matches/matrix/${matrix.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      查看详情
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// 收到的匹配标签页
function ReceivedMatchesTab({
  matches,
  onRespond
}: {
  matches: ReceivedMatch[];
  onRespond: (matchId: string, decision: 'liked' | 'skipped') => void;
}) {
  if (matches.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有收到喜欢</h3>
          <p className="text-gray-600">当有人喜欢您时，会在这里显示</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {matches.map((match) => (
        <Card key={match.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="w-8 h-8 text-pink-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-medium text-gray-900">
                      {match.requester.name}
                    </h3>
                    <Badge variant="secondary">
                      {match.requester.age}岁
                    </Badge>
                    <Badge variant="outline">
                      匹配度 {match.compatibilityScore}
                    </Badge>
                    {match.rank === 1 && (
                      <Badge className="bg-pink-500">
                        <Star className="w-3 h-3 mr-1" />
                        首席推荐
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-3">
                    {match.personalitySummary}
                  </p>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {match.highlights.slice(0, 3).map((highlight, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {highlight}
                      </Badge>
                    ))}
                  </div>

                  <p className="text-xs text-gray-500">
                    {new Date(match.createdAt).toLocaleDateString()} 喜欢了您
                  </p>
                </div>
              </div>

              {match.userDecision === 'liked' && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRespond(match.id, 'skipped')}
                  >
                    跳过
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => onRespond(match.id, 'liked')}
                    className="bg-pink-600 hover:bg-pink-700"
                  >
                    <Heart className="w-4 h-4 mr-1" />
                    喜欢
                  </Button>
                </div>
              )}

              {match.userDecision === 'mutual_liked' && (
                <Badge className="bg-green-500">
                  <Heart className="w-3 h-3 mr-1" />
                  互相喜欢
                </Badge>
              )}

              {match.userDecision === 'skipped' && (
                <Badge variant="secondary">已跳过</Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
