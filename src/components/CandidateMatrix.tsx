'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Heart, X, Star, MessageCircle, Calendar, MapPin } from 'lucide-react';
import type { CandidateMatrix } from '@/lib/services/arag-soul/types';

interface CandidateMatrixProps {
  matrix: CandidateMatrix;
  onCandidateDecision: (candidateId: string, decision: 'liked' | 'skipped') => Promise<void>;
}

export function CandidateMatrix({ matrix, onCandidateDecision }: CandidateMatrixProps) {
  const [decisions, setDecisions] = useState<Record<string, 'liked' | 'skipped'>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const handleDecision = async (candidateId: string, decision: 'liked' | 'skipped') => {
    setLoading(prev => ({ ...prev, [candidateId]: true }));
    try {
      await onCandidateDecision(candidateId, decision);
      setDecisions(prev => ({ ...prev, [candidateId]: decision }));
    } catch (error) {
      console.error('决策失败:', error);
    } finally {
      setLoading(prev => ({ ...prev, [candidateId]: false }));
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">您的专属推荐</h1>
        <p className="text-gray-600">AI红娘为您精心挑选的5位候选人</p>
        <p className="text-sm text-gray-500">
          生成时间: {new Date(matrix.generatedAt).toLocaleString('zh-CN')}
        </p>
      </div>

      {/* 首席推荐 */}
      <TopMatchCard 
        topMatch={matrix.topMatch}
        decision={decisions[matrix.topMatch.candidate.candidateId]}
        loading={loading[matrix.topMatch.candidate.candidateId]}
        onDecision={handleDecision}
      />

      {/* 潜力候选人 */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <Star className="w-5 h-5 text-yellow-500" />
          其他潜力候选人
        </h2>
        <div className="grid gap-4 md:grid-cols-2">
          {matrix.potentialMatches.map((match, index) => (
            <PotentialMatchCard
              key={match.candidate.candidateId}
              match={match}
              rank={index + 2}
              decision={decisions[match.candidate.candidateId]}
              loading={loading[match.candidate.candidateId]}
              onDecision={handleDecision}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

// 首席推荐卡片
function TopMatchCard({ topMatch, decision, loading, onDecision }: any) {
  const [showDetails, setShowDetails] = useState(false);
  const { candidate, relationshipInsight, conversationSimulation, datePlan } = topMatch;

  return (
    <Card className="border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-2">
          <Badge className="bg-pink-500 text-white px-3 py-1">
            <Star className="w-4 h-4 mr-1" />
            首席推荐
          </Badge>
        </div>
        <CardTitle className="text-2xl">{candidate.personalitySummary}</CardTitle>
        <div className="flex justify-center items-center gap-2">
          <div className="text-3xl font-bold text-pink-600">{candidate.compatibilityScore}</div>
          <div className="text-sm text-gray-600">匹配度</div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 亮点展示 */}
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">关系亮点</h4>
          <div className="flex flex-wrap gap-2">
            {candidate.highlights.map((highlight: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {highlight}
              </Badge>
            ))}
          </div>
        </div>

        {/* 详情按钮 */}
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              查看完整分析报告
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>首席推荐 - 完整分析报告</DialogTitle>
            </DialogHeader>
            <TopMatchDetails 
              candidate={candidate}
              relationshipInsight={relationshipInsight}
              conversationSimulation={conversationSimulation}
              datePlan={datePlan}
            />
          </DialogContent>
        </Dialog>

        {/* 决策按钮 */}
        <DecisionButtons
          candidateId={candidate.candidateId}
          decision={decision}
          loading={loading}
          onDecision={onDecision}
        />
      </CardContent>
    </Card>
  );
}

// 潜力候选人卡片
function PotentialMatchCard({ match, rank, decision, loading, onDecision }: any) {
  const { candidate } = match;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <Badge variant="outline" className="mb-2">#{rank}</Badge>
            <CardTitle className="text-lg">{candidate.personalitySummary}</CardTitle>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-blue-600">{candidate.compatibilityScore}</div>
            <div className="text-xs text-gray-500">匹配度</div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 亮点 */}
        <div className="space-y-2">
          <h5 className="text-sm font-medium text-gray-700">亮点</h5>
          <div className="flex flex-wrap gap-1">
            {candidate.highlights.slice(0, 3).map((highlight: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {highlight}
              </Badge>
            ))}
          </div>
        </div>

        {/* 兼容性原因 */}
        <div className="text-sm text-gray-600">
          {match.compatibilityReason.slice(0, 100)}...
        </div>

        {/* 决策按钮 */}
        <DecisionButtons
          candidateId={candidate.candidateId}
          decision={decision}
          loading={loading}
          onDecision={onDecision}
        />
      </CardContent>
    </Card>
  );
}

// 决策按钮组件
function DecisionButtons({ candidateId, decision, loading, onDecision }: any) {
  if (decision) {
    return (
      <div className="text-center py-2">
        <Badge variant={decision === 'liked' ? 'default' : 'secondary'}>
          {decision === 'liked' ? '已喜欢 ❤️' : '已跳过 ⏭️'}
        </Badge>
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        className="flex-1 border-red-200 hover:bg-red-50"
        onClick={() => onDecision(candidateId, 'skipped')}
        disabled={loading}
      >
        <X className="w-4 h-4 mr-1" />
        跳过
      </Button>
      <Button
        size="sm"
        className="flex-1 bg-pink-500 hover:bg-pink-600"
        onClick={() => onDecision(candidateId, 'liked')}
        disabled={loading}
      >
        <Heart className="w-4 h-4 mr-1" />
        喜欢
      </Button>
    </div>
  );
}

// 首席推荐详情组件
function TopMatchDetails({ candidate, relationshipInsight, conversationSimulation, datePlan }: any) {
  return (
    <Tabs defaultValue="analysis" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="analysis">关系分析</TabsTrigger>
        <TabsTrigger value="conversation">模拟对话</TabsTrigger>
        <TabsTrigger value="date">约会计划</TabsTrigger>
        <TabsTrigger value="profile">详细资料</TabsTrigger>
      </TabsList>
      
      <TabsContent value="analysis" className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">关系优势</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {relationshipInsight.strengths.map((strength: string, index: number) => (
              <li key={index}>{strength}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">潜在挑战</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {relationshipInsight.challenges.map((challenge: string, index: number) => (
              <li key={index}>{challenge}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">相处建议</h4>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {relationshipInsight.suggestions.map((suggestion: string, index: number) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      </TabsContent>
      
      <TabsContent value="conversation" className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <MessageCircle className="w-4 h-4" />
            {conversationSimulation.scenario}
          </h4>
          <div className="space-y-3">
            {conversationSimulation.messages.map((message: any, index: number) => (
              <div
                key={index}
                className={`p-3 rounded-lg ${
                  message.speaker === 'user'
                    ? 'bg-blue-100 ml-8'
                    : 'bg-white mr-8'
                }`}
              >
                <div className="text-xs text-gray-500 mb-1">
                  {message.speaker === 'user' ? '你' : '对方'}
                  {message.emotion && ` (${message.emotion})`}
                </div>
                <div className="text-sm">{message.content}</div>
              </div>
            ))}
          </div>
          <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
            <h5 className="text-sm font-medium mb-1">对话分析</h5>
            <p className="text-sm text-gray-600">{conversationSimulation.analysis}</p>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="date" className="space-y-4">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            {datePlan.title}
          </h4>
          <p className="text-sm text-gray-600 mb-3">{datePlan.description}</p>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium mb-1 flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                地点
              </h5>
              <p>{datePlan.location}</p>
            </div>
            <div>
              <h5 className="font-medium mb-1">时长</h5>
              <p>{datePlan.duration}</p>
            </div>
            <div>
              <h5 className="font-medium mb-1">预算</h5>
              <p>{datePlan.budget}</p>
            </div>
          </div>
          
          <div className="mt-3">
            <h5 className="font-medium mb-1">活动安排</h5>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {datePlan.activities.map((activity: string, index: number) => (
                <li key={index}>{activity}</li>
              ))}
            </ul>
          </div>
          
          <div className="mt-3 p-3 bg-white rounded-lg">
            <h5 className="text-sm font-medium mb-1">推荐理由</h5>
            <p className="text-sm text-gray-600">{datePlan.reasoning}</p>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="profile" className="space-y-4">
        <div className="space-y-3">
          <div>
            <h5 className="font-medium mb-1">兼容性分数</h5>
            <div className="text-2xl font-bold text-blue-600">{candidate.compatibilityScore}/100</div>
          </div>
          
          <div>
            <h5 className="font-medium mb-1">人格摘要</h5>
            <p className="text-sm text-gray-600">{candidate.personalitySummary}</p>
          </div>
          
          <div>
            <h5 className="font-medium mb-1">匹配推理</h5>
            <p className="text-sm text-gray-600">{candidate.reasoning}</p>
          </div>
          
          <div>
            <h5 className="font-medium mb-1">关系亮点</h5>
            <div className="flex flex-wrap gap-2">
              {candidate.highlights.map((highlight: string, index: number) => (
                <Badge key={index} variant="secondary">{highlight}</Badge>
              ))}
            </div>
          </div>
          
          {candidate.challenges && candidate.challenges.length > 0 && (
            <div>
              <h5 className="font-medium mb-1">潜在挑战</h5>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                {candidate.challenges.map((challenge: string, index: number) => (
                  <li key={index}>{challenge}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </TabsContent>
    </Tabs>
  );
}
