// ARAG-Soul 框架的 Agent 实现

import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { PROMPTS, SYSTEM_PROMPTS } from './prompts';
import type { 
  AragSoulState, 
  UserSoulProfile, 
  CandidateAnalysis,
  RelationshipInsight,
  ConversationSimulation,
  DatePlan 
} from './types';

// 初始化 Gemini 模型
const createModel = (systemPrompt: string = SYSTEM_PROMPTS.default) => {
  return new ChatGoogleGenerativeAI({
    model: "gemini-1.5-pro",
    temperature: 0.7,
    maxOutputTokens: 2048,
    apiKey: process.env.GEMINI_API_KEY,
  });
};

// 人格洞察 Agent
export async function generateUserSoulProfileNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  try {
    console.log('🧠 执行人格洞察 Agent...');
    
    const { userProfile } = state;
    if (!userProfile) {
      throw new Error('用户资料不存在');
    }

    const model = createModel(SYSTEM_PROMPTS.personality);
    
    const prompt = PROMPTS.personalityInsight
      .replace('{name}', userProfile.name || '未知')
      .replace('{age}', userProfile.age?.toString() || '未知')
      .replace('{gender}', userProfile.gender || '未知')
      .replace('{selfDescription}', userProfile.selfDescription || '')
      .replace('{interests}', JSON.stringify(userProfile.interests || []))
      .replace('{values}', JSON.stringify(userProfile.values || []))
      .replace('{lifestyle}', JSON.stringify(userProfile.lifestyle || {}))
      .replace('{relationshipGoals}', userProfile.relationshipGoals || '');

    const response = await model.invoke([
      { role: 'system', content: SYSTEM_PROMPTS.personality },
      { role: 'user', content: prompt }
    ]);

    const result = JSON.parse(response.content as string);
    
    const userSoulProfile: UserSoulProfile = {
      userId: userProfile.userId,
      personalityTraits: result.personalityTraits,
      coreValues: result.coreValues,
      communicationStyle: result.communicationStyle,
      relationshipGoals: userProfile.relationshipGoals,
      lifestyle: userProfile.lifestyle,
      summary: result.summary
    };

    console.log('✅ 人格洞察完成');
    return { 
      userSoulProfile,
      step: 'personality_insight_completed'
    };
  } catch (error) {
    console.error('❌ 人格洞察失败:', error);
    return { 
      error: `人格洞察失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'personality_insight_failed'
    };
  }
}

// 深度兼容性推理 Agent
export async function runCompatibilityInferenceNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  try {
    console.log('🔍 执行兼容性推理 Agent...');
    
    const { userSoulProfile, candidatePoolIds } = state;
    if (!userSoulProfile || !candidatePoolIds) {
      throw new Error('缺少必要的状态数据');
    }

    // 这里需要从数据库获取候选人资料
    // 为了演示，我们先创建一个模拟的候选人分析函数
    const candidatesWithAnalysis: CandidateAnalysis[] = [];
    
    const model = createModel(SYSTEM_PROMPTS.compatibility);

    // 并行处理所有候选人
    const analysisPromises = candidatePoolIds.map(async (candidateId) => {
      // TODO: 从数据库获取候选人详细资料
      const candidateProfile = await getCandidateProfile(candidateId);
      
      const prompt = PROMPTS.compatibilityInference
        .replace('{userSoulProfile}', JSON.stringify(userSoulProfile))
        .replace('{candidateName}', candidateProfile.name || '未知')
        .replace('{candidateAge}', candidateProfile.age?.toString() || '未知')
        .replace('{candidateSelfDescription}', candidateProfile.selfDescription || '')
        .replace('{candidateInterests}', JSON.stringify(candidateProfile.interests || []))
        .replace('{candidateValues}', JSON.stringify(candidateProfile.values || []))
        .replace('{candidateLifestyle}', JSON.stringify(candidateProfile.lifestyle || {}));

      const response = await model.invoke([
        { role: 'system', content: SYSTEM_PROMPTS.compatibility },
        { role: 'user', content: prompt }
      ]);

      const result = JSON.parse(response.content as string);
      
      return {
        candidateId,
        compatibilityScore: result.compatibilityScore,
        reasoning: result.reasoning,
        highlights: result.highlights,
        challenges: result.challenges,
        personalitySummary: result.personalitySummary
      } as CandidateAnalysis;
    });

    const analysisResults = await Promise.all(analysisPromises);
    candidatesWithAnalysis.push(...analysisResults);

    console.log(`✅ 兼容性推理完成，分析了 ${candidatesWithAnalysis.length} 个候选人`);
    return { 
      candidatesWithAnalysis,
      step: 'compatibility_inference_completed'
    };
  } catch (error) {
    console.error('❌ 兼容性推理失败:', error);
    return { 
      error: `兼容性推理失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'compatibility_inference_failed'
    };
  }
}

// 从数据库获取候选人资料
async function getCandidateProfile(candidateId: string) {
  const { db } = await import('@/lib/db');
  const { users, userProfiles } = await import('@/lib/db/schema');
  const { eq } = await import('drizzle-orm');

  const candidateWithProfile = await db
    .select({
      user: users,
      profile: userProfiles
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(eq(users.id, candidateId))
    .limit(1);

  if (candidateWithProfile.length === 0) {
    throw new Error(`候选人 ${candidateId} 不存在`);
  }

  const candidate = candidateWithProfile[0];
  return {
    userId: candidate.user.id,
    name: candidate.user.name || '未知',
    age: candidate.user.age || 0,
    selfDescription: candidate.profile?.selfDescription || '',
    interests: candidate.user.interests || [],
    values: candidate.profile?.values || [],
    lifestyle: candidate.profile?.lifestyle || {}
  };
}

// 排序和最终决策 Agent
export async function rankAndFinalizeNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  try {
    console.log('🏆 执行排序和最终决策 Agent...');
    
    const { candidatesWithAnalysis } = state;
    if (!candidatesWithAnalysis || candidatesWithAnalysis.length === 0) {
      throw new Error('没有候选人分析数据');
    }

    // 按兼容性分数排序
    const rankedCandidates = candidatesWithAnalysis
      .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
      .slice(0, 5); // 取前5名

    console.log(`✅ 排序完成，选出前 ${rankedCandidates.length} 名候选人`);
    return { 
      rankedCandidates,
      step: 'ranking_completed'
    };
  } catch (error) {
    console.error('❌ 排序失败:', error);
    return { 
      error: `排序失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'ranking_failed'
    };
  }
}

// 生成完整报告 Agent
export async function generateFullReportNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  try {
    console.log('📝 生成完整报告...');
    
    const { rankedCandidates, userSoulProfile } = state;
    if (!rankedCandidates || !userSoulProfile) {
      throw new Error('缺少必要数据');
    }

    // 为首席推荐生成完整报告
    const topCandidate = rankedCandidates[0];
    
    // 生成关系洞察、对话模拟和约会计划
    const [relationshipInsight, conversationSimulation, datePlan] = await Promise.all([
      generateRelationshipInsight(userSoulProfile, topCandidate),
      generateConversationSimulation(userSoulProfile, topCandidate),
      generateDatePlan(userSoulProfile, topCandidate)
    ]);

    const finalMatrix = {
      topMatch: {
        candidate: topCandidate,
        relationshipInsight,
        conversationSimulation,
        datePlan
      },
      potentialMatches: rankedCandidates.slice(1, 5).map(candidate => ({
        candidate,
        highlights: candidate.highlights,
        compatibilityReason: candidate.reasoning
      })),
      generatedAt: new Date(),
      requestId: state.requesterId
    };

    console.log('✅ 完整报告生成完成');
    return { 
      finalMatrix,
      step: 'report_generation_completed'
    };
  } catch (error) {
    console.error('❌ 报告生成失败:', error);
    return { 
      error: `报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'report_generation_failed'
    };
  }
}

// 辅助函数：生成关系洞察
async function generateRelationshipInsight(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<RelationshipInsight> {
  const model = createModel(SYSTEM_PROMPTS.compatibility);
  
  const prompt = PROMPTS.relationshipHighlight
    .replace('{userSoulProfile}', JSON.stringify(userProfile))
    .replace('{candidateAnalysis}', JSON.stringify(candidate));

  const response = await model.invoke([
    { role: 'system', content: SYSTEM_PROMPTS.compatibility },
    { role: 'user', content: prompt }
  ]);

  return JSON.parse(response.content as string);
}

// 辅助函数：生成对话模拟
async function generateConversationSimulation(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<ConversationSimulation> {
  const model = createModel(SYSTEM_PROMPTS.conversation);
  
  const prompt = PROMPTS.conversationSimulation
    .replace('{userProfile}', JSON.stringify(userProfile))
    .replace('{candidateProfile}', JSON.stringify(candidate))
    .replace('{scenario}', '咖啡厅初次见面');

  const response = await model.invoke([
    { role: 'system', content: SYSTEM_PROMPTS.conversation },
    { role: 'user', content: prompt }
  ]);

  return JSON.parse(response.content as string);
}

// 辅助函数：生成约会计划
async function generateDatePlan(userProfile: UserSoulProfile, candidate: CandidateAnalysis): Promise<DatePlan> {
  const model = createModel(SYSTEM_PROMPTS.dating);
  
  // 找出共同兴趣
  const commonInterests = userProfile.coreValues.filter(value => 
    candidate.highlights.some(highlight => highlight.includes(value))
  );
  
  const prompt = PROMPTS.datePlanGeneration
    .replace('{userProfile}', JSON.stringify(userProfile))
    .replace('{candidateProfile}', JSON.stringify(candidate))
    .replace('{commonInterests}', JSON.stringify(commonInterests));

  const response = await model.invoke([
    { role: 'system', content: SYSTEM_PROMPTS.dating },
    { role: 'user', content: prompt }
  ]);

  return JSON.parse(response.content as string);
}
