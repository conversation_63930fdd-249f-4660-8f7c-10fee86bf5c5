// ARAG-Soul 工作流编排器

import { StateGraph, END } from '@langchain/langgraph';
import { db } from '@/lib/db';
import { users, userProfiles } from '@/lib/db/schema';
import { eq, and, ne, inArray } from 'drizzle-orm';
import type { AragSoulState, CandidateMatrix } from './types';
import {
  generateUserSoulProfileNode,
  runCompatibilityInferenceNode,
  rankAndFinalizeNode,
  generateFullReportNode
} from './agents';

// 候选人检索节点
async function retrieveCandidatesNode(state: AragSoulState): Promise<Partial<AragSoulState>> {
  try {
    console.log('🔍 检索候选人池...');
    
    const { requesterId } = state;
    if (!requesterId) {
      throw new Error('缺少请求者ID');
    }

    // 获取用户资料
    const userWithProfile = await db
      .select({
        user: users,
        profile: userProfiles
      })
      .from(users)
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(eq(users.id, requesterId))
      .limit(1);

    if (userWithProfile.length === 0) {
      throw new Error('用户不存在');
    }

    const userProfile = {
      userId: userWithProfile[0].user.id,
      name: userWithProfile[0].user.name,
      age: userWithProfile[0].user.age,
      gender: userWithProfile[0].user.gender,
      interests: userWithProfile[0].user.interests,
      selfDescription: userWithProfile[0].profile?.selfDescription,
      values: userWithProfile[0].profile?.values,
      lifestyle: userWithProfile[0].profile?.lifestyle,
      relationshipGoals: userWithProfile[0].profile?.relationshipGoals
    };

    // 检索候选人池（排除自己，选择异性，活跃用户）
    const candidatePool = await db
      .select({
        id: users.id
      })
      .from(users)
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(
        and(
          ne(users.id, requesterId),
          eq(users.isActive, true),
          // 异性匹配逻辑
          userProfile.gender === 'male' 
            ? eq(users.gender, 'female')
            : eq(users.gender, 'male')
        )
      )
      .limit(50); // 限制候选人池大小

    const candidatePoolIds = candidatePool.map(c => c.id);

    console.log(`✅ 检索到 ${candidatePoolIds.length} 个候选人`);
    return {
      userProfile,
      candidatePoolIds,
      step: 'candidates_retrieved'
    };
  } catch (error) {
    console.error('❌ 候选人检索失败:', error);
    return {
      error: `候选人检索失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'candidates_retrieval_failed'
    };
  }
}

// 条件路由函数
function shouldContinue(state: AragSoulState): string {
  if (state.error) {
    return END;
  }
  
  switch (state.step) {
    case 'candidates_retrieved':
      return 'generateUserSoulProfile';
    case 'personality_insight_completed':
      return 'runCompatibilityInference';
    case 'compatibility_inference_completed':
      return 'rankAndFinalize';
    case 'ranking_completed':
      return 'generateFullReport';
    case 'report_generation_completed':
      return END;
    default:
      return END;
  }
}

// 创建 ARAG-Soul 工作流
export function createAragSoulWorkflow() {
  // 简化的工作流实现，暂时不使用 LangGraph 的复杂特性
  return {
    async invoke(initialState: AragSoulState) {
      let state = { ...initialState };

      try {
        // 1. 检索候选人
        const candidatesResult = await retrieveCandidatesNode(state);
        if (candidatesResult.error) throw new Error(candidatesResult.error);
        state = { ...state, ...candidatesResult };

        // 2. 生成用户灵魂画像
        const soulProfileResult = await generateUserSoulProfileNode(state);
        if (soulProfileResult.error) throw new Error(soulProfileResult.error);
        state = { ...state, ...soulProfileResult };

        // 3. 运行兼容性推理
        const compatibilityResult = await runCompatibilityInferenceNode(state);
        if (compatibilityResult.error) throw new Error(compatibilityResult.error);
        state = { ...state, ...compatibilityResult };

        // 4. 排序和最终决策
        const rankingResult = await rankAndFinalizeNode(state);
        if (rankingResult.error) throw new Error(rankingResult.error);
        state = { ...state, ...rankingResult };

        // 5. 生成完整报告
        const reportResult = await generateFullReportNode(state);
        if (reportResult.error) throw new Error(reportResult.error);
        state = { ...state, ...reportResult };

        return state;
      } catch (error) {
        return {
          ...state,
          error: error instanceof Error ? error.message : '工作流执行失败'
        };
      }
    },

    async *stream(initialState: AragSoulState) {
      let state = { ...initialState };

      try {
        // 1. 检索候选人
        yield { step: 'retrieving_candidates', data: state, timestamp: new Date() };
        const candidatesResult = await retrieveCandidatesNode(state);
        if (candidatesResult.error) throw new Error(candidatesResult.error);
        state = { ...state, ...candidatesResult };

        // 2. 生成用户灵魂画像
        yield { step: 'generating_soul_profile', data: state, timestamp: new Date() };
        const soulProfileResult = await generateUserSoulProfileNode(state);
        if (soulProfileResult.error) throw new Error(soulProfileResult.error);
        state = { ...state, ...soulProfileResult };

        // 3. 运行兼容性推理
        yield { step: 'running_compatibility_inference', data: state, timestamp: new Date() };
        const compatibilityResult = await runCompatibilityInferenceNode(state);
        if (compatibilityResult.error) throw new Error(compatibilityResult.error);
        state = { ...state, ...compatibilityResult };

        // 4. 排序和最终决策
        yield { step: 'ranking_candidates', data: state, timestamp: new Date() };
        const rankingResult = await rankAndFinalizeNode(state);
        if (rankingResult.error) throw new Error(rankingResult.error);
        state = { ...state, ...rankingResult };

        // 5. 生成完整报告
        yield { step: 'generating_report', data: state, timestamp: new Date() };
        const reportResult = await generateFullReportNode(state);
        if (reportResult.error) throw new Error(reportResult.error);
        state = { ...state, ...reportResult };

        yield { step: 'completed', data: state, timestamp: new Date() };
      } catch (error) {
        yield {
          step: 'error',
          error: error instanceof Error ? error.message : '工作流执行失败',
          timestamp: new Date()
        };
      }
    }
  };
}

// ARAG-Soul 服务主入口
export class AragSoulService {
  private workflow: any;

  constructor() {
    this.workflow = createAragSoulWorkflow();
  }

  async generateCandidateMatrix(requesterId: string): Promise<CandidateMatrix> {
    try {
      console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${requesterId}`);
      
      const initialState: AragSoulState = {
        requesterId,
        step: 'initialized'
      };

      // 执行工作流
      const result = await this.workflow.invoke(initialState);

      if (result.error) {
        throw new Error(result.error);
      }

      if (!result.finalMatrix) {
        throw new Error('工作流未生成最终结果');
      }

      console.log('✅ ARAG-Soul 工作流执行完成');
      return result.finalMatrix;
    } catch (error) {
      console.error('❌ ARAG-Soul 工作流执行失败:', error);
      throw error;
    }
  }

  // 流式执行（用于实时监控）
  async *generateCandidateMatrixStream(requesterId: string) {
    const initialState: AragSoulState = {
      requesterId,
      step: 'initialized'
    };

    try {
      for await (const step of this.workflow.stream(initialState)) {
        yield {
          step: step.step,
          data: step,
          timestamp: new Date()
        };
      }
    } catch (error) {
      yield {
        step: 'error',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date()
      };
    }
  }
}

// 导出单例实例
export const aragSoulService = new AragSoulService();
