// ARAG-Soul 工作流编排器

import { StateGraph, START, END, Annotation } from '@langchain/langgraph'; // 暂时不使用
import { db } from '@/lib/db';
import { users, userProfiles } from '@/lib/db/schema';
import { eq, and, ne } from 'drizzle-orm';
import type { CandidateMatrix, UserSoulProfile, CandidateAnalysis } from './types';
import {
  generateUserSoulProfileNode,
  runCompatibilityInferenceNode,
  rankAndFinalizeNode,
  generateFullReportNode
} from './agents';

// 定义 ARAG-Soul 状态注解
export const AragSoulStateAnnotation = Annotation.Root({
  // 输入
  requesterId: Annotation<string>,
  userProfile: Annotation<any>,
  candidatePoolIds: Annotation<string[]>({
    reducer: (x: string[], y: string[]) => y ?? x,
    default: () => []
  }),

  // 中间状态
  userSoulProfile: Annotation<UserSoulProfile | undefined>({
    reducer: (x: UserSoulProfile | undefined, y: UserSoulProfile | undefined) => y ?? x
  }),
  candidatesWithAnalysis: Annotation<CandidateAnalysis[]>({
    reducer: (x: CandidateAnalysis[], y: CandidateAnalysis[]) => y ?? x,
    default: () => []
  }),
  rankedCandidates: Annotation<CandidateAnalysis[]>({
    reducer: (x: CandidateAnalysis[], y: CandidateAnalysis[]) => y ?? x,
    default: () => []
  }),

  // 输出
  finalMatrix: Annotation<CandidateMatrix | undefined>({
    reducer: (x: CandidateMatrix | undefined, y: CandidateMatrix | undefined) => y ?? x
  }),

  // 错误处理
  error: Annotation<string | undefined>({
    reducer: (x: string | undefined, y: string | undefined) => y ?? x
  }),
  step: Annotation<string>({
    reducer: (x: string, y: string) => y ?? x,
    default: () => 'initialized'
  })
});

// 候选人检索节点
async function retrieveCandidatesNode(state: typeof AragSoulStateAnnotation.State): Promise<Partial<typeof AragSoulStateAnnotation.State>> {
  try {
    console.log('🔍 检索候选人池...');
    
    const { requesterId } = state;
    if (!requesterId) {
      throw new Error('缺少请求者ID');
    }

    // 获取用户资料
    const userWithProfile = await db
      .select({
        user: users,
        profile: userProfiles
      })
      .from(users)
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(eq(users.id, requesterId))
      .limit(1);

    if (userWithProfile.length === 0) {
      throw new Error('用户不存在');
    }

    const userProfile = {
      userId: userWithProfile[0].user.id,
      name: userWithProfile[0].user.name,
      age: userWithProfile[0].user.age,
      gender: userWithProfile[0].user.gender,
      interests: userWithProfile[0].user.interests,
      selfDescription: userWithProfile[0].profile?.selfDescription,
      values: userWithProfile[0].profile?.values,
      lifestyle: userWithProfile[0].profile?.lifestyle,
      relationshipGoals: userWithProfile[0].profile?.relationshipGoals
    };

    // 检索候选人池（排除自己，选择异性，活跃用户）
    const candidatePool = await db
      .select({
        id: users.id
      })
      .from(users)
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(
        and(
          ne(users.id, requesterId),
          eq(users.isActive, true),
          // 异性匹配逻辑
          userProfile.gender === 'male' 
            ? eq(users.gender, 'female')
            : eq(users.gender, 'male')
        )
      )
      .limit(50); // 限制候选人池大小

    const candidatePoolIds = candidatePool.map(c => c.id);

    console.log(`✅ 检索到 ${candidatePoolIds.length} 个候选人`);
    return {
      userProfile,
      candidatePoolIds,
      step: 'candidates_retrieved'
    };
  } catch (error) {
    console.error('❌ 候选人检索失败:', error);
    return {
      error: `候选人检索失败: ${error instanceof Error ? error.message : '未知错误'}`,
      step: 'candidates_retrieval_failed'
    };
  }
}

// 条件路由函数
function shouldContinue(state: typeof AragSoulStateAnnotation.State): string {
  if (state.error) {
    return '__end__';
  }

  switch (state.step) {
    case 'candidates_retrieved':
      return 'generateUserSoulProfile';
    case 'personality_insight_completed':
      return 'runCompatibilityInference';
    case 'compatibility_inference_completed':
      return 'rankAndFinalize';
    case 'ranking_completed':
      return 'generateFullReport';
    case 'report_generation_completed':
      return '__end__';
    default:
      return '__end__';
  }
}

// 创建 ARAG-Soul 工作流
export function createAragSoulWorkflow() {
  const workflow = new StateGraph(AragSoulStateAnnotation)
    // 添加节点
    .addNode('retrieveCandidates', retrieveCandidatesNode)
    .addNode('generateUserSoulProfile', generateUserSoulProfileNode)
    .addNode('runCompatibilityInference', runCompatibilityInferenceNode)
    .addNode('rankAndFinalize', rankAndFinalizeNode)
    .addNode('generateFullReport', generateFullReportNode)

    // 设置入口点
    .addEdge(START, 'retrieveCandidates')

    // 添加条件边
    .addConditionalEdges(
      'retrieveCandidates',
      shouldContinue,
      {
        'generateUserSoulProfile': 'generateUserSoulProfile',
        '__end__': END
      }
    )
    .addConditionalEdges(
      'generateUserSoulProfile',
      shouldContinue,
      {
        'runCompatibilityInference': 'runCompatibilityInference',
        '__end__': END
      }
    )
    .addConditionalEdges(
      'runCompatibilityInference',
      shouldContinue,
      {
        'rankAndFinalize': 'rankAndFinalize',
        '__end__': END
      }
    )
    .addConditionalEdges(
      'rankAndFinalize',
      shouldContinue,
      {
        'generateFullReport': 'generateFullReport',
        '__end__': END
      }
    )
    .addConditionalEdges(
      'generateFullReport',
      shouldContinue,
      {
        '__end__': END
      }
    );

  return workflow.compile();
}

// ARAG-Soul 服务主入口
export class AragSoulService {
  private workflow: any;

  constructor() {
    this.workflow = createAragSoulWorkflow();
  }

  async generateCandidateMatrix(requesterId: string): Promise<CandidateMatrix> {
    try {
      console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${requesterId}`);

      const initialState = {
        requesterId,
        step: 'initialized'
      };

      // 执行工作流
      const result = await this.workflow.invoke(initialState);

      if (result.error) {
        throw new Error(result.error);
      }

      if (!result.finalMatrix) {
        throw new Error('工作流未生成最终结果');
      }

      console.log('✅ ARAG-Soul 工作流执行完成');
      return result.finalMatrix;
    } catch (error) {
      console.error('❌ ARAG-Soul 工作流执行失败:', error);
      throw error;
    }
  }

  // 流式执行（用于实时监控）
  async *generateCandidateMatrixStream(requesterId: string) {
    const initialState = {
      requesterId,
      step: 'initialized'
    };

    try {
      for await (const step of this.workflow.stream(initialState)) {
        yield {
          step: step.step || 'processing',
          data: step,
          timestamp: new Date()
        };
      }
    } catch (error) {
      yield {
        step: 'error',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date()
      };
    }
  }
}

// 导出单例实例
export const aragSoulService = new AragSoulService();
