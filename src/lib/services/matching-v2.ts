// V2.0 匹配服务 - 智能候选人矩阵

import { ensureEnvLoaded } from '@/lib/utils/env';
import { db } from '@/lib/db';
import {
  matchQueue,
  matchRequests,
  matchCandidates,
  type NewMatchCandidate
} from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { aragSoulService } from './arag-soul';
import type { CandidateMatrix } from './arag-soul/types';

// 确保环境变量被加载
ensureEnvLoaded();

export class MatchingServiceV2 {
  
  /**
   * 处理队列中的匹配请求
   */
  static async processQueuedRequests(): Promise<{ processedCount: number }> {
    try {
      console.log('🔄 开始处理匹配队列...');

      // 获取待处理的任务
      const pendingTasks = await db
        .select()
        .from(matchQueue)
        .where(eq(matchQueue.status, 'pending'))
        .limit(5); // 一次处理5个任务

      if (pendingTasks.length === 0) {
        console.log('📭 队列为空，无待处理任务');
        return { processedCount: 0 };
      }

      console.log(`📋 发现 ${pendingTasks.length} 个待处理任务`);

      // 并行处理任务
      const processingPromises = pendingTasks.map(task =>
        this.processSingleRequest(task.matchRequestId, task.requesterId)
      );

      const results = await Promise.allSettled(processingPromises);
      const successCount = results.filter(result => result.status === 'fulfilled').length;

      console.log(`✅ 队列处理完成，成功处理 ${successCount}/${pendingTasks.length} 个任务`);
      return { processedCount: successCount };
    } catch (error) {
      console.error('❌ 队列处理失败:', error);
      return { processedCount: 0 };
    }
  }

  /**
   * 处理单个匹配请求
   */
  static async processSingleRequest(requestId: string, requesterId: string): Promise<void> {
    try {
      console.log(`🎯 开始处理请求: ${requestId}`);
      
      // 更新队列状态为处理中
      await db
        .update(matchQueue)
        .set({
          status: 'processing'
          // attempts 字段暂时不更新，需要更复杂的查询
        })
        .where(eq(matchQueue.matchRequestId, requestId));

      // 更新请求状态为处理中
      await db
        .update(matchRequests)
        .set({ status: 'processing' })
        .where(eq(matchRequests.id, requestId));

      // 调用 ARAG-Soul 生成候选人矩阵
      const candidateMatrix = await aragSoulService.generateCandidateMatrix(requesterId);

      // 保存结果到数据库
      await this.saveCandidateMatrix(requestId, candidateMatrix);

      // 更新状态为完成
      await db
        .update(matchRequests)
        .set({
          status: 'completed'
        })
        .where(eq(matchRequests.id, requestId));

      await db
        .update(matchQueue)
        .set({ status: 'completed' })
        .where(eq(matchQueue.matchRequestId, requestId));

      console.log(`✅ 请求处理完成: ${requestId}`);
    } catch (error) {
      console.error(`❌ 请求处理失败: ${requestId}`, error);
      
      // 更新状态为失败
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      await db
        .update(matchRequests)
        .set({ 
          status: 'failed',
          errorMessage
        })
        .where(eq(matchRequests.id, requestId));

      await db
        .update(matchQueue)
        .set({ status: 'failed' })
        .where(eq(matchQueue.matchRequestId, requestId));
    }
  }

  /**
   * 保存候选人矩阵到数据库
   */
  static async saveCandidateMatrix(requestId: string, matrix: CandidateMatrix): Promise<void> {
    try {
      // 保存首席推荐（包含完整数据）
      const topCandidateData: NewMatchCandidate = {
        requestId,
        candidateId: matrix.topMatch.candidate.candidateId,
        rank: 1,
        compatibilityScore: matrix.topMatch.candidate.compatibilityScore,
        reasoning: matrix.topMatch.candidate.reasoning,
        highlights: matrix.topMatch.candidate.highlights,
        challenges: matrix.topMatch.candidate.challenges,
        personalitySummary: matrix.topMatch.candidate.personalitySummary,
        // 首席推荐的额外数据
        relationshipInsight: matrix.topMatch.relationshipInsight,
        conversationSimulation: matrix.topMatch.conversationSimulation,
        datePlan: matrix.topMatch.datePlan,
        userDecision: 'pending'
      };

      await db.insert(matchCandidates).values(topCandidateData);

      // 保存潜力候选人（简化数据）
      const potentialCandidatesData: NewMatchCandidate[] = matrix.potentialMatches.map((match, index) => ({
        requestId,
        candidateId: match.candidate.candidateId,
        rank: index + 2, // 排名从2开始
        compatibilityScore: match.candidate.compatibilityScore,
        reasoning: match.candidate.reasoning,
        highlights: match.candidate.highlights,
        challenges: match.candidate.challenges,
        personalitySummary: match.candidate.personalitySummary,
        // 潜力候选人不需要额外数据
        relationshipInsight: null,
        conversationSimulation: null,
        datePlan: null,
        userDecision: 'pending'
      }));

      if (potentialCandidatesData.length > 0) {
        await db.insert(matchCandidates).values(potentialCandidatesData);
      }

      console.log(`💾 候选人矩阵已保存: ${requestId}`);
    } catch (error) {
      console.error('保存候选人矩阵失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户对候选人的决策
   */
  static async updateCandidateDecision(
    requestId: string, 
    candidateId: string, 
    userId: string,
    decision: 'liked' | 'skipped'
  ): Promise<{ success: boolean; mutualMatch?: boolean }> {
    try {
      // 验证权限
      const request = await db
        .select()
        .from(matchRequests)
        .where(eq(matchRequests.id, requestId))
        .limit(1);

      if (request.length === 0 || request[0].requesterId !== userId) {
        throw new Error('无权限操作此请求');
      }

      // 更新决策
      await db
        .update(matchCandidates)
        .set({ userDecision: decision })
        .where(
          and(
            eq(matchCandidates.requestId, requestId),
            eq(matchCandidates.candidateId, candidateId)
          )
        );

      // 如果是喜欢，检查是否互相喜欢
      let mutualMatch = false;
      if (decision === 'liked') {
        mutualMatch = await this.checkMutualMatch(userId, candidateId);
      }

      return { success: true, mutualMatch };
    } catch (error) {
      console.error('更新候选人决策失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否互相喜欢
   */
  static async checkMutualMatch(userId: string, candidateId: string): Promise<boolean> {
    try {
      // 查找候选人是否也喜欢了用户
      const candidateRequests = await db
        .select({
          request: matchRequests,
          candidate: matchCandidates
        })
        .from(matchRequests)
        .innerJoin(matchCandidates, eq(matchRequests.id, matchCandidates.requestId))
        .where(
          and(
            eq(matchRequests.requesterId, candidateId),
            eq(matchCandidates.candidateId, userId),
            eq(matchCandidates.userDecision, 'liked')
          )
        );

      return candidateRequests.length > 0;
    } catch (error) {
      console.error('检查互相匹配失败:', error);
      return false;
    }
  }

  /**
   * 获取用户的匹配历史
   */
  static async getUserMatchHistory(userId: string, limit: number = 10) {
    try {
      const history = await db
        .select({
          request: matchRequests,
          candidates: matchCandidates
        })
        .from(matchRequests)
        .leftJoin(matchCandidates, eq(matchRequests.id, matchCandidates.requestId))
        .where(eq(matchRequests.requesterId, userId))
        .orderBy(matchRequests.createdAt)
        .limit(limit);

      return history;
    } catch (error) {
      console.error('获取匹配历史失败:', error);
      throw error;
    }
  }

  /**
   * 获取匹配统计信息
   */
  static async getMatchingStats(userId: string) {
    try {
      // 总请求数
      const totalRequests = await db
        .select()
        .from(matchRequests)
        .where(eq(matchRequests.requesterId, userId));

      // 成功的匹配数（至少喜欢一个候选人）
      const successfulMatches = await db
        .select()
        .from(matchRequests)
        .innerJoin(matchCandidates, eq(matchRequests.id, matchCandidates.requestId))
        .where(
          and(
            eq(matchRequests.requesterId, userId),
            eq(matchCandidates.userDecision, 'liked')
          )
        );

      // 互相匹配数
      // 这里需要更复杂的查询，暂时简化
      const mutualMatches = 0; // TODO: 实现互相匹配统计

      return {
        totalRequests: totalRequests.length,
        successfulMatches: successfulMatches.length,
        mutualMatches,
        successRate: totalRequests.length > 0 
          ? (successfulMatches.length / totalRequests.length * 100).toFixed(1)
          : '0'
      };
    } catch (error) {
      console.error('获取匹配统计失败:', error);
      throw error;
    }
  }
}
