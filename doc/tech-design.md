# 寡佬 AI (Gualao AI) - 技术设计文档

**版本：** 1.0
**日期：** 2025年6月26日
**作者：** [你的名字/团队名]

## 1. 引言

本文档旨在根据产品需求文档（prd.md），为“寡佬 AI”项目提供全面的技术设计方案。文档将详细阐述系统架构、数据模型、核心功能实现、技术栈选型以及部署策略，作为后续开发工作的指导蓝图。

**技术栈核心：**
- **AI 模型:** Google Gemini API
- **Web 框架:** Next.js (全栈应用)
- **UI:** shadcn/ui, Tailwind CSS
- **数据存储:** Supabase (PostgreSQL)
- **ORM:** Drizzle ORM
- **部署:** Vercel

## 2. 系统架构

系统将采用基于 Next.js 的单体全栈架构，部署在 Vercel 平台上。这种架构简化了开发和部署流程，非常适合初创项目快速迭代。

```mermaid
graph TD
    subgraph "用户端 (Browser)"
        A[Next.js Frontend]
    end

    subgraph "服务端 (Vercel Serverless Functions)"
        B[Next.js API Routes]
    end

    subgraph "第三方服务"
        C[Supabase]
        D[Google Gemini API]
    end

    A -- HTTP Request --> B;
    B -- SQL (Drizzle ORM) --> C[/"DB (PostgreSQL)"/];
    B -- API Call --> D;
    C -- 数据 --> B;
    D -- AI-Generated Content --> B;
    B -- HTTP Response --> A;

```

**架构优势:**
- **开发效率:** Next.js 全栈开发体验流畅，前后端代码统一管理。
- **性能:** Vercel 提供全球 CDN 加速和优化的 Serverless Functions，确保快速响应。
- **可扩展性:** Supabase 作为后端服务，提供了数据库、认证、存储等功能，可以按需扩展。
- **AI 集成:** Gemini API 可以通过简单的 HTTP 请求在 Next.js 的 API 路由中轻松集成。

## 3. 数据模型 (Data Model)

我们将使用 Drizzle ORM 来定义和操作数据表。Drizzle 提供了类型安全的方式来与我们的 Supabase PostgreSQL 数据库进行交互。

以下是核心数据表的 Drizzle Schema 定义：

```typescript
// /lib/db/schema.ts

import { pgTable, text, timestamp, integer, boolean, jsonb, uuid, varchar } from 'drizzle-orm/pg-core';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  name: text('name'),
  avatar: text('avatar'),
  bio: text('bio'),
  age: integer('age'),
  gender: text('gender'),
  location: text('location'),
  interests: jsonb('interests').$type<string[]>().default([]),
  personalityTraits: jsonb('personality_traits').$type<Record<string, any>>(),
  personalitySummary: text('personality_summary'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// User profiles table for additional profile information
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  selfDescription: text('self_description'),
  lookingFor: text('looking_for'),
  relationshipGoals: text('relationship_goals'),
  lifestyle: jsonb('lifestyle').$type<Record<string, any>>(),
  values: jsonb('values').$type<string[]>().default([]),
  photos: jsonb('photos').$type<string[]>().default([]),
  preferences: jsonb('preferences').$type<Record<string, any>>(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Matches table
export const matches = pgTable('matches', {
  id: uuid('id').primaryKey().defaultRandom(),
  user1Id: uuid('user1_id').references(() => users.id).notNull(),
  user2Id: uuid('user2_id').references(() => users.id).notNull(),
  compatibilityScore: integer('compatibility_score'),
  aiAnalysis: jsonb('ai_analysis').$type<Record<string, any>>(),
  conversationSimulation: jsonb('conversation_simulation').$type<Record<string, any>>(),
  status: text('status').default('pending'), // pending, mutual_like, rejected
  user1Liked: boolean('user1_liked').default(false),
  user2Liked: boolean('user2_liked').default(false),
  user1Viewed: boolean('user1_viewed').default(false),
  user2Viewed: boolean('user2_viewed').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// AI Agent Feedback table
export const aiAgentFeedback = pgTable('ai_agent_feedback', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchId: uuid('match_id').references(() => matches.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  feedbackType: text('feedback_type').notNull(), // 'like', 'dislike', 'accurate', 'inaccurate'
  feedbackText: text('feedback_text'),
  rating: integer('rating'), // 1-5 scale
  aspectRated: text('aspect_rated'), // 'personality', 'conversation', 'compatibility'
  createdAt: timestamp('created_at').defaultNow(),
});

// Conversations table for storing chat messages
export const conversations = pgTable('conversations', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchId: uuid('match_id').references(() => matches.id).notNull(),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Messages table
export const messages = pgTable('messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  conversationId: uuid('conversation_id').references(() => conversations.id).notNull(),
  senderId: uuid('sender_id').references(() => users.id).notNull(),
  content: text('content').notNull(),
  messageType: text('message_type').default('text'), // text, image, etc.
  isRead: boolean('is_read').default(false),
  createdAt: timestamp('created_at').defaultNow(),
});

// User sessions for tracking activity
export const userSessions = pgTable('user_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  sessionToken: text('session_token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});
```

## 4. 核心功能实现

### 4.1 用户个性化资料创建 (Feature 3.1)

- **前端:** 使用 shadcn/ui 的 `Form`, `Input`, `Textarea`, `Select` 组件构建一个多步骤的引导式表单。
- **后端:**
    - 创建一个 API 路由 `/api/profiles/create`。
    - 该接口接收前端表单数据。
    - 使用 Drizzle ORM 将数据插入到 `profiles` 表中。
    - **AI 调用:** 在用户提交自我描述后，可以异步触发一个 AI 调用，初步生成 `personaSummary` 并存储，用于后续快速匹配。

### 4.2 AI 匹配与模拟核心流程 (Feature 3.2 - 3.4)

这是一个核心的后台流程，将通过一系列异步任务和 API 调用完成。

**步骤 1: 发起匹配请求**
- **前端:** 用户在主界面点击“开始新匹配”按钮。
- **后端 (`/api/matches?generate_daily=true`):**
    1.  调用 `MatchingService.generateSingleMatch`。
    2.  **每日限制检查:** 服务内部首先调用 `checkDailyMatchLimit` 检查用户在过去24小时内的匹配次数是否已达上限（例如3次）。如果达到上限，则直接返回错误，提示用户等待。
    3.  **寻找潜在用户:** 调用 `findPotentialMatches`，从数据库中筛选出未曾匹配过的、符合基本条件（如异性）的潜在用户。
    4.  **创建完整匹配:** 如果找到潜在用户，则调用 `createMatch` 方法。

**步骤 2: 创建和分析匹配 (`createMatch` 方法)**
- **后台任务:**
    1.  **获取双方资料:** 并发获取当前用户和潜在匹配对象的完整用户资料（包括 `users` 和 `user_profiles` 表中的信息）。
    2.  **AI 综合分析 (单次调用):** 构建一个包含双方详细资料的 Prompt，一次性调用 `GeminiService` 完成多个任务：
        - 生成双方的**人格摘要**。
        - 计算**兼容性分数**。
        - 模拟一段**对话**。
        - 生成一份全面的**匹配分析报告**（包括优点、潜在挑战、建议）。
        - 创造一个**约会计划**。
    3.  **存储结果:**
        - 将AI返回的所有结果（兼容性分数、分析报告、模拟对话等）打包存入 `matches` 表的一条新记录中。
        - 初始 `status` 为 `pending`。
    4.  **返回新匹配:** 向前端返回成功创建的匹配数据。

### 4.3 匹配回顾与反馈 (Feature 3.5, 3.7)

- **前端:**
    - 创建一个页面 `/[matchId]` 用于展示匹配详情。
    - 从 `/api/matches/[matchId]` 获取完整的匹配数据，包括AI分析、模拟对话等。
    - 渲染对话记录和AI分析报告。
    - 提供一个反馈组件，允许用户对本次匹配的特定方面（如性格分析、对话真实性）进行评价，例如通过打分（1-5星）或选择标签（如“喜欢”、“不喜欢”、“准确”、“不准确”）。
- **后端:**
    - **`/api/matches/[matchId]`:** 提供指定匹配记录的详细信息。
    - **`/api/feedback` (或类似路由):**
        - 当用户提交反馈时，前端将请求发送到此接口。
        - 接口接收 `matchId`, `userId`, `feedbackType`, `rating`, `aspectRated` 等数据。
        - 使用 Drizzle ORM 将这条反馈记录存入 `ai_agent_feedback` 表。这些数据未来可用于微调AI模型或优化匹配算法。

### 4.4 认证流程 (Authentication Flow)

- **服务提供商:** 使用 Supabase Auth 作为认证服务。它提供了完整的用户管理功能，包括基于邮箱/密码的注册、登录、密码重置以及 OAuth 第三方登录（如 Google, GitHub 等）。
- **前端实现:**
    - 使用 Supabase 官方的 `@supabase/auth-helpers-nextjs` 库，它可以与 Next.js 的服务端组件和 API 路由无缝集成。
    - 创建登录、注册页面，并使用 Supabase Auth 提供的 UI 组件或自定义表单来处理用户输入。
    - 登录成功后，Supabase Auth 会自动处理 JWT (JSON Web Token) 的存储和刷新。
- **后端 (API 路由) 实现:**
    - 在每个需要认证的 API 路由中，使用 `createRouteHandlerClient` (from `@supabase/auth-helpers-nextjs`) 来获取当前登录的用户信息。
    - 这会安全地从 cookie 中读取 JWT，并在服务端验证它。如果验证通过，你将能获取到用户的 `session` 和 `user` 对象。
    - **首次登录同步:** 当一个新用户通过 Supabase Auth 注册时，监听其 `onAuthStateChange` 事件或使用数据库触发器，在我们的 `users` 和 `profiles` 表中创建相应的记录，将 Supabase Auth 的用户 ID 存入 `authId` 字段。

### 4.5 前端状态管理 (Frontend State Management)

- **问题:** 随着应用功能的增多（例如，需要全局维护用户的登录状态、个人资料、当前的匹配信息等），仅靠 React Context 和 props 传递会变得非常繁琐（prop-drilling）。
- **选型建议:**
    - **Zustand** 或 **Jotai**: 推荐使用这些轻量级、现代的状态管理库。它们 API 简洁，学习曲线平缓，并且与 Next.js 的 App Router 模式兼容得很好。
    - **使用场景:**
        - 创建一个 `userStore` 来全局存储和访问用户的认证状态和基本资料。
        - 创建一个 `matchStore` 来管理当前匹配请求的状态、回顾数据等。
- **优势:** 避免了不必要的组件重渲染，并使状态逻辑与 UI 组件解耦，提高了代码的可维护性。

## 5. 部署与运维

- **部署平台:** Vercel
- **流程:**
    1. 将 Next.js 项目与 Vercel 账户关联。
    2. 在 Vercel 项目设置中，添加环境变量，用于连接 Supabase 数据库和存储 Gemini API 密钥。
        - `DATABASE_URL`
        - `GEMINI_API_KEY`
    3. 每当代码推送到主分支（`main` 或 `master`）时，Vercel 会自动构建和部署应用。
- **数据库迁移:** 使用 `drizzle-kit` 生成 SQL 迁移文件，并通过 Supabase 的数据库管理界面或 CLI 应用这些迁移。
- **定时任务:** 对于后台匹配流程，可以考虑使用 Vercel Cron Jobs 来定期触发 `/api/matching/run` 接口，处理待匹配队列。

## 6. 安全考虑

- **API 密钥管理:** 绝不在前端代码中暴露 `GEMINI_API_KEY`。所有对 Gemini 的调用都必须通过 Next.js 的 API Routes (后端) 进行。
- **数据访问控制:** 在所有 API 路由中，实施严格的身份验证和授权逻辑。确保用户只能访问和修改自己的数据。例如，在 `/api/profiles/update` 中，要校验当前登录用户的 ID 是否与要修改的 profile 的 `userId` 匹配。
- **Supabase RLS (Row Level Security):** 开启并配置 Supabase 的行级安全策略，作为数据库层面的最后一道防线，确保即使 API 逻辑有漏洞，用户也无法访问到不属于他们的数据。

## 7. 未来展望与扩展性

- **向量搜索:** 为了更精准地匹配用户，可以考虑将用户的 `selfDescription` 和 `endorsements` 文本通过 Gemini Embedding API 转换为向量，并存储在 Supabase 的 `pgvector` 扩展中。在寻找潜在匹配对象时，使用向量相似度搜索可以大大提高匹配质量。
- **缓存:** 对于不经常变动的用户资料和人格摘要，可以使用 Redis 或 Vercel Data Cache 进行缓存，减少数据库读取和 AI API 调用次数，降低成本和延迟。
- **WebSocket:** 为了实现更实时的通知和交互，未来可以引入 WebSocket 服务（例如，使用 aiven.io 或自建服务），用于推送匹配状态更新。

## 8. 错误处理与日志记录 (Error Handling & Logging)

- **API 错误处理:**
    - **统一响应格式:** 在所有 API 路由中，定义一个统一的错误响应格式。例如，使用 `try...catch` 块，并在 catch 中返回一个标准的 JSON 对象：
      ```json
      {
        "error": {
          "message": "错误信息描述",
          "code": "INTERNAL_SERVER_ERROR" // 或其他自定义错误码
        }
      }
      ```
    - **HTTP 状态码:** 使用恰当的 HTTP 状态码（如 400, 401, 403, 404, 500）来表示不同类型的错误。
- **前端错误处理:**
    - **用户友好提示:** 在前端 `fetch` 请求的 `.catch` 块中，解析后端返回的错误信息，并使用 shadcn/ui 的 `Toast` 或 `Alert` 组件向用户显示清晰、友好的错误提示，而不是直接暴露技术细节。
    - **React Error Boundaries:** 对于渲染期间的异常，使用 React 的 Error Boundaries 组件来捕获错误，防止整个应用崩溃，并可以展示一个备用的 UI。
- **日志记录:**
    - **Vercel Logs:** Vercel 平台会自动收集所有 Serverless Function (API Routes) 的标准输出和错误日志，方便快速排查问题。
    - **第三方日志服务 (可选):** 对于更复杂的生产环境应用，可以考虑集成专业的日志管理服务，如 Sentry (同时提供错误监控), Logtail 或 Better Stack。这些服务提供了更强大的日志搜索、分析和告警功能。

## 9. 测试策略 (Testing Strategy)

为了确保应用的质量和稳定性，应采用分层的测试策略。

- **单元测试 (Unit Tests):**
    - **工具:** Jest, React Testing Library。
    - **范围:** 测试独立的 React 组件、工具函数、Drizzle 的 schema 定义和简单的业务逻辑。
    - **示例:**
        - 测试一个按钮组件在点击后是否正确触发了回调。
        - 测试一个数据处理函数是否返回了预期的结果。
- **集成测试 (Integration Tests):**
    - **工具:** Jest, Supertest。
    - **范围:** 测试多个模块协同工作的场景，特别是 API 路由。
    - **示例:**
        - 模拟一个 HTTP 请求到 `/api/profiles/create`，并验证数据库中是否成功创建了新的记录。可以配合一个测试专用的数据库实例或使用 `docker-compose` 启动一个临时的数据库。
- **端到端测试 (End-to-End Tests):**
    - **工具:** Playwright (推荐) 或 Cypress。
    - **范围:** 模拟真实用户在浏览器中的完整操作流程。
    - **示例:**
        - 编写一个脚本，自动完成从用户注册、登录、填写资料、发起匹配、到最终看到匹配回顾页面的整个流程。
- **CI/CD 集成:**
    - 将测试命令（如 `npm test`）集成到 GitHub Actions 或其他 CI/CD 工具中。
    - 设置规则，要求在代码合并到主分支之前，所有测试必须通过，从而实现自动化质量控制。
