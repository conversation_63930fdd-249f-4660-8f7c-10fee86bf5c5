# 寡佬 AI v2.0 部署指南

## 🎯 版本概述

寡佬 AI v2.0 引入了革命性的 **ARAG-Soul 框架** 和 **智能候选人矩阵**，将单一匹配升级为一次性提供 5 名精心排序的候选人推荐。

### 🆕 新功能特性

1. **ARAG-Soul 多智能体框架**
   - 人格洞察 Agent：深度分析用户灵魂画像
   - 兼容性推理 Agent：智能评估匹配度
   - 关系亮点提炼 Agent：发现关系优势
   - 红娘决策排序 Agent：综合排序候选人

2. **智能候选人矩阵**
   - 1 个首席推荐（完整报告）
   - 4 个潜力候选人（简化报告）
   - 分层展示，突出重点

3. **异步处理架构**
   - 任务队列系统
   - 后台异步生成
   - 实时状态更新

4. **升级的用户界面**
   - 候选人矩阵展示
   - 交互式详情查看
   - 决策跟踪系统

## 📋 部署前准备

### 1. 环境要求

- Node.js 18+
- PostgreSQL 数据库（Supabase）
- OpenRouter API 密钥

### 2. 新增环境变量

在 `.env.local` 中添加：

```env
# 现有变量保持不变
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENROUTER_API_KEY=your_openrouter_api_key
DATABASE_URL=your_database_url

# V2.0 新增变量
WORKER_SECRET_TOKEN=your_worker_secret_token
NEXT_APP_URL=https://your-app.vercel.app
```

### 3. 数据库迁移

```bash
# 生成新的迁移文件
npm run db:generate

# 应用迁移
npm run db:migrate
```

新增的数据表：
- `match_queue` - 异步任务队列
- `match_requests` - 匹配请求记录
- `match_candidates` - 候选人决策记录

## 🚀 部署步骤

### 1. 代码部署

```bash
# 检查类型
npm run type-check

# 构建项目
npm run build

# 部署到 Vercel
npm run deploy
```

### 2. Supabase 配置

#### 创建 Edge Function

```bash
# 安装 Supabase CLI
npm install -g supabase

# 登录 Supabase
supabase login

# 部署 Edge Function
supabase functions deploy process-match-queue
```

#### 设置 Cron Job

在 Supabase Dashboard 中创建 Cron Job：

```sql
-- 每分钟执行一次队列处理
SELECT cron.schedule(
  'process-match-queue',
  '* * * * *',
  'SELECT net.http_post(
    url := ''https://your-project.supabase.co/functions/v1/process-match-queue'',
    headers := ''{"Authorization": "Bearer YOUR_ANON_KEY"}''::jsonb
  );'
);
```

### 3. 验证部署

```bash
# 测试 ARAG-Soul 框架
npm run test:arag-soul

# 测试队列处理
npm run queue:process

# 健康检查
curl https://your-app.vercel.app/api/health
```

## 🔧 API 端点

### V2.0 新增端点

- `POST /api/matches/matrix` - 生成候选人矩阵
- `GET /api/matches/matrix?requestId=xxx` - 获取矩阵结果
- `PATCH /api/matches/matrix/{requestId}/candidates/{candidateId}` - 候选人决策
- `POST /api/worker/process-queue` - 队列处理（内部）

### 页面路由

- `/matches/matrix/{requestId}` - 候选人矩阵展示页面

## 🎨 用户体验流程

1. **发起请求**：用户点击"生成候选人矩阵"
2. **异步处理**：后台 ARAG-Soul 框架工作
3. **实时反馈**：进度条显示处理状态
4. **结果展示**：候选人矩阵页面
5. **交互决策**：用户对每个候选人做出决策

## 🔍 监控与调试

### 日志查看

```bash
# Vercel 函数日志
vercel logs

# Supabase Edge Function 日志
supabase functions logs process-match-queue
```

### 性能监控

- API 响应时间
- 队列处理效率
- AI 生成成功率
- 用户决策转化率

## 🛠️ 故障排除

### 常见问题

1. **队列处理失败**
   - 检查 OPENROUTER_API_KEY 是否有效
   - 验证数据库连接
   - 查看 Edge Function 日志

2. **AI 生成超时**
   - 增加函数超时时间
   - 优化 Prompt 长度
   - 检查 API 配额

3. **前端显示异常**
   - 检查 API 响应格式
   - 验证类型定义
   - 查看浏览器控制台

### 回滚方案

如果 v2.0 出现问题，可以：

1. 禁用 v2.0 功能按钮
2. 保持 v1.0 功能正常运行
3. 修复问题后重新启用

## 📊 性能优化

### 建议配置

- **Vercel 函数超时**：AI 生成 60s，其他 30s
- **数据库连接池**：最大 20 连接
- **缓存策略**：用户资料缓存 1 小时

### 扩展性考虑

- 候选人池大小：当前 50 人，可根据用户增长调整
- 并发处理：支持多个匹配请求同时处理
- 队列容量：无限制，但建议监控队列长度

## 🎉 部署完成

部署成功后，用户将体验到：

- 更丰富的匹配选择
- 更深度的 AI 分析
- 更流畅的交互体验
- 更高的匹配成功率

恭喜！寡佬 AI v2.0 已成功部署！🚀
