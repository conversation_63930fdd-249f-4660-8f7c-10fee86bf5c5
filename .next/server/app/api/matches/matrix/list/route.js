/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/matches/matrix/list/route";
exports.ids = ["app/api/matches/matrix/list/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_list_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/matches/matrix/list/route.ts */ \"(rsc)/./src/app/api/matches/matrix/list/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/matches/matrix/list/route\",\n        pathname: \"/api/matches/matrix/list\",\n        filename: \"route\",\n        bundlePath: \"app/api/matches/matrix/list/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/list/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_list_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/matches/matrix/list/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/matches/matrix/list/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/sql.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n// 获取用户的匹配矩阵列表 API\n\n\n\n\n\n// 获取用户的匹配矩阵列表\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 获取用户的所有匹配请求，并统计候选人数量\n        const matrices = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id,\n            status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.status,\n            createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.createdAt,\n            candidatesCount: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.sql)`count(${_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.id})`\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.requesterId, user.id)).groupBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.status, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.createdAt).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.sql)`${_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.createdAt} DESC`);\n        // 格式化返回数据\n        const formattedMatrices = matrices.map((matrix)=>({\n                id: matrix.id,\n                status: matrix.status,\n                createdAt: matrix.createdAt?.toISOString(),\n                candidatesCount: Number(matrix.candidatesCount) || 0\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            matrices: formattedMatrices,\n            count: formattedMatrices.length\n        });\n    } catch (error) {\n        console.error('获取匹配矩阵列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: error instanceof Error ? error.message : '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/matches/matrix/list/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matchCandidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchCandidates),\n/* harmony export */   matchQueue: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchQueue),\n/* harmony export */   matchRequests: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchRequests),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n\n\n\n\n// 确保环境变量被加载\n(0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n// Create the connection\nconst connectionString = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getOptionalEnv)('DATABASE_URL');\nlet db;\nif (connectionString) {\n    // Create postgres client with better configuration for Supabase\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10,\n        ssl: 'require',\n        prepare: false\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matchCandidates: () => (/* binding */ matchCandidates),\n/* harmony export */   matchQueue: () => (/* binding */ matchQueue),\n/* harmony export */   matchRequests: () => (/* binding */ matchRequests),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// ===== V2.0 新增表 =====\n// 1. 任务队列，用于异步处理匹配请求\nconst matchQueue = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_queue', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchRequestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_request_id').notNull().unique(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    attempts: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('attempts').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 2. 匹配请求的总记录\nconst matchRequests = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_requests', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('processing'),\n    errorMessage: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('error_message'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 3. 用于记录用户对每个候选人的决策和详细分析\nconst matchCandidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('request_id').references(()=>matchRequests.id).notNull(),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('candidate_id').references(()=>users.id).notNull(),\n    rank: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rank').notNull(),\n    // 兼容性分析数据\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score').notNull().default(0),\n    reasoning: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reasoning'),\n    highlights: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('highlights'),\n    challenges: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('challenges'),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    // 首席推荐的额外数据 (rank = 1 时才有)\n    relationshipInsight: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('relationship_insight'),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation'),\n    datePlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('date_plan'),\n    // 用户决策\n    userDecision: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('user_decision').default('pending'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createRouteClient: () => (/* binding */ createRouteClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nconst createClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\nconst createRouteClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/env.ts":
/*!******************************!*\
  !*** ./src/lib/utils/env.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureEnvLoaded: () => (/* binding */ ensureEnvLoaded),\n/* harmony export */   getOptionalEnv: () => (/* binding */ getOptionalEnv),\n/* harmony export */   getRequiredEnv: () => (/* binding */ getRequiredEnv),\n/* harmony export */   validateRequiredEnvVars: () => (/* binding */ validateRequiredEnvVars)\n/* harmony export */ });\n// 环境变量加载工具\nlet envLoaded = false;\nfunction ensureEnvLoaded() {\n    if (envLoaded || \"undefined\" !== 'undefined') {\n        return;\n    }\n    try {\n        // 尝试加载 .env 文件\n        (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n            path: '.env'\n        });\n        envLoaded = true;\n        console.log('✅ 环境变量已加载');\n    } catch (error) {\n        // dotenv 可能不存在，尝试其他路径\n        try {\n            (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n                path: '.env.local'\n            });\n            envLoaded = true;\n            console.log('✅ 环境变量已从 .env.local 加载');\n        } catch (error2) {\n            console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');\n        }\n    }\n}\nfunction getRequiredEnv(key) {\n    ensureEnvLoaded();\n    const value = process.env[key];\n    if (!value) {\n        throw new Error(`环境变量 ${key} 未设置`);\n    }\n    return value;\n}\nfunction getOptionalEnv(key, defaultValue) {\n    ensureEnvLoaded();\n    return process.env[key] || defaultValue;\n}\n// 验证所有必需的环境变量\nfunction validateRequiredEnvVars() {\n    ensureEnvLoaded();\n    const required = [\n        'DATABASE_URL',\n        'OPENROUTER_API_KEY',\n        'NEXT_PUBLIC_SUPABASE_URL',\n        'NEXT_PUBLIC_SUPABASE_ANON_KEY'\n    ];\n    const missing = required.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);\n    }\n    console.log('✅ 所有必需的环境变量都已设置');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL2Vudi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsV0FBVztBQUVYLElBQUlBLFlBQVk7QUFFVCxTQUFTQztJQUNkLElBQUlELGFBQWEsZ0JBQWtCLGFBQWE7UUFDOUM7SUFDRjtJQUVBLElBQUk7UUFDRixlQUFlO1FBQ2ZFLHFGQUF3QixDQUFDO1lBQUVFLE1BQU07UUFBTztRQUN4Q0osWUFBWTtRQUNaSyxRQUFRQyxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9DLE9BQU87UUFDZCxzQkFBc0I7UUFDdEIsSUFBSTtZQUNGTCxxRkFBd0IsQ0FBQztnQkFBRUUsTUFBTTtZQUFhO1lBQzlDSixZQUFZO1lBQ1pLLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT0UsUUFBUTtZQUNmSCxRQUFRSSxJQUFJLENBQUM7UUFDZjtJQUNGO0FBQ0Y7QUFFTyxTQUFTQyxlQUFlQyxHQUFXO0lBQ3hDVjtJQUVBLE1BQU1XLFFBQVFDLFFBQVFDLEdBQUcsQ0FBQ0gsSUFBSTtJQUM5QixJQUFJLENBQUNDLE9BQU87UUFDVixNQUFNLElBQUlHLE1BQU0sQ0FBQyxLQUFLLEVBQUVKLElBQUksSUFBSSxDQUFDO0lBQ25DO0lBQ0EsT0FBT0M7QUFDVDtBQUVPLFNBQVNJLGVBQWVMLEdBQVcsRUFBRU0sWUFBcUI7SUFDL0RoQjtJQUVBLE9BQU9ZLFFBQVFDLEdBQUcsQ0FBQ0gsSUFBSSxJQUFJTTtBQUM3QjtBQUVBLGNBQWM7QUFDUCxTQUFTQztJQUNkakI7SUFFQSxNQUFNa0IsV0FBVztRQUNmO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCxNQUFNQyxVQUFVRCxTQUFTRSxNQUFNLENBQUNWLENBQUFBLE1BQU8sQ0FBQ0UsUUFBUUMsR0FBRyxDQUFDSCxJQUFJO0lBRXhELElBQUlTLFFBQVFFLE1BQU0sR0FBRyxHQUFHO1FBQ3RCLE1BQU0sSUFBSVAsTUFBTSxDQUFDLFdBQVcsRUFBRUssUUFBUUcsSUFBSSxDQUFDLE9BQU87SUFDcEQ7SUFFQWxCLFFBQVFDLEdBQUcsQ0FBQztBQUNkIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvdXRpbHMvZW52LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOeOr+Wig+WPmOmHj+WKoOi9veW3peWFt1xuXG5sZXQgZW52TG9hZGVkID0gZmFsc2U7XG5cbmV4cG9ydCBmdW5jdGlvbiBlbnN1cmVFbnZMb2FkZWQoKSB7XG4gIGlmIChlbnZMb2FkZWQgfHwgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm47XG4gIH1cblxuICB0cnkge1xuICAgIC8vIOWwneivleWKoOi9vSAuZW52IOaWh+S7tlxuICAgIHJlcXVpcmUoJ2RvdGVudicpLmNvbmZpZyh7IHBhdGg6ICcuZW52JyB9KTtcbiAgICBlbnZMb2FkZWQgPSB0cnVlO1xuICAgIGNvbnNvbGUubG9nKCfinIUg546v5aKD5Y+Y6YeP5bey5Yqg6L29Jyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gZG90ZW52IOWPr+iDveS4jeWtmOWcqO+8jOWwneivleWFtuS7lui3r+W+hFxuICAgIHRyeSB7XG4gICAgICByZXF1aXJlKCdkb3RlbnYnKS5jb25maWcoeyBwYXRoOiAnLmVudi5sb2NhbCcgfSk7XG4gICAgICBlbnZMb2FkZWQgPSB0cnVlO1xuICAgICAgY29uc29sZS5sb2coJ+KchSDnjq/looPlj5jph4/lt7Lku44gLmVudi5sb2NhbCDliqDovb0nKTtcbiAgICB9IGNhdGNoIChlcnJvcjIpIHtcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIOaXoOazleWKoOi9vSBkb3RlbnbvvIzkvb/nlKjns7vnu5/njq/looPlj5jph48nKTtcbiAgICB9XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFJlcXVpcmVkRW52KGtleTogc3RyaW5nKTogc3RyaW5nIHtcbiAgZW5zdXJlRW52TG9hZGVkKCk7XG4gIFxuICBjb25zdCB2YWx1ZSA9IHByb2Nlc3MuZW52W2tleV07XG4gIGlmICghdmFsdWUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYOeOr+Wig+WPmOmHjyAke2tleX0g5pyq6K6+572uYCk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0T3B0aW9uYWxFbnYoa2V5OiBzdHJpbmcsIGRlZmF1bHRWYWx1ZT86IHN0cmluZyk6IHN0cmluZyB8IHVuZGVmaW5lZCB7XG4gIGVuc3VyZUVudkxvYWRlZCgpO1xuICBcbiAgcmV0dXJuIHByb2Nlc3MuZW52W2tleV0gfHwgZGVmYXVsdFZhbHVlO1xufVxuXG4vLyDpqozor4HmiYDmnInlv4XpnIDnmoTnjq/looPlj5jph49cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVJlcXVpcmVkRW52VmFycygpIHtcbiAgZW5zdXJlRW52TG9hZGVkKCk7XG4gIFxuICBjb25zdCByZXF1aXJlZCA9IFtcbiAgICAnREFUQUJBU0VfVVJMJyxcbiAgICAnT1BFTlJPVVRFUl9BUElfS0VZJyxcbiAgICAnTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMJyxcbiAgICAnTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVknXG4gIF07XG5cbiAgY29uc3QgbWlzc2luZyA9IHJlcXVpcmVkLmZpbHRlcihrZXkgPT4gIXByb2Nlc3MuZW52W2tleV0pO1xuICBcbiAgaWYgKG1pc3NpbmcubGVuZ3RoID4gMCkge1xuICAgIHRocm93IG5ldyBFcnJvcihg57y65bCR5b+F6ZyA55qE546v5aKD5Y+Y6YePOiAke21pc3Npbmcuam9pbignLCAnKX1gKTtcbiAgfVxuICBcbiAgY29uc29sZS5sb2coJ+KchSDmiYDmnInlv4XpnIDnmoTnjq/looPlj5jph4/pg73lt7Lorr7nva4nKTtcbn1cbiJdLCJuYW1lcyI6WyJlbnZMb2FkZWQiLCJlbnN1cmVFbnZMb2FkZWQiLCJyZXF1aXJlIiwiY29uZmlnIiwicGF0aCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImVycm9yMiIsIndhcm4iLCJnZXRSZXF1aXJlZEVudiIsImtleSIsInZhbHVlIiwicHJvY2VzcyIsImVudiIsIkVycm9yIiwiZ2V0T3B0aW9uYWxFbnYiLCJkZWZhdWx0VmFsdWUiLCJ2YWxpZGF0ZVJlcXVpcmVkRW52VmFycyIsInJlcXVpcmVkIiwibWlzc2luZyIsImZpbHRlciIsImxlbmd0aCIsImpvaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/env.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/drizzle-orm","vendor-chunks/postgres","vendor-chunks/dotenv"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Flist%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();