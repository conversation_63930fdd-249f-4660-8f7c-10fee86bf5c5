/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/matches/matrix/route";
exports.ids = ["app/api/matches/matrix/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/matches/matrix/route.ts */ \"(rsc)/./src/app/api/matches/matrix/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/matches/matrix/route\",\n        pathname: \"/api/matches/matrix\",\n        filename: \"route\",\n        bundlePath: \"app/api/matches/matrix/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/matches/matrix/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/matches/matrix/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/matching-v2 */ \"(rsc)/./src/lib/services/matching-v2.ts\");\n// V2.0 智能候选人矩阵 API\n\n\n\n\n\n\n\n// 异步处理匹配请求\nasync function processMatchRequestAsync(requestId, requesterId) {\n    try {\n        console.log(`🚀 开始异步处理匹配请求: ${requestId}`);\n        await _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__.MatchingServiceV2.processSingleRequest(requestId, requesterId);\n        console.log(`✅ 异步处理完成: ${requestId}`);\n    } catch (error) {\n        console.error(`❌ 异步处理失败: ${requestId}`, error);\n    }\n}\n// 生成智能候选人矩阵请求\nasync function POST(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 检查每日限制（复用 v1.0 的限制逻辑）\n        const { MatchingService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_services_matching_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/matching */ \"(rsc)/./src/lib/services/matching.ts\"));\n        const limitCheck = await MatchingService.checkDailyMatchLimit(user.id);\n        if (!limitCheck.canMatch) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'DAILY_LIMIT_EXCEEDED',\n                message: '今日匹配次数已用完',\n                limitInfo: limitCheck\n            }, {\n                status: 429\n            });\n        }\n        // 生成请求ID\n        const requestId = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        // 创建匹配请求记录\n        const matchRequestData = {\n            id: requestId,\n            requesterId: user.id,\n            status: 'processing'\n        };\n        const [matchRequest] = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).values(matchRequestData).returning();\n        // 创建任务队列记录\n        const queueData = {\n            matchRequestId: requestId,\n            requesterId: user.id,\n            status: 'pending'\n        };\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchQueue).values(queueData);\n        console.log(`📝 创建匹配矩阵请求: ${requestId} for user: ${user.id}`);\n        // 立即触发异步处理\n        processMatchRequestAsync(requestId, user.id).catch((error)=>{\n            console.error(`异步处理失败 ${requestId}:`, error);\n        });\n        // 立即返回请求ID，实际处理将异步进行\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            requestId: requestId,\n            status: 'processing',\n            message: '专属红娘正在为您筛选候选人，请稍候...',\n            estimatedTime: '2-3分钟'\n        }, {\n            status: 202\n        });\n    } catch (error) {\n        console.error('创建匹配矩阵请求失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 获取匹配矩阵结果\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const url = new URL(request.url);\n        const requestId = url.searchParams.get('requestId');\n        if (!requestId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'BAD_REQUEST',\n                message: '缺少 requestId 参数'\n            }, {\n                status: 400\n            });\n        }\n        // 查询匹配请求状态\n        const matchRequest = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id, requestId)).limit(1);\n        if (matchRequest.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'NOT_FOUND',\n                message: '匹配请求不存在'\n            }, {\n                status: 404\n            });\n        }\n        const request_data = matchRequest[0];\n        // 检查权限\n        if (request_data.requesterId !== user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'FORBIDDEN',\n                message: '无权访问此匹配请求'\n            }, {\n                status: 403\n            });\n        }\n        // 根据状态返回不同响应\n        switch(request_data.status){\n            case 'processing':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'processing',\n                    message: '正在生成匹配矩阵，请稍候...',\n                    requestId: requestId\n                });\n            case 'completed':\n                if (!request_data.finalReport) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'DATA_ERROR',\n                        message: '匹配结果数据异常'\n                    }, {\n                        status: 500\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'completed',\n                    requestId: requestId,\n                    matrix: request_data.finalReport,\n                    generatedAt: request_data.createdAt\n                });\n            case 'failed':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'failed',\n                    message: request_data.errorMessage || '匹配生成失败',\n                    requestId: requestId\n                }, {\n                    status: 500\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'UNKNOWN_STATUS',\n                    message: '未知的请求状态'\n                }, {\n                    status: 500\n                });\n        }\n    } catch (error) {\n        console.error('获取匹配矩阵结果失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/matches/matrix/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matchCandidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchCandidates),\n/* harmony export */   matchQueue: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchQueue),\n/* harmony export */   matchRequests: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchRequests),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n\n\n\n\n// 确保环境变量被加载\n(0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n// Create the connection\nconst connectionString = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getOptionalEnv)('DATABASE_URL');\nlet db;\nif (connectionString) {\n    // Create postgres client with better configuration for Supabase\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10,\n        ssl: 'require',\n        prepare: false\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matchCandidates: () => (/* binding */ matchCandidates),\n/* harmony export */   matchQueue: () => (/* binding */ matchQueue),\n/* harmony export */   matchRequests: () => (/* binding */ matchRequests),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// ===== V2.0 新增表 =====\n// 1. 任务队列，用于异步处理匹配请求\nconst matchQueue = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_queue', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchRequestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_request_id').notNull().unique(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    attempts: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('attempts').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 2. 匹配请求的总记录\nconst matchRequests = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_requests', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('processing'),\n    finalReport: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('final_report'),\n    errorMessage: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('error_message'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 3. 用于记录用户对每个候选人的决策\nconst matchCandidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('request_id').references(()=>matchRequests.id).notNull(),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('candidate_id').references(()=>users.id).notNull(),\n    rank: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rank').notNull(),\n    userDecision: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('user_decision').default('pending'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/agents.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/arag-soul/agents.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateFullReportNode: () => (/* binding */ generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* binding */ generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* binding */ rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* binding */ runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n// ARAG-Soul 框架的 Agent 实现\n\n\n\n// 模型配置\nconst MODEL_NAME = 'google/gemini-2.5-flash-preview-05-20';\n// 创建 AI 调用函数\nconst createModelCall = async (prompt, systemPrompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.default)=>{\n    // 确保环境变量被加载并获取 API 密钥\n    (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n    const apiKey = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getRequiredEnv)('OPENROUTER_API_KEY');\n    // 初始化 OpenRouter 客户端\n    const openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        baseURL: 'https://openrouter.ai/api/v1',\n        apiKey: apiKey\n    });\n    try {\n        const completion = await openrouter.chat.completions.create({\n            model: MODEL_NAME,\n            messages: [\n                {\n                    role: 'system',\n                    content: systemPrompt\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2048,\n            response_format: {\n                type: 'json_object'\n            }\n        });\n        return completion.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error('OpenRouter API 调用失败:', error);\n        throw error;\n    }\n};\n// 人格洞察 Agent\nasync function generateUserSoulProfileNode(state) {\n    try {\n        console.log('🧠 执行人格洞察 Agent...');\n        const { userProfile } = state;\n        if (!userProfile) {\n            throw new Error('用户资料不存在');\n        }\n        const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.personalityInsight.replace('{name}', userProfile.name || '未知').replace('{age}', userProfile.age?.toString() || '未知').replace('{gender}', userProfile.gender || '未知').replace('{selfDescription}', userProfile.selfDescription || '').replace('{interests}', JSON.stringify(userProfile.interests || [])).replace('{values}', JSON.stringify(userProfile.values || [])).replace('{lifestyle}', JSON.stringify(userProfile.lifestyle || {})).replace('{relationshipGoals}', userProfile.relationshipGoals || '');\n        const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.personality);\n        const result = JSON.parse(responseContent);\n        const userSoulProfile = {\n            userId: userProfile.userId,\n            personalityTraits: result.personalityTraits,\n            coreValues: result.coreValues,\n            communicationStyle: result.communicationStyle,\n            relationshipGoals: userProfile.relationshipGoals,\n            lifestyle: userProfile.lifestyle,\n            summary: result.summary\n        };\n        console.log('✅ 人格洞察完成');\n        // 打印当前状态\n        console.log('当前状态:', state);\n        return {\n            userSoulProfile,\n            step: 'personality_insight_completed'\n        };\n    } catch (error) {\n        console.error('❌ 人格洞察失败:', error);\n        return {\n            error: `人格洞察失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'personality_insight_failed'\n        };\n    }\n}\n// 深度兼容性推理 Agent\nasync function runCompatibilityInferenceNode(state) {\n    try {\n        console.log('🔍 执行兼容性推理 Agent...');\n        const { userSoulProfile, candidatePoolIds } = state;\n        if (!userSoulProfile || !candidatePoolIds) {\n            throw new Error('缺少必要的状态数据');\n        }\n        // 这里需要从数据库获取候选人资料\n        // 为了演示，我们先创建一个模拟的候选人分析函数\n        const candidatesWithAnalysis = [];\n        // 并行处理所有候选人\n        const analysisPromises = candidatePoolIds.map(async (candidateId)=>{\n            // TODO: 从数据库获取候选人详细资料\n            const candidateProfile = await getCandidateProfile(candidateId);\n            const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.compatibilityInference.replace('{userSoulProfile}', JSON.stringify(userSoulProfile)).replace('{candidateName}', candidateProfile.name || '未知').replace('{candidateAge}', candidateProfile.age?.toString() || '未知').replace('{candidateSelfDescription}', candidateProfile.selfDescription || '').replace('{candidateInterests}', JSON.stringify(candidateProfile.interests || [])).replace('{candidateValues}', JSON.stringify(candidateProfile.values || [])).replace('{candidateLifestyle}', JSON.stringify(candidateProfile.lifestyle || {}));\n            const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n            const result = JSON.parse(responseContent);\n            return {\n                candidateId,\n                compatibilityScore: result.compatibilityScore,\n                reasoning: result.reasoning,\n                highlights: result.highlights,\n                challenges: result.challenges,\n                personalitySummary: result.personalitySummary\n            };\n        });\n        const analysisResults = await Promise.all(analysisPromises);\n        candidatesWithAnalysis.push(...analysisResults);\n        console.log(`✅ 兼容性推理完成，分析了 ${candidatesWithAnalysis.length} 个候选人`);\n        return {\n            candidatesWithAnalysis,\n            step: 'compatibility_inference_completed'\n        };\n    } catch (error) {\n        console.error('❌ 兼容性推理失败:', error);\n        return {\n            error: `兼容性推理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'compatibility_inference_failed'\n        };\n    }\n}\n// 从数据库获取候选人资料\nasync function getCandidateProfile(candidateId) {\n    const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\"));\n    const { users, userProfiles } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\"));\n    const { eq } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/drizzle-orm\").then(__webpack_require__.bind(__webpack_require__, /*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/index.js\"));\n    const candidateWithProfile = await db.select({\n        user: users,\n        profile: userProfiles\n    }).from(users).leftJoin(userProfiles, eq(users.id, userProfiles.userId)).where(eq(users.id, candidateId)).limit(1);\n    if (candidateWithProfile.length === 0) {\n        throw new Error(`候选人 ${candidateId} 不存在`);\n    }\n    const candidate = candidateWithProfile[0];\n    return {\n        userId: candidate.user.id,\n        name: candidate.user.name || '未知',\n        age: candidate.user.age || 0,\n        selfDescription: candidate.profile?.selfDescription || '',\n        interests: candidate.user.interests || [],\n        values: candidate.profile?.values || [],\n        lifestyle: candidate.profile?.lifestyle || {}\n    };\n}\n// 排序和最终决策 Agent\nasync function rankAndFinalizeNode(state) {\n    try {\n        console.log('🏆 执行排序和最终决策 Agent...');\n        const { candidatesWithAnalysis } = state;\n        if (!candidatesWithAnalysis || candidatesWithAnalysis.length === 0) {\n            throw new Error('没有候选人分析数据');\n        }\n        // 按兼容性分数排序\n        const rankedCandidates = candidatesWithAnalysis.sort((a, b)=>b.compatibilityScore - a.compatibilityScore).slice(0, 5); // 取前5名\n        console.log(`✅ 排序完成，选出前 ${rankedCandidates.length} 名候选人`);\n        return {\n            rankedCandidates,\n            step: 'ranking_completed'\n        };\n    } catch (error) {\n        console.error('❌ 排序失败:', error);\n        return {\n            error: `排序失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'ranking_failed'\n        };\n    }\n}\n// 生成完整报告 Agent\nasync function generateFullReportNode(state) {\n    try {\n        console.log('📝 生成完整报告...');\n        const { rankedCandidates, userSoulProfile } = state;\n        if (!rankedCandidates || !userSoulProfile) {\n            throw new Error('缺少必要数据');\n        }\n        // 为首席推荐生成完整报告\n        const topCandidate = rankedCandidates[0];\n        // 生成关系洞察、对话模拟和约会计划\n        const [relationshipInsight, conversationSimulation, datePlan] = await Promise.all([\n            generateRelationshipInsight(userSoulProfile, topCandidate),\n            generateConversationSimulation(userSoulProfile, topCandidate),\n            generateDatePlan(userSoulProfile, topCandidate)\n        ]);\n        const finalMatrix = {\n            topMatch: {\n                candidate: topCandidate,\n                relationshipInsight,\n                conversationSimulation,\n                datePlan\n            },\n            potentialMatches: rankedCandidates.slice(1, 5).map((candidate)=>({\n                    candidate,\n                    highlights: candidate.highlights,\n                    compatibilityReason: candidate.reasoning\n                })),\n            generatedAt: new Date(),\n            requestId: state.requesterId\n        };\n        console.log('✅ 完整报告生成完成');\n        return {\n            finalMatrix,\n            step: 'report_generation_completed'\n        };\n    } catch (error) {\n        console.error('❌ 报告生成失败:', error);\n        return {\n            error: `报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'report_generation_failed'\n        };\n    }\n}\n// 辅助函数：生成关系洞察\nasync function generateRelationshipInsight(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.relationshipHighlight.replace('{userSoulProfile}', JSON.stringify(userProfile)).replace('{candidateAnalysis}', JSON.stringify(candidate));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成对话模拟\nasync function generateConversationSimulation(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.conversationSimulation.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{scenario}', '咖啡厅初次见面');\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.conversation);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成约会计划\nasync function generateDatePlan(userProfile, candidate) {\n    // 找出共同兴趣\n    const commonInterests = userProfile.coreValues.filter((value)=>candidate.highlights.some((highlight)=>highlight.includes(value)));\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.datePlanGeneration.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{commonInterests}', JSON.stringify(commonInterests));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.dating);\n    return JSON.parse(responseContent);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/index.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulStateAnnotation),\n/* harmony export */   PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS),\n/* harmony export */   aragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.createAragSoulWorkflow),\n/* harmony export */   \"default\": () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   generateFullReportNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/services/arag-soul/types.ts\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n/* harmony import */ var _workflow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./workflow */ \"(rsc)/./src/lib/services/arag-soul/workflow.ts\");\n// ARAG-Soul 框架主入口\n\n\n\n\n// 便捷导出\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2FyYWctc291bC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxrQkFBa0I7QUFFTTtBQUNFO0FBQ0Q7QUFDRTtBQUUzQixPQUFPO0FBQ2lEIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvc2VydmljZXMvYXJhZy1zb3VsL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFSQUctU291bCDmoYbmnrbkuLvlhaXlj6NcblxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL3Byb21wdHMnO1xuZXhwb3J0ICogZnJvbSAnLi9hZ2VudHMnO1xuZXhwb3J0ICogZnJvbSAnLi93b3JrZmxvdyc7XG5cbi8vIOS+v+aNt+WvvOWHulxuZXhwb3J0IHsgYXJhZ1NvdWxTZXJ2aWNlIGFzIGRlZmF1bHQgfSBmcm9tICcuL3dvcmtmbG93JztcbiJdLCJuYW1lcyI6WyJhcmFnU291bFNlcnZpY2UiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/prompts.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/arag-soul/prompts.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPTS: () => (/* binding */ PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* binding */ SYSTEM_PROMPTS)\n/* harmony export */ });\n// ARAG-Soul 框架的 Prompt 模板\nconst PROMPTS = {\n    // 人格洞察 Agent\n    personalityInsight: `\n你是一位专业的心理学家和人格分析师。请基于用户的详细资料，生成一份深度的\"灵魂画像\"。\n\n用户资料：\n姓名: {name}\n年龄: {age}\n性别: {gender}\n自我描述: {selfDescription}\n兴趣爱好: {interests}\n价值观: {values}\n生活方式: {lifestyle}\n感情目标: {relationshipGoals}\n\n请分析并生成：\n1. 核心人格特质（5-7个关键词）\n2. 沟通风格描述\n3. 价值观体系\n4. 情感需求\n5. 生活态度\n6. 一句话人格总结\n\n请以JSON格式返回：\n{\n  \"personalityTraits\": {\n    \"openness\": 0.8,\n    \"conscientiousness\": 0.7,\n    \"extraversion\": 0.6,\n    \"agreeableness\": 0.9,\n    \"neuroticism\": 0.3\n  },\n  \"coreValues\": [\"诚实\", \"成长\", \"家庭\"],\n  \"communicationStyle\": \"温和而深度的交流者\",\n  \"emotionalNeeds\": [\"理解\", \"支持\", \"共同成长\"],\n  \"lifeAttitude\": \"积极向上，注重内在成长\",\n  \"summary\": \"一个温暖、有深度、追求真实连接的灵魂\"\n}\n`,\n    // 深度兼容性推理 Agent\n    compatibilityInference: `\n你是一位资深的情感匹配专家。请分析两个人的兼容性。\n\n用户A的灵魂画像：\n{userSoulProfile}\n\n候选人B的资料：\n姓名: {candidateName}\n年龄: {candidateAge}\n自我描述: {candidateSelfDescription}\n兴趣爱好: {candidateInterests}\n价值观: {candidateValues}\n生活方式: {candidateLifestyle}\n\n请进行深度分析并给出：\n1. 兼容性分数 (0-100)\n2. 详细推理过程\n3. 关系亮点 (3-5个)\n4. 潜在挑战 (2-3个)\n5. 候选人人格摘要\n\n请以JSON格式返回：\n{\n  \"compatibilityScore\": 85,\n  \"reasoning\": \"详细的兼容性分析...\",\n  \"highlights\": [\"共同的价值观\", \"互补的性格\", \"相似的生活目标\"],\n  \"challenges\": [\"沟通方式差异\", \"生活节奏不同\"],\n  \"personalitySummary\": \"一个温暖、独立、有创造力的人\"\n}\n`,\n    // 关系亮点提炼 Agent\n    relationshipHighlight: `\n基于兼容性分析，请深入挖掘这段关系的潜力。\n\n用户A: {userSoulProfile}\n候选人B: {candidateAnalysis}\n\n请提供：\n1. 关系优势 (3-4个具体方面)\n2. 成长机会 (2-3个)\n3. 相处建议 (3-4条实用建议)\n4. 沟通技巧 (2-3个针对性建议)\n\n请以JSON格式返回：\n{\n  \"strengths\": [\"深度的精神连接\", \"互补的技能组合\"],\n  \"growthOpportunities\": [\"共同探索新兴趣\", \"相互学习不同视角\"],\n  \"suggestions\": [\"定期深度对话\", \"尊重彼此的独立空间\"],\n  \"communicationTips\": [\"使用'我'语句表达感受\", \"积极倾听对方观点\"]\n}\n`,\n    // 对话模拟 Agent\n    conversationSimulation: `\n请模拟用户A和候选人B的一段自然对话。\n\n用户A资料: {userProfile}\n候选人B资料: {candidateProfile}\n对话场景: {scenario}\n\n要求：\n1. 对话要体现双方的性格特点\n2. 包含6-8轮对话\n3. 展现自然的互动和化学反应\n4. 体现共同兴趣或价值观\n\n请以JSON格式返回：\n{\n  \"scenario\": \"咖啡厅初次见面\",\n  \"messages\": [\n    {\"speaker\": \"user\", \"content\": \"...\", \"emotion\": \"好奇\"},\n    {\"speaker\": \"candidate\", \"content\": \"...\", \"emotion\": \"友善\"}\n  ],\n  \"analysis\": \"这段对话展现了双方的...\"\n}\n`,\n    // 约会计划生成 Agent\n    datePlanGeneration: `\n基于两人的共同兴趣和性格特点，设计一个完美的初次约会计划。\n\n用户A: {userProfile}\n候选人B: {candidateProfile}\n共同兴趣: {commonInterests}\n\n请设计：\n1. 约会主题和地点\n2. 具体活动安排\n3. 时间规划\n4. 预算建议\n5. 为什么这个计划适合他们\n\n请以JSON格式返回：\n{\n  \"title\": \"艺术与咖啡的邂逅\",\n  \"description\": \"结合艺术欣赏和深度交流的约会\",\n  \"location\": \"市中心艺术区\",\n  \"activities\": [\"参观画廊\", \"咖啡厅聊天\", \"街头艺术漫步\"],\n  \"duration\": \"3-4小时\",\n  \"budget\": \"200-300元\",\n  \"reasoning\": \"这个计划结合了双方对艺术的热爱...\"\n}\n`\n};\n// 系统提示词\nconst SYSTEM_PROMPTS = {\n    default: `你是寡佬AI的专业红娘助手，专门负责深度的情感匹配分析。你的分析要准确、深入、有洞察力，同时保持温暖和专业的语调。请始终以JSON格式返回结构化的结果。`,\n    personality: `你是一位专业的心理学家，擅长人格分析和深度洞察。你的分析要基于心理学理论，同时具有实用性。`,\n    compatibility: `你是一位资深的情感匹配专家，拥有丰富的关系咨询经验。你的分析要客观、全面，既看到优势也识别挑战。`,\n    conversation: `你是一位对话专家，擅长模拟真实的人际互动。你的对话要自然、有趣，体现人物的真实性格。`,\n    dating: `你是一位约会策划专家，了解各种约会形式和场所。你的建议要实用、有创意，适合不同性格的人。`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/types.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/types.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// ARAG-Soul 框架的类型定义\n// Prompt 模板\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/workflow.ts":
/*!************************************************!*\
  !*** ./src/lib/services/arag-soul/workflow.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* binding */ AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* binding */ AragSoulStateAnnotation),\n/* harmony export */   aragSoulService: () => (/* binding */ aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* binding */ createAragSoulWorkflow)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n// ARAG-Soul 工作流编排器\n // 暂时不使用\n\n\n\n\n// 定义 ARAG-Soul 状态注解\nconst AragSoulStateAnnotation = _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation.Root({\n    // 输入\n    requesterId: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    userProfile: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    candidatePoolIds: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 中间状态\n    userSoulProfile: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    candidatesWithAnalysis: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    rankedCandidates: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 输出\n    finalMatrix: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    // 错误处理\n    error: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    step: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>'initialized'\n    })\n});\n// 候选人检索节点\nasync function retrieveCandidatesNode(state) {\n    try {\n        console.log('🔍 检索候选人池...');\n        const { requesterId } = state;\n        if (!requesterId) {\n            throw new Error('缺少请求者ID');\n        }\n        // 获取用户资料\n        const userWithProfile = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            user: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users,\n            profile: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId)).limit(1);\n        if (userWithProfile.length === 0) {\n            throw new Error('用户不存在');\n        }\n        const userProfile = {\n            userId: userWithProfile[0].user.id,\n            name: userWithProfile[0].user.name,\n            age: userWithProfile[0].user.age,\n            gender: userWithProfile[0].user.gender,\n            interests: userWithProfile[0].user.interests,\n            selfDescription: userWithProfile[0].profile?.selfDescription,\n            values: userWithProfile[0].profile?.values,\n            lifestyle: userWithProfile[0].profile?.lifestyle,\n            relationshipGoals: userWithProfile[0].profile?.relationshipGoals\n        };\n        // 检索候选人池（排除自己，选择异性，活跃用户）\n        const candidatePool = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ne)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.isActive, true), // 异性匹配逻辑\n        userProfile.gender === 'male' ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'female') : (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'male'))).limit(8); // 限制候选人池大小\n        const candidatePoolIds = candidatePool.map((c)=>c.id);\n        console.log(`✅ 检索到 ${candidatePoolIds.length} 个候选人`);\n        return {\n            userProfile,\n            candidatePoolIds,\n            step: 'candidates_retrieved'\n        };\n    } catch (error) {\n        console.error('❌ 候选人检索失败:', error);\n        return {\n            error: `候选人检索失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'candidates_retrieval_failed'\n        };\n    }\n}\n// 条件路由函数\nfunction shouldContinue(state) {\n    if (state.error) {\n        return '__end__';\n    }\n    switch(state.step){\n        case 'candidates_retrieved':\n            return 'generateUserSoulProfile';\n        case 'personality_insight_completed':\n            return 'runCompatibilityInference';\n        case 'compatibility_inference_completed':\n            return 'rankAndFinalize';\n        case 'ranking_completed':\n            return 'generateFullReport';\n        case 'report_generation_completed':\n            return '__end__';\n        default:\n            return '__end__';\n    }\n}\n// 创建 ARAG-Soul 工作流\nfunction createAragSoulWorkflow() {\n    const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph(AragSoulStateAnnotation)// 添加节点\n    .addNode('retrieveCandidates', retrieveCandidatesNode).addNode('generateUserSoulProfile', _agents__WEBPACK_IMPORTED_MODULE_3__.generateUserSoulProfileNode).addNode('runCompatibilityInference', _agents__WEBPACK_IMPORTED_MODULE_3__.runCompatibilityInferenceNode).addNode('rankAndFinalize', _agents__WEBPACK_IMPORTED_MODULE_3__.rankAndFinalizeNode).addNode('generateFullReport', _agents__WEBPACK_IMPORTED_MODULE_3__.generateFullReportNode)// 设置入口点\n    .addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'retrieveCandidates')// 添加条件边\n    .addConditionalEdges('retrieveCandidates', shouldContinue, {\n        'generateUserSoulProfile': 'generateUserSoulProfile',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateUserSoulProfile', shouldContinue, {\n        'runCompatibilityInference': 'runCompatibilityInference',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('runCompatibilityInference', shouldContinue, {\n        'rankAndFinalize': 'rankAndFinalize',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('rankAndFinalize', shouldContinue, {\n        'generateFullReport': 'generateFullReport',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateFullReport', shouldContinue, {\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    });\n    return workflow.compile();\n}\n// ARAG-Soul 服务主入口\nclass AragSoulService {\n    constructor(){\n        this.workflow = createAragSoulWorkflow();\n    }\n    async generateCandidateMatrix(requesterId) {\n        try {\n            console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${requesterId}`);\n            const initialState = {\n                requesterId,\n                step: 'initialized'\n            };\n            // 执行工作流\n            const result = await this.workflow.invoke(initialState);\n            if (result.error) {\n                throw new Error(result.error);\n            }\n            if (!result.finalMatrix) {\n                throw new Error('工作流未生成最终结果');\n            }\n            console.log('✅ ARAG-Soul 工作流执行完成');\n            return result.finalMatrix;\n        } catch (error) {\n            console.error('❌ ARAG-Soul 工作流执行失败:', error);\n            throw error;\n        }\n    }\n    // 流式执行（用于实时监控）\n    async *generateCandidateMatrixStream(requesterId) {\n        const initialState = {\n            requesterId,\n            step: 'initialized'\n        };\n        try {\n            for await (const step of this.workflow.stream(initialState)){\n                yield {\n                    step: step.step || 'processing',\n                    data: step,\n                    timestamp: new Date()\n                };\n            }\n        } catch (error) {\n            yield {\n                step: 'error',\n                error: error instanceof Error ? error.message : '未知错误',\n                timestamp: new Date()\n            };\n        }\n    }\n}\n// 导出单例实例\nconst aragSoulService = new AragSoulService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/workflow.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/matching-v2.ts":
/*!*****************************************!*\
  !*** ./src/lib/services/matching-v2.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchingServiceV2: () => (/* binding */ MatchingServiceV2)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _arag_soul__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./arag-soul */ \"(rsc)/./src/lib/services/arag-soul/index.ts\");\n// V2.0 匹配服务 - 智能候选人矩阵\n\n\n\n\n\n// 确保环境变量被加载\n(0,_lib_utils_env__WEBPACK_IMPORTED_MODULE_0__.ensureEnvLoaded)();\nclass MatchingServiceV2 {\n    /**\n   * 处理队列中的匹配请求\n   */ static async processQueuedRequests() {\n        try {\n            console.log('🔄 开始处理匹配队列...');\n            // 获取待处理的任务\n            const pendingTasks = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.status, 'pending')).limit(5); // 一次处理5个任务\n            if (pendingTasks.length === 0) {\n                console.log('📭 队列为空，无待处理任务');\n                return {\n                    processedCount: 0\n                };\n            }\n            console.log(`📋 发现 ${pendingTasks.length} 个待处理任务`);\n            // 并行处理任务\n            const processingPromises = pendingTasks.map((task)=>this.processSingleRequest(task.matchRequestId, task.requesterId));\n            const results = await Promise.allSettled(processingPromises);\n            const successCount = results.filter((result)=>result.status === 'fulfilled').length;\n            console.log(`✅ 队列处理完成，成功处理 ${successCount}/${pendingTasks.length} 个任务`);\n            return {\n                processedCount: successCount\n            };\n        } catch (error) {\n            console.error('❌ 队列处理失败:', error);\n            return {\n                processedCount: 0\n            };\n        }\n    }\n    /**\n   * 处理单个匹配请求\n   */ static async processSingleRequest(requestId, requesterId) {\n        try {\n            console.log(`🎯 开始处理请求: ${requestId}`);\n            // 更新队列状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            // 更新请求状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            // 调用 ARAG-Soul 生成候选人矩阵\n            const candidateMatrix = await _arag_soul__WEBPACK_IMPORTED_MODULE_3__.aragSoulService.generateCandidateMatrix(requesterId);\n            // 保存结果到数据库\n            await this.saveCandidateMatrix(requestId, candidateMatrix);\n            // 更新状态为完成\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'completed',\n                finalReport: candidateMatrix\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'completed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            console.log(`✅ 请求处理完成: ${requestId}`);\n        } catch (error) {\n            console.error(`❌ 请求处理失败: ${requestId}`, error);\n            // 更新状态为失败\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'failed',\n                errorMessage\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'failed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n        }\n    }\n    /**\n   * 保存候选人矩阵到数据库\n   */ static async saveCandidateMatrix(requestId, matrix) {\n        try {\n            // 保存首席推荐\n            const topCandidateData = {\n                requestId,\n                candidateId: matrix.topMatch.candidate.candidateId,\n                rank: 1,\n                userDecision: 'pending'\n            };\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(topCandidateData);\n            // 保存潜力候选人\n            const potentialCandidatesData = matrix.potentialMatches.map((match, index)=>({\n                    requestId,\n                    candidateId: match.candidate.candidateId,\n                    rank: index + 2,\n                    userDecision: 'pending'\n                }));\n            if (potentialCandidatesData.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(potentialCandidatesData);\n            }\n            console.log(`💾 候选人矩阵已保存: ${requestId}`);\n        } catch (error) {\n            console.error('保存候选人矩阵失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 更新用户对候选人的决策\n   */ static async updateCandidateDecision(requestId, candidateId, userId, decision) {\n        try {\n            // 验证权限\n            const request = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId)).limit(1);\n            if (request.length === 0 || request[0].requesterId !== userId) {\n                throw new Error('无权限操作此请求');\n            }\n            // 更新决策\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                userDecision: decision\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, requestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, candidateId)));\n            // 如果是喜欢，检查是否互相喜欢\n            let mutualMatch = false;\n            if (decision === 'liked') {\n                mutualMatch = await this.checkMutualMatch(userId, candidateId);\n            }\n            return {\n                success: true,\n                mutualMatch\n            };\n        } catch (error) {\n            console.error('更新候选人决策失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 检查是否互相喜欢\n   */ static async checkMutualMatch(userId, candidateId) {\n        try {\n            // 查找候选人是否也喜欢了用户\n            const candidateRequests = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                request: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests,\n                candidate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            return candidateRequests.length > 0;\n        } catch (error) {\n            console.error('检查互相匹配失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 获取用户的匹配历史\n   */ static async getUserMatchHistory(userId, limit = 10) {\n        try {\n            const history = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                request: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests,\n                candidates: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.createdAt).limit(limit);\n            return history;\n        } catch (error) {\n            console.error('获取匹配历史失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取匹配统计信息\n   */ static async getMatchingStats(userId) {\n        try {\n            // 总请求数\n            const totalRequests = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId));\n            // 成功的匹配数（至少喜欢一个候选人）\n            const successfulMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            // 互相匹配数\n            // 这里需要更复杂的查询，暂时简化\n            const mutualMatches = 0; // TODO: 实现互相匹配统计\n            return {\n                totalRequests: totalRequests.length,\n                successfulMatches: successfulMatches.length,\n                mutualMatches,\n                successRate: totalRequests.length > 0 ? (successfulMatches.length / totalRequests.length * 100).toFixed(1) : '0'\n            };\n        } catch (error) {\n            console.error('获取匹配统计失败:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/matching-v2.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createRouteClient: () => (/* binding */ createRouteClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nconst createClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\nconst createRouteClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzRztBQUMvRDtBQUVoQyxNQUFNRyxlQUFlO0lBQzFCLElBQUksS0FBbUYsRUFBRSxFQU94RjtJQUNELE1BQU1ZLGNBQWNiLHFEQUFPQTtJQUMzQixPQUFPRiwwRkFBMkJBLENBQUM7UUFBRUUsU0FBUyxJQUFNYTtJQUFZO0FBQ2xFLEVBQUU7QUFFSyxNQUFNQyxvQkFBb0I7SUFDL0IsSUFBSSxLQUFtRixFQUFFLEVBT3hGO0lBQ0QsTUFBTUQsY0FBY2IscURBQU9BO0lBQzNCLE9BQU9ELHVGQUF3QkEsQ0FBQztRQUFFQyxTQUFTLElBQU1hO0lBQVk7QUFDL0QsRUFBRSIsInNvdXJjZXMiOlsiL2hvbWUvdWJ0MjIvd29ya3NwYWNlL2luZGllL2xpbmd4aWFpLWdlbWluaS9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVTZXJ2ZXJDb21wb25lbnRDbGllbnQsIGNyZWF0ZVJvdXRlSGFuZGxlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWhlbHBlcnMtbmV4dGpzJztcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xuXG5leHBvcnQgY29uc3QgY3JlYXRlQ2xpZW50ID0gKCkgPT4ge1xuICBpZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCB8fCAhcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkpIHtcbiAgICAvLyBSZXR1cm4gYSBtb2NrIGNsaWVudCBmb3IgYnVpbGQgdGltZVxuICAgIHJldHVybiB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIGdldFVzZXI6ICgpID0+IFByb21pc2UucmVzb2x2ZSh7IGRhdGE6IHsgdXNlcjogbnVsbCB9LCBlcnJvcjogbnVsbCB9KSxcbiAgICAgIH1cbiAgICB9IGFzIGFueTtcbiAgfVxuICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNvbXBvbmVudENsaWVudCh7IGNvb2tpZXM6ICgpID0+IGNvb2tpZVN0b3JlIH0pO1xufTtcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZVJvdXRlQ2xpZW50ID0gKCkgPT4ge1xuICBpZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCB8fCAhcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkpIHtcbiAgICAvLyBSZXR1cm4gYSBtb2NrIGNsaWVudCBmb3IgYnVpbGQgdGltZVxuICAgIHJldHVybiB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIGdldFVzZXI6ICgpID0+IFByb21pc2UucmVzb2x2ZSh7IGRhdGE6IHsgdXNlcjogbnVsbCB9LCBlcnJvcjogbnVsbCB9KSxcbiAgICAgIH1cbiAgICB9IGFzIGFueTtcbiAgfVxuICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgcmV0dXJuIGNyZWF0ZVJvdXRlSGFuZGxlckNsaWVudCh7IGNvb2tpZXM6ICgpID0+IGNvb2tpZVN0b3JlIH0pO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDb21wb25lbnRDbGllbnQiLCJjcmVhdGVSb3V0ZUhhbmRsZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiYXV0aCIsImdldFVzZXIiLCJQcm9taXNlIiwicmVzb2x2ZSIsImRhdGEiLCJ1c2VyIiwiZXJyb3IiLCJjb29raWVTdG9yZSIsImNyZWF0ZVJvdXRlQ2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/env.ts":
/*!******************************!*\
  !*** ./src/lib/utils/env.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureEnvLoaded: () => (/* binding */ ensureEnvLoaded),\n/* harmony export */   getOptionalEnv: () => (/* binding */ getOptionalEnv),\n/* harmony export */   getRequiredEnv: () => (/* binding */ getRequiredEnv),\n/* harmony export */   validateRequiredEnvVars: () => (/* binding */ validateRequiredEnvVars)\n/* harmony export */ });\n// 环境变量加载工具\nlet envLoaded = false;\nfunction ensureEnvLoaded() {\n    if (envLoaded || \"undefined\" !== 'undefined') {\n        return;\n    }\n    try {\n        // 尝试加载 .env 文件\n        (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n            path: '.env'\n        });\n        envLoaded = true;\n        console.log('✅ 环境变量已加载');\n    } catch (error) {\n        // dotenv 可能不存在，尝试其他路径\n        try {\n            (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n                path: '.env.local'\n            });\n            envLoaded = true;\n            console.log('✅ 环境变量已从 .env.local 加载');\n        } catch (error2) {\n            console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');\n        }\n    }\n}\nfunction getRequiredEnv(key) {\n    ensureEnvLoaded();\n    const value = process.env[key];\n    if (!value) {\n        throw new Error(`环境变量 ${key} 未设置`);\n    }\n    return value;\n}\nfunction getOptionalEnv(key, defaultValue) {\n    ensureEnvLoaded();\n    return process.env[key] || defaultValue;\n}\n// 验证所有必需的环境变量\nfunction validateRequiredEnvVars() {\n    ensureEnvLoaded();\n    const required = [\n        'DATABASE_URL',\n        'OPENROUTER_API_KEY',\n        'NEXT_PUBLIC_SUPABASE_URL',\n        'NEXT_PUBLIC_SUPABASE_ANON_KEY'\n    ];\n    const missing = required.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);\n    }\n    console.log('✅ 所有必需的环境变量都已设置');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/env.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/drizzle-orm","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/openai","vendor-chunks/postgres","vendor-chunks/dotenv","vendor-chunks/@langchain","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/@cfworker","vendor-chunks/uuid","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/js-tiktoken","vendor-chunks/mustache","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/eventemitter3","vendor-chunks/decamelize","vendor-chunks/camelcase","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();