(()=>{var e={};e.id=983,e.ids=[983],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{P:()=>u,U:()=>i});var s=r(61223),a=r(44999);let i=()=>{let e=(0,a.UL)();return(0,s.createServerComponentClient)({cookies:()=>e})},u=()=>{let e=(0,a.UL)();return(0,s.createRouteHandlerClient)({cookies:()=>e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>R,serverHooks:()=>x,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>q});var s={};r.r(s),r.d(s,{GET:()=>f,POST:()=>m});var a=r(96559),i=r(48088),u=r(37719),n=r(32190),d=r(2507),o=r(71682),c=r(32767),l=r(94634),p=r(23870);async function m(e){try{let e=(0,d.P)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{MatchingService:a}=await Promise.all([r.e(694),r.e(385)]).then(r.bind(r,9385)),i=await a.checkDailyMatchLimit(t.id);if(!i.canMatch)return n.NextResponse.json({error:"DAILY_LIMIT_EXCEEDED",message:"今日匹配次数已用完",limitInfo:i},{status:429});let u=(0,p.A)(),l={id:u,requesterId:t.id,status:"processing"},[m]=await o.db.insert(c.matchRequests).values(l).returning(),f={matchRequestId:u,requesterId:t.id,status:"pending"};return await o.db.insert(c.matchQueue).values(f),console.log(`📝 创建匹配矩阵请求: ${u} for user: ${t.id}`),n.NextResponse.json({success:!0,requestId:u,status:"processing",message:"专属红娘正在为您筛选候选人，请稍候...",estimatedTime:"2-3分钟"},{status:202})}catch(e){return console.error("创建匹配矩阵请求失败:",e),n.NextResponse.json({error:"INTERNAL_SERVER_ERROR",message:"服务器内部错误"},{status:500})}}async function f(e){try{let t=(0,d.P)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let a=new URL(e.url).searchParams.get("requestId");if(!a)return n.NextResponse.json({error:"BAD_REQUEST",message:"缺少 requestId 参数"},{status:400});let i=await o.db.select().from(c.matchRequests).where((0,l.eq)(c.matchRequests.id,a)).limit(1);if(0===i.length)return n.NextResponse.json({error:"NOT_FOUND",message:"匹配请求不存在"},{status:404});let u=i[0];if(u.requesterId!==r.id)return n.NextResponse.json({error:"FORBIDDEN",message:"无权访问此匹配请求"},{status:403});switch(u.status){case"processing":return n.NextResponse.json({status:"processing",message:"正在生成匹配矩阵，请稍候...",requestId:a});case"completed":if(!u.finalReport)return n.NextResponse.json({error:"DATA_ERROR",message:"匹配结果数据异常"},{status:500});return n.NextResponse.json({status:"completed",requestId:a,matrix:u.finalReport,generatedAt:u.createdAt});case"failed":return n.NextResponse.json({status:"failed",message:u.errorMessage||"匹配生成失败",requestId:a},{status:500});default:return n.NextResponse.json({error:"UNKNOWN_STATUS",message:"未知的请求状态"},{status:500})}}catch(e){return console.error("获取匹配矩阵结果失败:",e),n.NextResponse.json({error:"INTERNAL_SERVER_ERROR",message:"服务器内部错误"},{status:500})}}let R=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/matches/matrix/route",pathname:"/api/matches/matrix",filename:"route",bundlePath:"app/api/matches/matrix/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:q,serverHooks:x}=R;function g(){return(0,u.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:q})}},21820:e=>{"use strict";e.exports=require("os")},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(55511);let a={randomUUID:s.randomUUID},i=new Uint8Array(256),u=i.length,n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));let d=function(e,t,r){if(a.randomUUID&&!t&&!e)return a.randomUUID();let d=(e=e||{}).random??e.rng?.()??(u>i.length-16&&((0,s.randomFillSync)(i),u=0),i.slice(u,u+=16));if(d.length<16)throw Error("Random bytes length must be >= 16");if(d[6]=15&d[6]|64,d[8]=63&d[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=d[e];return t}return function(e,t=0){return(n[e[t+0]]+n[e[t+1]]+n[e[t+2]]+n[e[t+3]]+"-"+n[e[t+4]]+n[e[t+5]]+"-"+n[e[t+6]]+n[e[t+7]]+"-"+n[e[t+8]]+n[e[t+9]]+"-"+n[e[t+10]]+n[e[t+11]]+n[e[t+12]]+n[e[t+13]]+n[e[t+14]]+n[e[t+15]]).toLowerCase()}(d)}},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{aiAgentFeedback:()=>m,conversations:()=>f,matchCandidates:()=>g,matchQueue:()=>q,matchRequests:()=>x,matches:()=>p,messages:()=>R,userProfiles:()=>l,userSessions:()=>_,users:()=>c});var s=r(92768),a=r(9594),i=r(29334),u=r(63431),n=r(34509),d=r(54693),o=r(9253);let c=(0,s.cJ)("users",{id:(0,a.uR)("id").primaryKey().defaultRandom(),email:(0,i.Qq)("email").notNull().unique(),name:(0,i.Qq)("name"),avatar:(0,i.Qq)("avatar"),bio:(0,i.Qq)("bio"),age:(0,u.nd)("age"),gender:(0,i.Qq)("gender"),location:(0,i.Qq)("location"),interests:(0,n.Fx)("interests").$type().default([]),personalityTraits:(0,n.Fx)("personality_traits").$type(),personalitySummary:(0,i.Qq)("personality_summary"),isActive:(0,d.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),l=(0,s.cJ)("user_profiles",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),selfDescription:(0,i.Qq)("self_description"),lookingFor:(0,i.Qq)("looking_for"),relationshipGoals:(0,i.Qq)("relationship_goals"),lifestyle:(0,n.Fx)("lifestyle").$type(),values:(0,n.Fx)("values").$type().default([]),photos:(0,n.Fx)("photos").$type().default([]),preferences:(0,n.Fx)("preferences").$type(),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),p=(0,s.cJ)("matches",{id:(0,a.uR)("id").primaryKey().defaultRandom(),user1Id:(0,a.uR)("user1_id").references(()=>c.id).notNull(),user2Id:(0,a.uR)("user2_id").references(()=>c.id).notNull(),compatibilityScore:(0,u.nd)("compatibility_score"),aiAnalysis:(0,n.Fx)("ai_analysis").$type(),conversationSimulation:(0,n.Fx)("conversation_simulation").$type(),status:(0,i.Qq)("status").default("pending"),user1Liked:(0,d.zM)("user1_liked").default(!1),user2Liked:(0,d.zM)("user2_liked").default(!1),user1Viewed:(0,d.zM)("user1_viewed").default(!1),user2Viewed:(0,d.zM)("user2_viewed").default(!1),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),m=(0,s.cJ)("ai_agent_feedback",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),feedbackType:(0,i.Qq)("feedback_type").notNull(),feedbackText:(0,i.Qq)("feedback_text"),rating:(0,u.nd)("rating"),aspectRated:(0,i.Qq)("aspect_rated"),createdAt:(0,o.vE)("created_at").defaultNow()}),f=(0,s.cJ)("conversations",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),isActive:(0,d.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),R=(0,s.cJ)("messages",{id:(0,a.uR)("id").primaryKey().defaultRandom(),conversationId:(0,a.uR)("conversation_id").references(()=>f.id).notNull(),senderId:(0,a.uR)("sender_id").references(()=>c.id).notNull(),content:(0,i.Qq)("content").notNull(),messageType:(0,i.Qq)("message_type").default("text"),isRead:(0,d.zM)("is_read").default(!1),createdAt:(0,o.vE)("created_at").defaultNow()}),_=(0,s.cJ)("user_sessions",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),sessionToken:(0,i.Qq)("session_token").notNull().unique(),expiresAt:(0,o.vE)("expires_at").notNull(),createdAt:(0,o.vE)("created_at").defaultNow()}),q=(0,s.cJ)("match_queue",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchRequestId:(0,a.uR)("match_request_id").notNull().unique(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("pending"),attempts:(0,u.nd)("attempts").default(0),createdAt:(0,o.vE)("created_at").defaultNow()}),x=(0,s.cJ)("match_requests",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("processing"),finalReport:(0,n.Fx)("final_report"),errorMessage:(0,i.Qq)("error_message"),createdAt:(0,o.vE)("created_at").defaultNow()}),g=(0,s.cJ)("match_candidates",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requestId:(0,a.uR)("request_id").references(()=>x.id).notNull(),candidateId:(0,a.uR)("candidate_id").references(()=>c.id).notNull(),rank:(0,u.nd)("rank").notNull(),userDecision:(0,i.Qq)("user_decision").default("pending"),createdAt:(0,o.vE)("created_at").defaultNow()})},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71682:(e,t,r)=>{"use strict";let s;r.d(t,{db:()=>s});var a=r(37383),i=r(43971),u=r(32767);let n=process.env.DATABASE_URL;if(n){let e=(0,i.A)(n,{max:1,idle_timeout:20,connect_timeout:10,ssl:"require",prepare:!1});s=(0,a.f)(e,{schema:u})}else s=(0,a.f)({},{schema:u})},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,605,383],()=>r(16501));module.exports=s})();