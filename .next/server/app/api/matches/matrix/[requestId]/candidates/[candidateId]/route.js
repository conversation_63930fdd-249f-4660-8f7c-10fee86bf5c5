(()=>{var e={};e.id=246,e.ids=[246],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{P:()=>u,U:()=>a});var s=r(61223),i=r(44999);let a=()=>{let e=(0,i.UL)();return(0,s.createServerComponentClient)({cookies:()=>e})},u=()=>{let e=(0,i.UL)();return(0,s.createRouteHandlerClient)({cookies:()=>e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},54544:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{PATCH:()=>p});var i=r(96559),a=r(48088),u=r(37719),o=r(32190),n=r(2507),c=r(8116);async function p(e,t){try{let r=(0,n.P)(),{data:{user:s},error:i}=await r.auth.getUser();if(i||!s)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{requestId:a,candidateId:u}=await t.params,{decision:p}=await e.json();if(!p||!["liked","skipped"].includes(p))return o.NextResponse.json({error:"BAD_REQUEST",message:'decision 必须是 "liked" 或 "skipped"'},{status:400});let d=await c.l.updateCandidateDecision(a,u,s.id,p);return o.NextResponse.json({success:!0,decision:p,mutualMatch:d.mutualMatch||!1,message:d.mutualMatch?"恭喜！你们互相喜欢了！":"决策已保存"})}catch(e){return console.error("更新候选人决策失败:",e),o.NextResponse.json({error:"INTERNAL_SERVER_ERROR",message:e instanceof Error?e.message:"服务器内部错误"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/matches/matrix/[requestId]/candidates/[candidateId]/route",pathname:"/api/matches/matrix/[requestId]/candidates/[candidateId]",filename:"route",bundlePath:"app/api/matches/matrix/[requestId]/candidates/[candidateId]/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/[requestId]/candidates/[candidateId]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:m}=d;function q(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,605,383,633,528],()=>r(54544));module.exports=s})();