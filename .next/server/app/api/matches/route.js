(()=>{var e={};e.id=809,e.ids=[809],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{P:()=>n,U:()=>i});var s=r(61223),a=r(44999);let i=()=>{let e=(0,a.UL)();return(0,s.createServerComponentClient)({cookies:()=>e})},n=()=>{let e=(0,a.UL)();return(0,s.createRouteHandlerClient)({cookies:()=>e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{aiAgentFeedback:()=>m,conversations:()=>f,matchCandidates:()=>g,matchQueue:()=>x,matchRequests:()=>R,matches:()=>p,messages:()=>_,userProfiles:()=>l,userSessions:()=>h,users:()=>c});var s=r(92768),a=r(9594),i=r(29334),n=r(63431),u=r(34509),d=r(54693),o=r(9253);let c=(0,s.cJ)("users",{id:(0,a.uR)("id").primaryKey().defaultRandom(),email:(0,i.Qq)("email").notNull().unique(),name:(0,i.Qq)("name"),avatar:(0,i.Qq)("avatar"),bio:(0,i.Qq)("bio"),age:(0,n.nd)("age"),gender:(0,i.Qq)("gender"),location:(0,i.Qq)("location"),interests:(0,u.Fx)("interests").$type().default([]),personalityTraits:(0,u.Fx)("personality_traits").$type(),personalitySummary:(0,i.Qq)("personality_summary"),isActive:(0,d.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),l=(0,s.cJ)("user_profiles",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),selfDescription:(0,i.Qq)("self_description"),lookingFor:(0,i.Qq)("looking_for"),relationshipGoals:(0,i.Qq)("relationship_goals"),lifestyle:(0,u.Fx)("lifestyle").$type(),values:(0,u.Fx)("values").$type().default([]),photos:(0,u.Fx)("photos").$type().default([]),preferences:(0,u.Fx)("preferences").$type(),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),p=(0,s.cJ)("matches",{id:(0,a.uR)("id").primaryKey().defaultRandom(),user1Id:(0,a.uR)("user1_id").references(()=>c.id).notNull(),user2Id:(0,a.uR)("user2_id").references(()=>c.id).notNull(),compatibilityScore:(0,n.nd)("compatibility_score"),aiAnalysis:(0,u.Fx)("ai_analysis").$type(),conversationSimulation:(0,u.Fx)("conversation_simulation").$type(),status:(0,i.Qq)("status").default("pending"),user1Liked:(0,d.zM)("user1_liked").default(!1),user2Liked:(0,d.zM)("user2_liked").default(!1),user1Viewed:(0,d.zM)("user1_viewed").default(!1),user2Viewed:(0,d.zM)("user2_viewed").default(!1),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),m=(0,s.cJ)("ai_agent_feedback",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),feedbackType:(0,i.Qq)("feedback_type").notNull(),feedbackText:(0,i.Qq)("feedback_text"),rating:(0,n.nd)("rating"),aspectRated:(0,i.Qq)("aspect_rated"),createdAt:(0,o.vE)("created_at").defaultNow()}),f=(0,s.cJ)("conversations",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),isActive:(0,d.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),_=(0,s.cJ)("messages",{id:(0,a.uR)("id").primaryKey().defaultRandom(),conversationId:(0,a.uR)("conversation_id").references(()=>f.id).notNull(),senderId:(0,a.uR)("sender_id").references(()=>c.id).notNull(),content:(0,i.Qq)("content").notNull(),messageType:(0,i.Qq)("message_type").default("text"),isRead:(0,d.zM)("is_read").default(!1),createdAt:(0,o.vE)("created_at").defaultNow()}),h=(0,s.cJ)("user_sessions",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),sessionToken:(0,i.Qq)("session_token").notNull().unique(),expiresAt:(0,o.vE)("expires_at").notNull(),createdAt:(0,o.vE)("created_at").defaultNow()}),x=(0,s.cJ)("match_queue",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchRequestId:(0,a.uR)("match_request_id").notNull().unique(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("pending"),attempts:(0,n.nd)("attempts").default(0),createdAt:(0,o.vE)("created_at").defaultNow()}),R=(0,s.cJ)("match_requests",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("processing"),finalReport:(0,u.Fx)("final_report"),errorMessage:(0,i.Qq)("error_message"),createdAt:(0,o.vE)("created_at").defaultNow()}),g=(0,s.cJ)("match_candidates",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requestId:(0,a.uR)("request_id").references(()=>R.id).notNull(),candidateId:(0,a.uR)("candidate_id").references(()=>c.id).notNull(),rank:(0,n.nd)("rank").notNull(),userDecision:(0,i.Qq)("user_decision").default("pending"),createdAt:(0,o.vE)("created_at").defaultNow()})},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71682:(e,t,r)=>{"use strict";let s;r.d(t,{db:()=>s});var a=r(37383),i=r(43971),n=r(32767);let u=process.env.DATABASE_URL;if(u){let e=(0,i.A)(u,{max:1,idle_timeout:20,connect_timeout:10,ssl:"require",prepare:!1});s=(0,a.f)(e,{schema:n})}else s=(0,a.f)({},{schema:n})},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>l});var a=r(96559),i=r(48088),n=r(37719),u=r(32190),d=r(2507),o=r(9385);async function c(e){try{let t=(0,d.P)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});let a=new URL(e.url);if("true"===a.searchParams.get("generate_daily"))try{if(await o.MatchingService.generateSingleMatch(r.id)){let e=await o.MatchingService.getUserMatches(r.id);return u.NextResponse.json({matches:e})}{let e=await o.MatchingService.getUserMatches(r.id);return u.NextResponse.json({matches:e,message:"暂时没有新的匹配对象"})}}catch(t){if(console.error("Error generating new match:",t),"DAILY_LIMIT_EXCEEDED"===t.message){let e=t.limitInfo,r=new Date(e.nextResetTime),s=Math.ceil((r.getTime()-Date.now())/36e5);return u.NextResponse.json({error:"DAILY_LIMIT_EXCEEDED",message:`今日匹配次数已达上限（3次）`,limitInfo:{remainingMatches:e.remainingMatches,hoursUntilReset:Math.max(1,s),nextResetTime:e.nextResetTime}},{status:429})}let e=await o.MatchingService.getUserMatches(r.id);return u.NextResponse.json({matches:e,error:"生成匹配失败，请稍后重试"})}{let e=await o.MatchingService.getUserMatches(r.id);return u.NextResponse.json({matches:e})}}catch(e){return console.error("Error fetching matches:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let t=(0,d.P)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{action:a,targetUserId:i}=await e.json();if("generate_daily"===a){let e=await o.MatchingService.generateDailyMatches(r.id);return u.NextResponse.json({matches:e})}if("create_match"===a&&i){let e=await o.MatchingService.createMatch(r.id,i);return u.NextResponse.json({match:e})}return u.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error processing match request:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/matches/route",pathname:"/api/matches",filename:"route",bundlePath:"app/api/matches/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:_}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,605,383,694,385],()=>r(88351));module.exports=s})();