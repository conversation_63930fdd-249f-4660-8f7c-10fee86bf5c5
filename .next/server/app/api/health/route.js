(()=>{var e={};e.id=772,e.ids=[772],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{P:()=>i,U:()=>o});var s=t(61223),n=t(44999);let o=()=>{let e=(0,n.UL)();return(0,s.createServerComponentClient)({cookies:()=>e})},i=()=>{let e=(0,n.UL)();return(0,s.createRouteHandlerClient)({cookies:()=>e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58052:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>c});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(2507);async function c(){try{let e=Date.now(),r=(0,u.U)(),{data:t,error:s}=await r.from("users").select("count").limit(1);if(s)throw Error(`Database connection failed: ${s.message}`);let n=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","OPENROUTER_API_KEY"].filter(e=>!process.env[e]),o="unknown";try{o=process.env.OPENROUTER_API_KEY?"configured":"not_configured"}catch(e){o="error"}let i=Date.now()-e,c={status:0===n.length?"healthy":"degraded",timestamp:new Date().toISOString(),responseTime:`${i}ms`,services:{database:"connected",openrouter:o},environment:{nodeEnv:"production",vercelEnv:process.env.VERCEL_ENV||"development",region:process.env.VERCEL_REGION||"unknown"},version:process.env.npm_package_version||"1.0.0",deployment:{vercelUrl:process.env.VERCEL_URL||"localhost",gitCommitSha:process.env.VERCEL_GIT_COMMIT_SHA?.substring(0,7)||"unknown",gitBranch:process.env.VERCEL_GIT_COMMIT_REF||"unknown"}};return n.length>0&&(c.warnings={missingEnvVars:n}),a.NextResponse.json(c,{status:0===n.length?200:206,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return console.error("Health check failed:",e),a.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",services:{database:"error",openrouter:"unknown"},environment:{nodeEnv:"production",vercelEnv:process.env.VERCEL_ENV||"development"}},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/health/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:v}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,605],()=>t(58052));module.exports=s})();