"use strict";(()=>{var e={};e.id=788,e.ids=[788],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},77888:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>i});var o=s(96559),n=s(48088),a=s(37719),u=s(32190),p=s(8116);async function i(e){try{let r=e.headers.get("authorization"),s=process.env.WORKER_SECRET_TOKEN;if(s&&r!==`Bearer ${s}`)return u.NextResponse.json({error:"Unauthorized"},{status:401});return console.log("\uD83D\uDD04 Worker: 开始处理匹配队列"),await p.l.processQueuedRequests(),u.NextResponse.json({success:!0,message:"队列处理完成",timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Worker: 队列处理失败:",e),u.NextResponse.json({error:"PROCESSING_FAILED",message:e instanceof Error?e.message:"队列处理失败"},{status:500})}}async function c(){return u.NextResponse.json({status:"healthy",service:"queue-processor",timestamp:new Date().toISOString()})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/worker/process-queue/route",pathname:"/api/worker/process-queue",filename:"route",bundlePath:"app/api/worker/process-queue/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/worker/process-queue/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:q}=d;function m(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},91645:e=>{e.exports=require("net")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580,383,718,528],()=>s(77888));module.exports=t})();