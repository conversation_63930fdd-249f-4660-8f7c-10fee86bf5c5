(()=>{var e={};e.id=43,e.ids=[43],e.modules={2496:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(96559),i=r(48088),u=r(37719),d=r(32190),n=r(2507),o=r(71682),c=r(32767);async function l(e){try{let t=(0,n.P)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{matchId:a,feedbackType:i,rating:u,aspectRated:l,feedbackText:p}=await e.json(),f=await o.db.insert(c.aiAgentFeedback).values({matchId:a,userId:r.id,feedbackType:i,rating:u,aspectRated:l,feedbackText:p}).returning();return d.NextResponse.json({feedback:f[0]})}catch(e){return console.error("Error saving feedback:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/feedback/route",pathname:"/api/feedback",filename:"route",bundlePath:"app/api/feedback/route"},resolvedPagePath:"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/feedback/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:_}=p;function q(){return(0,u.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},2507:(e,t,r)=>{"use strict";r.d(t,{P:()=>u,U:()=>i});var s=r(61223),a=r(44999);let i=()=>{let e=(0,a.UL)();return(0,s.createServerComponentClient)({cookies:()=>e})},u=()=>{let e=(0,a.UL)();return(0,s.createRouteHandlerClient)({cookies:()=>e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{aiAgentFeedback:()=>f,conversations:()=>m,matchCandidates:()=>x,matchQueue:()=>y,matchRequests:()=>v,matches:()=>p,messages:()=>_,userProfiles:()=>l,userSessions:()=>q,users:()=>c});var s=r(92768),a=r(9594),i=r(29334),u=r(63431),d=r(34509),n=r(54693),o=r(9253);let c=(0,s.cJ)("users",{id:(0,a.uR)("id").primaryKey().defaultRandom(),email:(0,i.Qq)("email").notNull().unique(),name:(0,i.Qq)("name"),avatar:(0,i.Qq)("avatar"),bio:(0,i.Qq)("bio"),age:(0,u.nd)("age"),gender:(0,i.Qq)("gender"),location:(0,i.Qq)("location"),interests:(0,d.Fx)("interests").$type().default([]),personalityTraits:(0,d.Fx)("personality_traits").$type(),personalitySummary:(0,i.Qq)("personality_summary"),isActive:(0,n.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),l=(0,s.cJ)("user_profiles",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),selfDescription:(0,i.Qq)("self_description"),lookingFor:(0,i.Qq)("looking_for"),relationshipGoals:(0,i.Qq)("relationship_goals"),lifestyle:(0,d.Fx)("lifestyle").$type(),values:(0,d.Fx)("values").$type().default([]),photos:(0,d.Fx)("photos").$type().default([]),preferences:(0,d.Fx)("preferences").$type(),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),p=(0,s.cJ)("matches",{id:(0,a.uR)("id").primaryKey().defaultRandom(),user1Id:(0,a.uR)("user1_id").references(()=>c.id).notNull(),user2Id:(0,a.uR)("user2_id").references(()=>c.id).notNull(),compatibilityScore:(0,u.nd)("compatibility_score"),aiAnalysis:(0,d.Fx)("ai_analysis").$type(),conversationSimulation:(0,d.Fx)("conversation_simulation").$type(),status:(0,i.Qq)("status").default("pending"),user1Liked:(0,n.zM)("user1_liked").default(!1),user2Liked:(0,n.zM)("user2_liked").default(!1),user1Viewed:(0,n.zM)("user1_viewed").default(!1),user2Viewed:(0,n.zM)("user2_viewed").default(!1),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),f=(0,s.cJ)("ai_agent_feedback",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),feedbackType:(0,i.Qq)("feedback_type").notNull(),feedbackText:(0,i.Qq)("feedback_text"),rating:(0,u.nd)("rating"),aspectRated:(0,i.Qq)("aspect_rated"),createdAt:(0,o.vE)("created_at").defaultNow()}),m=(0,s.cJ)("conversations",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchId:(0,a.uR)("match_id").references(()=>p.id).notNull(),isActive:(0,n.zM)("is_active").default(!0),createdAt:(0,o.vE)("created_at").defaultNow(),updatedAt:(0,o.vE)("updated_at").defaultNow()}),_=(0,s.cJ)("messages",{id:(0,a.uR)("id").primaryKey().defaultRandom(),conversationId:(0,a.uR)("conversation_id").references(()=>m.id).notNull(),senderId:(0,a.uR)("sender_id").references(()=>c.id).notNull(),content:(0,i.Qq)("content").notNull(),messageType:(0,i.Qq)("message_type").default("text"),isRead:(0,n.zM)("is_read").default(!1),createdAt:(0,o.vE)("created_at").defaultNow()}),q=(0,s.cJ)("user_sessions",{id:(0,a.uR)("id").primaryKey().defaultRandom(),userId:(0,a.uR)("user_id").references(()=>c.id).notNull(),sessionToken:(0,i.Qq)("session_token").notNull().unique(),expiresAt:(0,o.vE)("expires_at").notNull(),createdAt:(0,o.vE)("created_at").defaultNow()}),y=(0,s.cJ)("match_queue",{id:(0,a.uR)("id").primaryKey().defaultRandom(),matchRequestId:(0,a.uR)("match_request_id").notNull().unique(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("pending"),attempts:(0,u.nd)("attempts").default(0),createdAt:(0,o.vE)("created_at").defaultNow()}),v=(0,s.cJ)("match_requests",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requesterId:(0,a.uR)("requester_id").references(()=>c.id).notNull(),status:(0,i.Qq)("status").default("processing"),finalReport:(0,d.Fx)("final_report"),errorMessage:(0,i.Qq)("error_message"),createdAt:(0,o.vE)("created_at").defaultNow()}),x=(0,s.cJ)("match_candidates",{id:(0,a.uR)("id").primaryKey().defaultRandom(),requestId:(0,a.uR)("request_id").references(()=>v.id).notNull(),candidateId:(0,a.uR)("candidate_id").references(()=>c.id).notNull(),rank:(0,u.nd)("rank").notNull(),userDecision:(0,i.Qq)("user_decision").default("pending"),createdAt:(0,o.vE)("created_at").defaultNow()})},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71682:(e,t,r)=>{"use strict";let s;r.d(t,{db:()=>s});var a=r(37383),i=r(43971),u=r(32767);let d=process.env.DATABASE_URL;if(d){let e=(0,i.A)(d,{max:1,idle_timeout:20,connect_timeout:10,ssl:"require",prepare:!1});s=(0,a.f)(e,{schema:u})}else s=(0,a.f)({},{schema:u})},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,605,383],()=>r(2496));module.exports=s})();