(()=>{var e={};e.id=534,e.ids=[534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,n)=>{"use strict";n.d(t,{cn:()=>i});var r=n(49384),l=n(82348);function i(...e){return(0,l.QP)((0,r.$)(e))}},7662:(e,t,n)=>{Promise.resolve().then(n.bind(n,30894))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11120:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(65840)),l=n(86908);function i(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,l.camelCase)(e,t)]=r)}),n}i.default=i,e.exports=i},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19377:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(43210),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var o=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:s,children:u,...c},p)=>(0,r.createElement)("svg",{ref:p,...l,width:o,height:o,stroke:n,strokeWidth:s?24*Number(a)/Number(o):a,className:`lucide lucide-${i(e)}`,...c},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...(Array.isArray(u)?u:[u])||[]]));return n.displayName=`${e}`,n}},19566:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,l=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,l=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!l&&!i)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(l)return l(e,n).value}return e[n]};e.exports=function e(){var t,n,r,l,u,c,p=arguments[0],d=1,f=arguments.length,h=!1;for("boolean"==typeof p&&(h=p,p=arguments[1]||{},d=2),(null==p||"object"!=typeof p&&"function"!=typeof p)&&(p={});d<f;++d)if(t=arguments[d],null!=t)for(n in t)r=s(p,n),p!==(l=s(t,n))&&(h&&l&&(o(l)||(u=i(l)))?(u?(u=!1,c=r&&i(r)?r:[]):c=r&&o(r)?r:{},a(p,{name:n,newValue:e(h,c,l)})):void 0!==l&&a(p,{name:n,newValue:l}));return p}},22633:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,n)=>{"use strict";n.d(t,{$:()=>u});var r=n(60687),l=n(43210),i=n(8730),o=n(24224),a=n(4780);let s=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=l.forwardRef(({className:e,variant:t,size:n,asChild:l=!1,...o},u)=>{let c=l?i.DX:"button";return(0,r.jsx)(c,{className:(0,a.cn)(s({variant:t,size:n,className:e})),ref:u,...o})});u.displayName="Button"},29777:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},30894:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx","default")},32440:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},33873:e=>{"use strict";e.exports=require("path")},43593:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>d,tree:()=>u});var r=n(65239),l=n(48088),i=n(88170),o=n.n(i),a=n(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);n.d(t,s);let u={children:["",{children:["match",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,30894)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx"],p={require:n,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/match/[id]/page",pathname:"/match/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},44493:(e,t,n)=>{"use strict";n.d(t,{BT:()=>u,Wu:()=>c,ZB:()=>s,Zp:()=>o,aR:()=>a});var r=n(60687),l=n(43210),i=n(4780);let o=l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let a=l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let s=l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("h3",{ref:n,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));s.displayName="CardTitle";let u=l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("p",{ref:n,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let c=l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",l.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},44517:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,l=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g;function u(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var c=1,p=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");p=~r?e.length-r:p+e.length}function f(){var e={line:c,column:p};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:p},this.source=s.source}h.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+c+":"+p+": "+t);if(n.reason=t,n.filename=s.source,n.line=c,n.column=p,n.source=e,s.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function x(e){var t;for(e=e||[];t=v();)!1!==t&&e.push(t);return e}function v(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return p+=2,d(r),e=e.slice(n),p+=2,t({type:"comment",comment:r})}}y(r);var b,k=[];for(x(k);b=function(){var e=f(),n=y(l);if(n){if(v(),!y(i))return g("property missing ':'");var r=y(o),s=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return y(a),s}}();)!1!==b&&(k.push(b),x(k));return k}},47414:(e,t,n)=>{Promise.resolve().then(n.bind(n,50402))},50402:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>nY});var r={};n.r(r),n.d(r,{boolean:()=>L,booleanish:()=>I,commaOrSpaceSeparated:()=>F,commaSeparated:()=>M,number:()=>z,overloadedBoolean:()=>R,spaceSeparated:()=>O});var l={};n.r(l),n.d(l,{attentionMarkers:()=>tH,contentInitial:()=>tM,disable:()=>tG,document:()=>tO,flow:()=>tB,flowInitial:()=>tF,insideSpan:()=>tV,string:()=>tU,text:()=>t_});var i=n(60687),o=n(43210),a=n(16189),s=n(29523),u=n(44493),c=n(96834),p=n(53344),d=n(77473),f=n(29777),h=n(19377);let m=(0,h.A)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),g=(0,h.A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);var y=n(84219);let x=(0,h.A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),v=(0,h.A)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]);var b=n(85814),k=n.n(b);let w=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,S=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,N={};function j(e,t){return((t||N).jsx?S:w).test(e)}let E=/[ \t\n\f\r]/g;function C(e){return""===e.replace(E,"")}class A{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function T(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new A(n,r,t)}function D(e){return e.toLowerCase()}A.prototype.normal={},A.prototype.property={},A.prototype.space=void 0;class q{constructor(e,t){this.attribute=t,this.property=e}}q.prototype.attribute="",q.prototype.booleanish=!1,q.prototype.boolean=!1,q.prototype.commaOrSpaceSeparated=!1,q.prototype.commaSeparated=!1,q.prototype.defined=!1,q.prototype.mustUseProperty=!1,q.prototype.number=!1,q.prototype.overloadedBoolean=!1,q.prototype.property="",q.prototype.spaceSeparated=!1,q.prototype.space=void 0;let P=0,L=B(),I=B(),R=B(),z=B(),O=B(),M=B(),F=B();function B(){return 2**++P}let U=Object.keys(r);class _ extends q{constructor(e,t,n,l){let i=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",l),"number"==typeof n)for(;++i<U.length;){let e=U[i];!function(e,t,n){n&&(e[t]=n)}(this,U[i],(n&r[e])===r[e])}}}function V(e){let t={},n={};for(let[r,l]of Object.entries(e.properties)){let i=new _(r,e.transform(e.attributes||{},r),l,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[D(r)]=r,n[D(i.attribute)]=r}return new A(t,n,e.space)}_.prototype.defined=!0;let H=V({properties:{ariaActiveDescendant:null,ariaAtomic:I,ariaAutoComplete:null,ariaBusy:I,ariaChecked:I,ariaColCount:z,ariaColIndex:z,ariaColSpan:z,ariaControls:O,ariaCurrent:null,ariaDescribedBy:O,ariaDetails:null,ariaDisabled:I,ariaDropEffect:O,ariaErrorMessage:null,ariaExpanded:I,ariaFlowTo:O,ariaGrabbed:I,ariaHasPopup:null,ariaHidden:I,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:O,ariaLevel:z,ariaLive:null,ariaModal:I,ariaMultiLine:I,ariaMultiSelectable:I,ariaOrientation:null,ariaOwns:O,ariaPlaceholder:null,ariaPosInSet:z,ariaPressed:I,ariaReadOnly:I,ariaRelevant:null,ariaRequired:I,ariaRoleDescription:O,ariaRowCount:z,ariaRowIndex:z,ariaRowSpan:z,ariaSelected:I,ariaSetSize:z,ariaSort:null,ariaValueMax:z,ariaValueMin:z,ariaValueNow:z,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function G(e,t){return t in e?e[t]:t}function $(e,t){return G(e,t.toLowerCase())}let W=V({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:M,acceptCharset:O,accessKey:O,action:null,allow:null,allowFullScreen:L,allowPaymentRequest:L,allowUserMedia:L,alt:null,as:null,async:L,autoCapitalize:null,autoComplete:O,autoFocus:L,autoPlay:L,blocking:O,capture:null,charSet:null,checked:L,cite:null,className:O,cols:z,colSpan:null,content:null,contentEditable:I,controls:L,controlsList:O,coords:z|M,crossOrigin:null,data:null,dateTime:null,decoding:null,default:L,defer:L,dir:null,dirName:null,disabled:L,download:R,draggable:I,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:L,formTarget:null,headers:O,height:z,hidden:R,high:z,href:null,hrefLang:null,htmlFor:O,httpEquiv:O,id:null,imageSizes:null,imageSrcSet:null,inert:L,inputMode:null,integrity:null,is:null,isMap:L,itemId:null,itemProp:O,itemRef:O,itemScope:L,itemType:O,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:L,low:z,manifest:null,max:null,maxLength:z,media:null,method:null,min:null,minLength:z,multiple:L,muted:L,name:null,nonce:null,noModule:L,noValidate:L,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:L,optimum:z,pattern:null,ping:O,placeholder:null,playsInline:L,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:L,referrerPolicy:null,rel:O,required:L,reversed:L,rows:z,rowSpan:z,sandbox:O,scope:null,scoped:L,seamless:L,selected:L,shadowRootClonable:L,shadowRootDelegatesFocus:L,shadowRootMode:null,shape:null,size:z,sizes:null,slot:null,span:z,spellCheck:I,src:null,srcDoc:null,srcLang:null,srcSet:null,start:z,step:null,style:null,tabIndex:z,target:null,title:null,translate:null,type:null,typeMustMatch:L,useMap:null,value:I,width:z,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:O,axis:null,background:null,bgColor:null,border:z,borderColor:null,bottomMargin:z,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:L,declare:L,event:null,face:null,frame:null,frameBorder:null,hSpace:z,leftMargin:z,link:null,longDesc:null,lowSrc:null,marginHeight:z,marginWidth:z,noResize:L,noHref:L,noShade:L,noWrap:L,object:null,profile:null,prompt:null,rev:null,rightMargin:z,rules:null,scheme:null,scrolling:I,standby:null,summary:null,text:null,topMargin:z,valueType:null,version:null,vAlign:null,vLink:null,vSpace:z,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:L,disableRemotePlayback:L,prefix:null,property:null,results:z,security:null,unselectable:null},space:"html",transform:$}),Z=V({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:F,accentHeight:z,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:z,amplitude:z,arabicForm:null,ascent:z,attributeName:null,attributeType:null,azimuth:z,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:z,by:null,calcMode:null,capHeight:z,className:O,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:z,diffuseConstant:z,direction:null,display:null,dur:null,divisor:z,dominantBaseline:null,download:L,dx:null,dy:null,edgeMode:null,editable:null,elevation:z,enableBackground:null,end:null,event:null,exponent:z,externalResourcesRequired:null,fill:null,fillOpacity:z,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:M,g2:M,glyphName:M,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:z,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:z,horizOriginX:z,horizOriginY:z,id:null,ideographic:z,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:z,k:z,k1:z,k2:z,k3:z,k4:z,kernelMatrix:F,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:z,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:z,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:z,overlineThickness:z,paintOrder:null,panose1:null,path:null,pathLength:z,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:O,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:z,pointsAtY:z,pointsAtZ:z,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:F,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:F,rev:F,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:F,requiredFeatures:F,requiredFonts:F,requiredFormats:F,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:z,specularExponent:z,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:z,strikethroughThickness:z,string:null,stroke:null,strokeDashArray:F,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:z,strokeOpacity:z,strokeWidth:null,style:null,surfaceScale:z,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:F,tabIndex:z,tableValues:null,target:null,targetX:z,targetY:z,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:F,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:z,underlineThickness:z,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:z,values:null,vAlphabetic:z,vMathematical:z,vectorEffect:null,vHanging:z,vIdeographic:z,version:null,vertAdvY:z,vertOriginX:z,vertOriginY:z,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:z,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:G}),Y=V({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),K=V({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:$}),J=V({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),Q=T([H,W,Y,K,J],"html"),X=T([H,Z,Y,K,J],"svg"),ee=/[A-Z]/g,et=/-[a-z]/g,en=/^data[-\w.:]+$/i;function er(e){return"-"+e.toLowerCase()}function el(e){return e.charAt(1).toUpperCase()}let ei={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var eo=n(11120);let ea=eu("end"),es=eu("start");function eu(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function ec(e){return e&&"object"==typeof e?"position"in e||"type"in e?ed(e.position):"start"in e||"end"in e?ed(e):"line"in e||"column"in e?ep(e):"":""}function ep(e){return ef(e&&e.line)+":"+ef(e&&e.column)}function ed(e){return ep(e&&e.start)+"-"+ep(e&&e.end)}function ef(e){return e&&"number"==typeof e?e:1}class eh extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",l={},i=!1;if(t&&(l="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!l.cause&&e&&(i=!0,r=e.message,l.cause=e),!l.ruleId&&!l.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?l.ruleId=n:(l.source=n.slice(0,e),l.ruleId=n.slice(e+1))}if(!l.place&&l.ancestors&&l.ancestors){let e=l.ancestors[l.ancestors.length-1];e&&(l.place=e.position)}let o=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=o?o.line:void 0,this.name=ec(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=i&&l.cause&&"string"==typeof l.cause.stack?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}eh.prototype.file="",eh.prototype.name="",eh.prototype.reason="",eh.prototype.message="",eh.prototype.stack="",eh.prototype.column=void 0,eh.prototype.line=void 0,eh.prototype.ancestors=void 0,eh.prototype.cause=void 0,eh.prototype.fatal=void 0,eh.prototype.place=void 0,eh.prototype.ruleId=void 0,eh.prototype.source=void 0;let em={}.hasOwnProperty,eg=new Map,ey=/[A-Z]/g,ex=new Set(["table","tbody","thead","tfoot","tr"]),ev=new Set(["td","th"]),eb="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ek(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=X),e.ancestors.push(t);let i=ej(e,t.tagName,!1),o=function(e,t){let n,r,l={};for(r in t.properties)if("children"!==r&&em.call(t.properties,r)){let i=function(e,t,n){let r=function(e,t){let n=D(t),r=t,l=q;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&en.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(et,el);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!et.test(e)){let n=e.replace(ee,er);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}l=_}return new l(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return eo(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new eh("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=eb+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)em.call(e,t)&&(n[function(e){let t=e.replace(ey,eC);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?ei[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(i){let[r,o]=i;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&ev.has(t.tagName)?n=o:l[r]=o}}return n&&((l.style||(l.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),l}(e,t),a=eN(e,t);return ex.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&C(e.value):C(e))})),ew(e,o,i,t),eS(o,a),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}eE(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.name&&"html"===r.space&&(e.schema=X),e.ancestors.push(t);let i=null===t.name?e.Fragment:ej(e,t.name,!0),o=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let l=t.expression;l.type;let i=l.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else eE(e,t.position);else{let l,i=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,l=e.evaluater.evaluateExpression(t.expression)}else eE(e,t.position);else l=null===r.value||r.value;n[i]=l}return n}(e,t),a=eN(e,t);return ew(e,o,i,t),eS(o,a),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);eE(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return eS(r,eN(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function ew(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function eS(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function eN(e,t){let n=[],r=-1,l=e.passKeys?new Map:eg;for(;++r<t.children.length;){let i,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=l.get(e)||0;i=e+"-"+t,l.set(e,t+1)}}let a=ek(e,o,i);void 0!==a&&n.push(a)}return n}function ej(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),l=-1;for(;++l<n.length;){let t=j(n[l])?{type:"Identifier",name:n[l]}:{type:"Literal",value:n[l]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(l&&"Literal"===t.type),optional:!1}:t}r=e}else r=j(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return em.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);eE(e)}function eE(e,t){let n=new eh("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eb+"#cannot-handle-mdx-estrees-without-createevaluater",n}function eC(e){return"-"+e.toLowerCase()}let eA={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},eT={};function eD(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return eq(e.children,t,n)}return Array.isArray(e)?eq(e,t,n):""}function eq(e,t,n){let r=[],l=-1;for(;++l<e.length;)r[l]=eD(e[l],t,n);return r.join("")}function eP(e,t,n,r){let l,i=e.length,o=0;if(t=t<0?-t>i?0:i+t:t>i?i:t,n=n>0?n:0,r.length<1e4)(l=Array.from(r)).unshift(t,n),e.splice(...l);else for(n&&e.splice(t,n);o<r.length;)(l=r.slice(o,o+1e4)).unshift(t,0),e.splice(...l),o+=1e4,t+=1e4}function eL(e,t){return e.length>0?(eP(e,e.length,0,t),e):t}class eI{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&eR(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),eR(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),eR(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);eR(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);eR(this.left,t.reverse())}}}function eR(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ez(e){let t,n,r,l,i,o,a,s={},u=-1,c=new eI(e);for(;++u<c.length;){for(;u in s;)u=s[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r,l=e.get(t)[1],i=e.get(t)[2],o=t-1,a=[],s=l._tokenizer;!s&&(s=i.parser[l.contentType](l.start),l._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let u=s.events,c=[],p={},d=-1,f=l,h=0,m=0,g=[0];for(;f;){for(;e.get(++o)[1]!==f;);a.push(o),!f._tokenizer&&(n=i.sliceStream(f),f.next||n.push(null),r&&s.defineSkip(f.start),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=l;++d<u.length;)"exit"===u[d][0]&&"enter"===u[d-1][0]&&u[d][1].type===u[d-1][1].type&&u[d][1].start.line!==u[d][1].end.line&&(m=d+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(s.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),d=g.length;d--;){let t=u.slice(g[d],g[d+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),d=-1;++d<c.length;)p[h+c[d][0]]=h+c[d][1],h+=c[d][1]-c[d][0]-1;return p}(c,u)),u=s[u],a=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(l=c.get(r))[1].type||"lineEndingBlank"===l[1].type)"enter"===l[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),l[1].type="lineEnding",n=r);else if("linePrefix"===l[1].type||"listItemIndent"===l[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(i=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,i))}}return eP(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}let eO={}.hasOwnProperty,eM=eK(/[A-Za-z]/),eF=eK(/[\dA-Za-z]/),eB=eK(/[#-'*+\--9=?A-Z^-~]/);function eU(e){return null!==e&&(e<32||127===e)}let e_=eK(/\d/),eV=eK(/[\dA-Fa-f]/),eH=eK(/[!-/:-@[-`{-~]/);function eG(e){return null!==e&&e<-2}function e$(e){return null!==e&&(e<0||32===e)}function eW(e){return -2===e||-1===e||32===e}let eZ=eK(/\p{P}|\p{S}/u),eY=eK(/\s/);function eK(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function eJ(e,t,n,r){let l=r?r-1:Number.POSITIVE_INFINITY,i=0;return function(r){return eW(r)?(e.enter(n),function r(o){return eW(o)&&i++<l?(e.consume(o),r):(e.exit(n),t(o))}(r)):t(r)}}let eQ={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eJ(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let l=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=l),t=l,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eG(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eX={tokenize:function(e){let t,n,r,l=this,i=[],o=0;return a;function a(t){if(o<i.length){let n=i[o];return l.containerState=n[1],e.attempt(n[0].continuation,s,u)(t)}return u(t)}function s(e){if(o++,l.containerState._closeFlow){let n;l.containerState._closeFlow=void 0,t&&y();let r=l.events.length,i=r;for(;i--;)if("exit"===l.events[i][0]&&"chunkFlow"===l.events[i][1].type){n=l.events[i][1].end;break}g(o);let a=r;for(;a<l.events.length;)l.events[a][1].end={...n},a++;return eP(l.events,i+1,0,l.events.slice(r)),l.events.length=a,u(e)}return a(e)}function u(n){if(o===i.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);l.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return l.containerState={},e.check(e0,c,p)(n)}function c(e){return t&&y(),g(o),d(e)}function p(e){return l.parser.lazy[l.now().line]=o!==i.length,r=l.now().offset,h(e)}function d(t){return l.containerState={},e.attempt(e0,f,h)(t)}function f(e){return o++,i.push([l.currentConstruct,l.containerState]),d(e)}function h(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||l.parser.flow(l.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eG(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,l.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,i){let a=l.sliceStream(e);if(i&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),l.parser.lazy[e.start.line]){let e,n,i=t.events.length;for(;i--;)if(t.events[i][1].start.offset<r&&(!t.events[i][1].end||t.events[i][1].end.offset>r))return;let a=l.events.length,s=a;for(;s--;)if("exit"===l.events[s][0]&&"chunkFlow"===l.events[s][1].type){if(e){n=l.events[s][1].end;break}e=!0}for(g(o),i=a;i<l.events.length;)l.events[i][1].end={...n},i++;eP(l.events,s+1,0,l.events.slice(a)),l.events.length=i}}function g(t){let n=i.length;for(;n-- >t;){let t=i[n];l.containerState=t[1],t[0].exit.call(l,e)}i.length=t}function y(){t.write([null]),n=void 0,t=void 0,l.containerState._closeFlow=void 0}}},e0={tokenize:function(e,t,n){return eJ(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},e1={partial:!0,tokenize:function(e,t,n){return function(t){return eW(t)?eJ(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eG(e)?t(e):n(e)}}},e2={resolve:function(e){return ez(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?l(t):eG(t)?e.check(e4,i,l)(t):(e.consume(t),r)}function l(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},e4={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eJ(e,l,"linePrefix")};function l(l){if(null===l||eG(l))return n(l);let i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(l):e.interrupt(r.parser.constructs.flow,n,t)(l)}}},e3={tokenize:function(e){let t=this,n=e.attempt(e1,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,eJ(e,e.attempt(this.parser.constructs.flow,r,e.attempt(e2,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},e5={resolveAll:e8()},e6=e7("string"),e9=e7("text");function e7(e){return{resolveAll:e8("text"===e?te:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],l=t.attempt(r,i,o);return i;function i(e){return s(e)?l(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return s(e)?(t.exit("data"),l(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;let t=r[e],l=-1;if(t)for(;++l<t.length;){let e=t[l];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function e8(e){return function(t,n){let r,l=-1;for(;++l<=t.length;)void 0===r?t[l]&&"data"===t[l][1].type&&(r=l,l++):t[l]&&"data"===t[l][1].type||(l!==r+2&&(t[r][1].end=t[l-1][1].end,t.splice(r+2,l-r-2),l=r+2),r=void 0);return e?e(t,n):t}}function te(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,l=e[n-1][1],i=t.sliceStream(l),o=i.length,a=-1,s=0;for(;o--;){let e=i[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let i={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:l.start._bufferIndex+a,_index:l.start._index+o,line:l.end.line,column:l.end.column-s,offset:l.end.offset-s},end:{...l.end}};l.end={...i.start},l.start.offset===l.end.offset?Object.assign(l,i):(e.splice(n,0,["enter",i,t],["exit",i,t]),n+=2)}n++}return e}let tt={name:"thematicBreak",tokenize:function(e,t,n){let r,l=0;return function(i){var o;return e.enter("thematicBreak"),r=o=i,function i(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),l++,t):(e.exit("thematicBreakSequence"),eW(n)?eJ(e,i,"whitespace")(n):i(n))}(o)):l>=3&&(null===o||eG(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},tn={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(e1,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,eJ(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eW(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,l(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(tl,t,l)(n))});function l(l){return r.containerState._closeFlow=!0,r.interrupt=void 0,eJ(e,e.attempt(tn,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,l=r.events[r.events.length-1],i=l&&"linePrefix"===l[1].type?l[2].sliceSerialize(l[1],!0).length:0,o=0;return function(t){let l=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===l?!r.containerState.marker||t===r.containerState.marker:e_(t)){if(r.containerState.type||(r.containerState.type=l,e.enter(l,{_container:!0})),"listUnordered"===l)return e.enter("listItemPrefix"),42===t||45===t?e.check(tt,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(l){return e_(l)&&++o<10?(e.consume(l),t):(!r.interrupt||o<2)&&(r.containerState.marker?l===r.containerState.marker:41===l||46===l)?(e.exit("listItemValue"),a(l)):n(l)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(e1,r.interrupt?n:s,e.attempt(tr,c,u))}function s(e){return r.containerState.initialBlankLine=!0,i++,c(e)}function u(t){return eW(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},tr={partial:!0,tokenize:function(e,t,n){let r=this;return eJ(e,function(e){let l=r.events[r.events.length-1];return!eW(e)&&l&&"listItemPrefixWhitespace"===l[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},tl={partial:!0,tokenize:function(e,t,n){let r=this;return eJ(e,function(e){let l=r.events[r.events.length-1];return l&&"listItemIndent"===l[1].type&&l[2].sliceSerialize(l[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},ti={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eW(t)?eJ(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)};function l(r){return e.attempt(ti,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),l}return n(t)};function l(n){return eW(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function to(e,t,n,r,l,i,o,a,s){let u=s||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(l),e.enter(i),e.consume(t),e.exit(i),p):null===t||32===t||41===t||eU(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),h(t))};function p(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(l),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),p(t)):null===t||60===t||eG(t)?n(t):(e.consume(t),92===t?f:d)}function f(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(l){return!c&&(null===l||41===l||e$(l))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(l)):c<u&&40===l?(e.consume(l),c++,h):41===l?(e.consume(l),c--,h):null===l||32===l||40===l||eU(l)?n(l):(e.consume(l),92===l?m:h)}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function ta(e,t,n,r,l,i){let o,a=this,s=0;return function(t){return e.enter(r),e.enter(l),e.consume(t),e.exit(l),e.enter(i),u};function u(p){return s>999||null===p||91===p||93===p&&!o||94===p&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(p):93===p?(e.exit(i),e.enter(l),e.consume(p),e.exit(l),e.exit(r),t):eG(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(p))}function c(t){return null===t||91===t||93===t||eG(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),o||(o=!eW(t)),92===t?p:c)}function p(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function ts(e,t,n,r,l,i){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(l),e.consume(t),e.exit(l),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(l),e.consume(n),e.exit(l),e.exit(r),t):(e.enter(i),s(n))}function s(t){return t===o?(e.exit(i),a(o)):null===t?n(t):eG(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eJ(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===o||null===t||eG(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===o||92===t?(e.consume(t),u):u(t)}}function tu(e,t){let n;return function r(l){return eG(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),n=!0,r):eW(l)?eJ(e,r,n?"linePrefix":"lineSuffix")(l):t(l)}}function tc(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let tp={partial:!0,tokenize:function(e,t,n){return function(t){return e$(t)?tu(e,r)(t):n(t)};function r(t){return ts(e,l,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function l(t){return eW(t)?eJ(e,i,"whitespace")(t):i(t)}function i(e){return null===e||eG(e)?t(e):n(e)}}},td={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),eJ(e,l,"linePrefix",5)(t)};function l(t){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?function t(n){return null===n?i(n):eG(n)?e.attempt(tf,t,i)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eG(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function i(n){return e.exit("codeIndented"),t(n)}}},tf={partial:!0,tokenize:function(e,t,n){let r=this;return l;function l(t){return r.parser.lazy[r.now().line]?n(t):eG(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l):eJ(e,i,"linePrefix",5)(t)}function i(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):eG(e)?l(e):n(e)}}},th={name:"setextUnderline",resolveTo:function(e,t){let n,r,l,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),l||"definition"!==e[i][1].type||(l=i);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",l?(e.splice(r,0,["enter",o,t]),e.splice(l+1,0,["exit",e[n][1],t]),e[n][1].end={...e[l][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,l=this;return function(t){var o;let a,s=l.events.length;for(;s--;)if("lineEnding"!==l.events[s][1].type&&"linePrefix"!==l.events[s][1].type&&"content"!==l.events[s][1].type){a="paragraph"===l.events[s][1].type;break}return!l.parser.lazy[l.now().line]&&(l.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eW(n)?eJ(e,i,"lineSuffix")(n):i(n))}(o)):n(t)};function i(r){return null===r||eG(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},tm=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],tg=["pre","script","style","textarea"],ty={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(e1,t,n)}}},tx={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eG(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l):n(t)};function l(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tv={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l)};function l(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tb={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,l=this,i={partial:!0,tokenize:function(e,t,n){let i=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),eW(t)?eJ(e,s,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(l){return l===r?(i++,e.consume(l),t):i>=a?(e.exit("codeFencedFenceSequence"),eW(l)?eJ(e,u,"whitespace")(l):u(l)):n(l)}(t)):n(t)}function u(r){return null===r||eG(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var i=t;let u=l.events[l.events.length-1];return o=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=i,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(l){return l===r?(a++,e.consume(l),t):a<3?n(l):(e.exit("codeFencedFenceSequence"),eW(l)?eJ(e,s,"whitespace")(l):s(l))}(i)};function s(i){return null===i||eG(i)?(e.exit("codeFencedFence"),l.interrupt?t(i):e.check(tv,c,h)(i)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(l){return null===l||eG(l)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(l)):eW(l)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),eJ(e,u,"whitespace")(l)):96===l&&l===r?n(l):(e.consume(l),t)}(i))}function u(t){return null===t||eG(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(l){return null===l||eG(l)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(l)):96===l&&l===r?n(l):(e.consume(l),t)}(t))}function c(t){return e.attempt(i,h,p)(t)}function p(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return o>0&&eW(t)?eJ(e,f,"linePrefix",o+1)(t):f(t)}function f(t){return null===t||eG(t)?e.check(tv,c,h)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eG(n)?(e.exit("codeFlowValue"),f(n)):(e.consume(n),t)}(t))}function h(n){return e.exit("codeFenced"),t(n)}}},tk={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},tw={}.hasOwnProperty;function tS(e){return!!tw.call(tk,e)&&tk[e]}let tN={name:"characterReference",tokenize:function(e,t,n){let r,l,i=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,l=eF,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,l=eV,u):(e.enter("characterReferenceValue"),r=7,l=e_,u(t))}function u(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return l!==eF||tS(i.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return l(a)&&o++<r?(e.consume(a),u):n(a)}}},tj={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eH(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},tE={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),eJ(e,t,"linePrefix")}}};function tC(e,t,n){let r=[],l=-1;for(;++l<e.length;){let i=e[l].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}let tA={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eP(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,l,i,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(l){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(l=o);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[l][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[l-2][1].start}};return i=eL(i=[["enter",s,t],["enter",u,t]],e.slice(r+1,r+a+3)),i=eL(i,[["enter",c,t]]),i=eL(i,tC(t.parser.constructs.insideSpan.null,e.slice(r+a+4,l-3),t)),i=eL(i,[["exit",c,t],e[l-2],e[l-1],["exit",u,t]]),i=eL(i,e.slice(l+1)),i=eL(i,[["exit",s,t]]),eP(e,r,e.length,i),e},tokenize:function(e,t,n){let r,l,i=this,o=i.events.length;for(;o--;)if(("labelImage"===i.events[o][1].type||"labelLink"===i.events[o][1].type)&&!i.events[o][1]._balanced){r=i.events[o][1];break}return function(t){return r?r._inactive?c(t):(l=i.parser.defined.includes(tc(i.sliceSerialize({start:r.end,end:i.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(tT,u,l?u:c)(t):91===t?e.attempt(tD,u,l?s:c)(t):l?u(t):c(t)}function s(t){return e.attempt(tq,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},tT={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return e$(t)?tu(e,l)(t):l(t)}function l(t){return 41===t?u(t):to(e,i,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return e$(t)?tu(e,a)(t):u(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?ts(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return e$(t)?tu(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tD={tokenize:function(e,t,n){let r=this;return function(t){return ta.call(r,e,l,i,"reference","referenceMarker","referenceString")(t)};function l(e){return r.parser.defined.includes(tc(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},tq={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tP={name:"labelStartImage",resolveAll:tA.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),l};function l(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tL(e){return null===e||e$(e)||eY(e)?1:eZ(e)?2:void 0}let tI={name:"attention",resolveAll:function(e,t){let n,r,l,i,o,a,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let p={...e[n][1].end},d={...e[c][1].start};tR(p,-a),tR(d,a),i={type:a>1?"strongSequence":"emphasisSequence",start:p,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},l={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...i.start},end:{...o.end}},e[n][1].end={...i.start},e[c][1].start={...o.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=eL(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=eL(s,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",l,t]]),s=eL(s,tC(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=eL(s,[["exit",l,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=eL(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,eP(e,n-1,c-n+3,s),c=n+s.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,l=this.previous,i=tL(l);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let s=e.exit("attentionSequence"),u=tL(a),c=!u||2===u&&i||r.includes(a),p=!i||2===i&&u||r.includes(l);return s._open=!!(42===n?c:c&&(i||!p)),s._close=!!(42===n?p:p&&(u||!c)),t(a)}(o)}}};function tR(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tz={name:"labelStartLink",resolveAll:tA.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),l};function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tO={42:tn,43:tn,45:tn,48:tn,49:tn,50:tn,51:tn,52:tn,53:tn,54:tn,55:tn,56:tn,57:tn,62:ti},tM={91:{name:"definition",tokenize:function(e,t,n){let r,l=this;return function(t){var r;return e.enter("definition"),r=t,ta.call(l,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function i(t){return(r=tc(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return e$(t)?tu(e,a)(t):a(t)}function a(t){return to(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(tp,u,u)(t)}function u(t){return eW(t)?eJ(e,c,"whitespace")(t):c(t)}function c(i){return null===i||eG(i)?(e.exit("definition"),l.parser.defined.push(r),t(i)):n(i)}}}},tF={[-2]:td,[-1]:td,32:td},tB={35:{name:"headingAtx",resolve:function(e,t){let n,r,l=e.length-2,i=3;return"whitespace"===e[3][1].type&&(i+=2),l-2>i&&"whitespace"===e[l][1].type&&(l-=2),"atxHeadingSequence"===e[l][1].type&&(i===l-1||l-4>i&&"whitespace"===e[l-2][1].type)&&(l-=i+1===l?2:4),l>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[l][1].end},r={type:"chunkText",start:e[i][1].start,end:e[l][1].end,contentType:"text"},eP(e,i,l-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(l){var i;return e.enter("atxHeading"),i=l,e.enter("atxHeadingSequence"),function l(i){return 35===i&&r++<6?(e.consume(i),l):null===i||e$(i)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eG(r)?(e.exit("atxHeading"),t(r)):eW(r)?eJ(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||e$(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(i)):n(i)}(i)}}},42:tt,45:[th,tt],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,l,i,o,a,s=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),l=!0,f):63===o?(e.consume(o),r=3,s.interrupt?t:P):eM(o)?(e.consume(o),i=String.fromCharCode(o),h):n(o)}function c(l){return 45===l?(e.consume(l),r=2,p):91===l?(e.consume(l),r=5,o=0,d):eM(l)?(e.consume(l),r=4,s.interrupt?t:P):n(l)}function p(r){return 45===r?(e.consume(r),s.interrupt?t:P):n(r)}function d(r){let l="CDATA[";return r===l.charCodeAt(o++)?(e.consume(r),o===l.length)?s.interrupt?t:N:d:n(r)}function f(t){return eM(t)?(e.consume(t),i=String.fromCharCode(t),h):n(t)}function h(o){if(null===o||47===o||62===o||e$(o)){let a=47===o,u=i.toLowerCase();return!a&&!l&&tg.includes(u)?(r=1,s.interrupt?t(o):N(o)):tm.includes(i.toLowerCase())?(r=6,a)?(e.consume(o),m):s.interrupt?t(o):N(o):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(o):l?function t(n){return eW(n)?(e.consume(n),t):w(n)}(o):g(o))}return 45===o||eF(o)?(e.consume(o),i+=String.fromCharCode(o),h):n(o)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:N):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||eM(t)?(e.consume(t),y):eW(t)?(e.consume(t),g):w(t)}function y(t){return 45===t||46===t||58===t||95===t||eF(t)?(e.consume(t),y):x(t)}function x(t){return 61===t?(e.consume(t),v):eW(t)?(e.consume(t),x):g(t)}function v(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):eW(t)?(e.consume(t),v):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||e$(n)?x(n):(e.consume(n),t)}(t)}function b(t){return t===a?(e.consume(t),a=null,k):null===t||eG(t)?n(t):(e.consume(t),b)}function k(e){return 47===e||62===e||eW(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||eG(t)?N(t):eW(t)?(e.consume(t),S):n(t)}function N(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),T):62===t&&4===r?(e.consume(t),L):63===t&&3===r?(e.consume(t),P):93===t&&5===r?(e.consume(t),q):eG(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(ty,I,j)(t)):null===t||eG(t)?(e.exit("htmlFlowData"),j(t)):(e.consume(t),N)}function j(t){return e.check(tx,E,I)(t)}function E(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),C}function C(t){return null===t||eG(t)?j(t):(e.enter("htmlFlowData"),N(t))}function A(t){return 45===t?(e.consume(t),P):N(t)}function T(t){return 47===t?(e.consume(t),i="",D):N(t)}function D(t){if(62===t){let n=i.toLowerCase();return tg.includes(n)?(e.consume(t),L):N(t)}return eM(t)&&i.length<8?(e.consume(t),i+=String.fromCharCode(t),D):N(t)}function q(t){return 93===t?(e.consume(t),P):N(t)}function P(t){return 62===t?(e.consume(t),L):45===t&&2===r?(e.consume(t),P):N(t)}function L(t){return null===t||eG(t)?(e.exit("htmlFlowData"),I(t)):(e.consume(t),L)}function I(n){return e.exit("htmlFlow"),t(n)}}},61:th,95:tt,96:tb,126:tb},tU={38:tN,92:tj},t_={[-5]:tE,[-4]:tE,[-3]:tE,33:tP,38:tN,42:tI,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l};function l(t){return eM(t)?(e.consume(t),i):64===t?n(t):a(t)}function i(t){return 43===t||45===t||46===t||eF(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||eF(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eU(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),s):eB(t)?(e.consume(t),a):n(t)}function s(l){return eF(l)?function l(i){return 46===i?(e.consume(i),r=0,s):62===i?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(i),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(i){if((45===i||eF(i))&&r++<63){let n=45===i?t:l;return e.consume(i),n}return n(i)}(i)}(l):n(l)}}},{name:"htmlText",tokenize:function(e,t,n){let r,l,i,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),b):63===t?(e.consume(t),x):eM(t)?(e.consume(t),w):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),l=0,f):eM(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),p):eG(t)?(i=c,D(t)):(e.consume(t),c)}function p(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?T(e):45===e?p(e):c(e)}function f(t){let r="CDATA[";return t===r.charCodeAt(l++)?(e.consume(t),l===r.length?h:f):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):eG(t)?(i=h,D(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?T(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?T(t):eG(t)?(i=y,D(t)):(e.consume(t),y)}function x(t){return null===t?n(t):63===t?(e.consume(t),v):eG(t)?(i=x,D(t)):(e.consume(t),x)}function v(e){return 62===e?T(e):x(e)}function b(t){return eM(t)?(e.consume(t),k):n(t)}function k(t){return 45===t||eF(t)?(e.consume(t),k):function t(n){return eG(n)?(i=t,D(n)):eW(n)?(e.consume(n),t):T(n)}(t)}function w(t){return 45===t||eF(t)?(e.consume(t),w):47===t||62===t||e$(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),T):58===t||95===t||eM(t)?(e.consume(t),N):eG(t)?(i=S,D(t)):eW(t)?(e.consume(t),S):T(t)}function N(t){return 45===t||46===t||58===t||95===t||eF(t)?(e.consume(t),N):function t(n){return 61===n?(e.consume(n),j):eG(n)?(i=t,D(n)):eW(n)?(e.consume(n),t):S(n)}(t)}function j(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,E):eG(t)?(i=j,D(t)):eW(t)?(e.consume(t),j):(e.consume(t),C)}function E(t){return t===r?(e.consume(t),r=void 0,A):null===t?n(t):eG(t)?(i=E,D(t)):(e.consume(t),E)}function C(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||e$(t)?S(t):(e.consume(t),C)}function A(e){return 47===e||62===e||e$(e)?S(e):n(e)}function T(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function D(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),q}function q(t){return eW(t)?eJ(e,P,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):P(t)}function P(t){return e.enter("htmlTextData"),i(t)}}}],91:tz,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eG(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tj],93:tA,95:tI,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,l=3;if(("lineEnding"===e[3][1].type||"space"===e[l][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=l;++t<r;)if("codeTextData"===e[t][1].type){e[l][1].type="codeTextPadding",e[r][1].type="codeTextPadding",l+=2,r-=2;break}}for(t=l-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,l,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),i++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),o):96===s?(l=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(l.type="codeTextData",a(o))}(s)):eG(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(s))}function a(t){return null===t||32===t||96===t||eG(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},tV={null:[tI,e5]},tH={null:[42,95]},tG={null:[]},t$=/[\0\t\n\r]/g;function tW(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tZ=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tY(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tW(n.slice(t?2:1),t?16:10)}return tS(n)||e}let tK={}.hasOwnProperty;function tJ(e){return{line:e.line,column:e.column,offset:e.offset}}function tQ(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+ec({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+ec({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+ec({start:t.start,end:t.end})+") is still open")}function tX(e){let t=this;t.parser=function(n){var r,i;let o,a,s,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(i=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(h),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(f),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:r(f,l),codeText:r(function(){return{type:"inlineCode",value:""}},l),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,l),htmlFlowData:u,htmlText:r(g,l),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:l,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(x,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(x),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:r(h),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tW(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=tS(n);let l=this.stack[this.stack.length-1];l.value+=t},characterReference:function(e){this.stack.pop().position.end=tJ(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tc(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(p),hardBreakTrailing:o(p),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tZ,tY),n.identifier=tc(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tJ(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tc(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};!function e(t,n){let r=-1;for(;++r<n.length;){let l=n[r];Array.isArray(l)?e(t,l):function(e,t){let n;for(n in t)if(tK.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,l)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:i,exit:a,buffer:l,resume:s,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,l,i,o,a=t-1,s=-1,u=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||s||i||(i=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(l=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;l&&(e[l][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",l=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}i&&(!l||i<l)&&(r._spread=!0),r.end=Object.assign({},l?e[l][1].start:t[1].end),e.splice(l||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let l={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=l,e.splice(a,0,["enter",l,t[2]]),a++,n++,i=void 0,o=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tK.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||tQ).call(o,void 0,e[0])}for(r.position={start:tJ(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tJ(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){i.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function i(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tJ(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tQ).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+ec({start:e.start,end:e.end})+"): it’s not open");n.position.end=tJ(e.end)}function s(){var e;return eD(this.stack.pop(),"boolean"!=typeof eT.includeImageAlt||eT.includeImageAlt,"boolean"!=typeof eT.includeHtml||eT.includeHtml)}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tJ(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tJ(e.end)}function p(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function f(){return{type:"code",lang:null,meta:null,value:""}}function h(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function x(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(i)(function(e){for(;!ez(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let r,l=(eO.call(e,n)?e[n]:void 0)||(e[n]={}),i=t[n];if(i)for(r in i){eO.call(l,r)||(l[r]=[]);let e=i[r];!function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eP(e,0,0,r)}(l[r],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}([l,...(e||{}).extensions||[]]),content:n(eQ),defined:[],document:n(eX),flow:n(e3),lazy:{},string:n(e6),text:n(e9)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},l={},i=[],o=[],a=[],s={attempt:h(function(e,t){m(e,t.from)}),check:h(f),consume:function(e){eG(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:h(f,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){l[e.line]=e.column,g()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,l=[];for(;++r<e.length;){let i,o=e[r];if("string"==typeof o)i=o;else switch(o){case -5:i="\r";break;case -4:i="\n";break;case -3:i="\r\n";break;case -2:i=t?" ":"	";break;case -1:if(!t&&n)continue;i=" ";break;default:i=String.fromCharCode(o)}n=-2===o,l.push(i)}return l.join("")}(p(e),t)},sliceStream:p,write:function(e){return(o=eL(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),u.events=tC(i,u.events,u),u.events)}},c=t.tokenize.call(u,s);return t.resolveAll&&i.push(t),u;function p(e){return function(e,t){let n,r=t.start._index,l=t.start._bufferIndex,i=t.end._index,o=t.end._bufferIndex;if(r===i)n=[e[r].slice(l,o)];else{if(n=e.slice(r,i),l>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(l):n.shift()}o>0&&n.push(e[i].slice(0,o))}return n}(o,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:l,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:l,offset:i}}function f(e,t){t.restore()}function h(e,t){return function(n,l,i){var o;let c,p,f,h;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,p=0,0===e.length)?i:y(e[p])}function y(e){return function(n){return(h=function(){let e=d(),t=u.previous,n=u.currentConstruct,l=u.events.length,i=Array.from(a);return{from:l,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=l,a=i,g()}}}(),f=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?v(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,x,v)(n)}}function x(t){return e(f,h),l}function v(e){return(h.restore(),++p<c.length)?y(c[p]):i}}}function m(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&eP(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in l&&r.column<2&&(r.column=l[r.line],r.offset+=l[r.line]-1)}}(t,e,n)}}})(i).document().write((a=1,s="",u=!0,function(e,t,n){let r,l,i,c,p,d=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),i=0,s="",u&&(65279===e.charCodeAt(0)&&i++,u=void 0);i<e.length;){if(t$.lastIndex=i,c=(r=t$.exec(e))&&void 0!==r.index?r.index:e.length,p=e.charCodeAt(c),!r){s=e.slice(i);break}if(10===p&&i===c&&o)d.push(-3),o=void 0;else switch(o&&(d.push(-5),o=void 0),i<c&&(d.push(e.slice(i,c)),a+=c-i),p){case 0:d.push(65533),a++;break;case 9:for(l=4*Math.ceil(a/4),d.push(-2);a++<l;)d.push(-1);break;case 10:d.push(-4),a=1;break;default:o=!0,a=1}i=c+1}return n&&(o&&d.push(-5),s&&d.push(s),d.push(null)),d})(n,r,!0))))}}let t0="object"==typeof self?self:globalThis,t1=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=l=>{if(e.has(l))return e.get(l);let[i,o]=t[l];switch(i){case 0:case -1:return n(o,l);case 1:{let e=n([],l);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},l);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),l);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),l)}case 5:{let e=n(new Map,l);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,l);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new t0[e](t),l)}case 8:return n(BigInt(o),l);case"BigInt":return n(Object(BigInt(o)),l);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new t0[i](o),l)};return r},t2=e=>t1(new Map,e)(0),{toString:t4}={},{keys:t3}=Object,t5=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=t4.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},t6=([e,t])=>0===e&&("function"===t||"symbol"===t),t9=(e,t,n,r)=>{let l=(e,t)=>{let l=r.push(e)-1;return n.set(t,l),l},i=r=>{if(n.has(r))return n.get(r);let[o,a]=t5(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return l([-1],r)}return l([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),l([a,[...e]],r)}let e=[],t=l([o,e],r);for(let t of r)e.push(i(t));return t}case 2:{if(a)switch(a){case"BigInt":return l([a,r.toString()],r);case"Boolean":case"Number":case"String":return l([a,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());let n=[],s=l([o,n],r);for(let t of t3(r))(e||!t6(t5(r[t])))&&n.push([i(t),i(r[t])]);return s}case 3:return l([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return l([o,{source:e,flags:t}],r)}case 5:{let t=[],n=l([o,t],r);for(let[n,l]of r)(e||!(t6(t5(n))||t6(t5(l))))&&t.push([i(n),i(l)]);return n}case 6:{let t=[],n=l([o,t],r);for(let n of r)(e||!t6(t5(n)))&&t.push(i(n));return n}}let{message:s}=r;return l([o,{name:a,message:s}],r)};return i},t7=(e,{json:t,lossy:n}={})=>{let r=[];return t9(!(t||n),!!t,new Map,r)(e),r},t8="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?t2(t7(e,t)):structuredClone(e):(e,t)=>t2(t7(e,t));function ne(e){let t=[],n=-1,r=0,l=0;for(;++n<e.length;){let i=e.charCodeAt(n),o="";if(37===i&&eF(e.charCodeAt(n+1))&&eF(e.charCodeAt(n+2)))l=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(o=String.fromCharCode(i));else if(i>55295&&i<57344){let t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(o=String.fromCharCode(i,t),l=1):o="�"}else o=String.fromCharCode(i);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+l+1,o=""),l&&(n+=l,l=0)}return t.join("")+e.slice(r)}function nt(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function nn(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let nr=function(e){var t,n;if(null==e)return ni;if("function"==typeof e)return nl(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=nr(e[n]);return nl(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,nl(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,nl(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function nl(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function ni(){return!0}let no=[];function na(e,t,n,r){let l,i,o,a;"function"==typeof t&&"function"!=typeof n?(i=void 0,o=t,l=n):(i=t,o=n,l=r);var s=i,u=function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return o(e,r,n)},c=l;"function"==typeof s&&"function"!=typeof u?(c=u,u=s):a=s;let p=nr(a),d=c?-1:1;(function e(t,n,r){let l=t&&"object"==typeof t?t:{};if("string"==typeof l.type){let e="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0;Object.defineProperty(i,"name",{value:"node (\x1b[33m"+t.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return i;function i(){var l;let i,o,a,f=no;if((!s||p(t,n,r[r.length-1]||void 0))&&!1===(f=Array.isArray(l=u(t,r))?l:"number"==typeof l?[!0,l]:null==l?no:[l])[0])return f;if("children"in t&&t.children&&t.children&&"skip"!==f[0])for(o=(c?t.children.length:-1)+d,a=r.concat(t);o>-1&&o<t.children.length;){if(!1===(i=e(t.children[o],o,a)())[0])return i;o="number"==typeof i[1]?i[1]:o+d}return f}})(e,void 0,[])()}function ns(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let l=e.all(t),i=l[0];i&&"text"===i.type?i.value="["+i.value:l.unshift({type:"text",value:"["});let o=l[l.length-1];return o&&"text"===o.type?o.value+=r:l.push({type:"text",value:r}),l}function nu(e){let t=e.spread;return null==t?e.children.length>1:t}function nc(e,t,n){let r=0,l=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(l-1);for(;9===t||32===t;)l--,t=e.codePointAt(l-1)}return l>r?e.slice(r,l):""}let np={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let l={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(l.data={meta:t.meta}),e.patch(t,l),l={type:"element",tagName:"pre",properties:{},children:[l=e.applyData(t,l)]},e.patch(t,l),l},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",l=String(t.identifier).toUpperCase(),i=ne(l.toLowerCase()),o=e.footnoteOrder.indexOf(l),a=e.footnoteCounts.get(l);void 0===a?(a=0,e.footnoteOrder.push(l),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(l,a);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+i,id:r+"fnref-"+i+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return ns(e,t);let l={src:ne(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(l.title=r.title);let i={type:"element",tagName:"img",properties:l,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){let n={src:ne(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return ns(e,t);let l={href:ne(r.url||"")};null!==r.title&&void 0!==r.title&&(l.title=r.title);let i={type:"element",tagName:"a",properties:l,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){let n={href:ne(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),l=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=nu(n[r])}return t}(n):nu(t),i={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(l||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||l?o.push(e):o.push(...e.children)}let s=r[r.length-1];s&&(l||"element"!==s.type||"p"!==s.tagName)&&o.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:i,children:o};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),l=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++l<r.length;){let e=r[l];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),l=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),l.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=es(t.children[1]),o=ea(t.children[t.children.length-1]);i&&o&&(r.position={start:i,end:o}),l.push(r)}let i={type:"element",tagName:"table",properties:{},children:e.wrap(l,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,l=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,o=i?i.length:t.children.length,a=-1,s=[];for(;++a<o;){let n=t.children[a],r={},o=i?i[a]:void 0;o&&(r.align=o);let u={type:"element",tagName:l,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),l=0,i=[];for(;r;)i.push(nc(t.slice(l,r.index),l>0,!0),r[0]),l=r.index+r[0].length,r=n.exec(t);return i.push(nc(t.slice(l),l>0,!1)),i.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:nd,yaml:nd,definition:nd,footnoteDefinition:nd};function nd(){}let nf={}.hasOwnProperty,nh={};function nm(e,t){e.position&&(t.position=function(e){let t=es(e),n=ea(e);if(t&&n)return{start:t,end:n}}(e))}function ng(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,l=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&l&&Object.assign(n.properties,t8(l)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ny(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nx(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nv(e,t){let n=function(e,t){let n=t||nh,r=new Map,l=new Map,i={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let l=i.one(n[r],e);if(l){if(r&&"break"===n[r-1].type&&(Array.isArray(l)||"text"!==l.type||(l.value=nx(l.value)),!Array.isArray(l)&&"element"===l.type)){let e=l.children[0];e&&"text"===e.type&&(e.value=nx(e.value))}Array.isArray(l)?t.push(...l):t.push(l)}}}return t},applyData:ng,definitionById:r,footnoteById:l,footnoteCounts:new Map,footnoteOrder:[],handlers:{...np,...n.handlers},one:function(e,t){let n=e.type,r=i.handlers[n];if(nf.call(i.handlers,n)&&r)return r(i,e,t);if(i.options.passThrough&&i.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=t8(n);return r.children=i.all(e),r}return t8(e)}return(i.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(nf.call(n,"hProperties")||nf.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(i,e,t)},options:n,patch:nm,wrap:ny};return na(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:l,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),i}(e,t),r=n.one(e,void 0),l=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||nt,r=e.options.footnoteBackLabel||nn,l=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],s=-1;for(;++s<e.footnoteOrder.length;){let l=e.footnoteById.get(e.footnoteOrder[s]);if(!l)continue;let i=e.all(l),o=String(l.identifier).toUpperCase(),u=ne(o.toLowerCase()),c=0,p=[],d=e.footnoteCounts.get(o);for(;void 0!==d&&++c<=d;){p.length>0&&p.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),p.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=i[i.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...p)}else i.push(...p);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(i,!0)};e.patch(l,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...t8(o),id:"footnote-label"},children:[{type:"text",value:l}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return l&&i.children.push({type:"text",value:"\n"},l),i}function nb(e,t){return e&&"run"in e?async function(n,r){let l=nv(n,{file:r,...t});await e.run(l,r)}:function(n,r){return nv(n,{file:r,...e||t})}}function nk(e){if(e)throw e}var nw=n(19566);function nS(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let nN=require("node:path"),nj=require("node:process");function nE(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nC=require("node:url"),nA=["history","path","basename","stem","extname","dirname"];class nT{constructor(e){let t,n;t=e?nE(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":nj.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nA.length;){let e=nA[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nA.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?nN.basename(this.path):void 0}set basename(e){nq(e,"basename"),nD(e,"basename"),this.path=nN.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?nN.dirname(this.path):void 0}set dirname(e){nP(this.basename,"dirname"),this.path=nN.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?nN.extname(this.path):void 0}set extname(e){if(nD(e,"extname"),nP(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=nN.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nE(e)&&(e=(0,nC.fileURLToPath)(e)),nq(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?nN.basename(this.path,this.extname):void 0}set stem(e){nq(e,"stem"),nD(e,"stem"),this.path=nN.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new eh(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nD(e,t){if(e&&e.includes(nN.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+nN.sep+"`")}function nq(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nP(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let nL=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nI={}.hasOwnProperty;class nR extends nL{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function l(i,...o){let a=e[++n],s=-1;if(i)return void r(i);for(;++s<t.length;)(null===o[s]||void 0===o[s])&&(o[s]=t[s]);t=o,a?(function(e,t){let n;return function(...t){let i,o=e.length>t.length;o&&t.push(r);try{i=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(i&&i.then&&"function"==typeof i.then?i.then(l,r):i instanceof Error?r(i):l(i))};function r(e,...l){n||(n=!0,t(e,...l))}function l(e){r(null,e)}})(a,l)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nR,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(nw(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nF("data",this.frozen),this.namespace[e]=t,this):nI.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nF("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=n_(e),n=this.parser||this.Parser;return nO("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nO("process",this.parser||this.Parser),nM("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,l){let i=n_(e),o=n.parse(i);function a(e,n){e||!n?l(e):r?r(n):t(void 0,n)}n.run(o,i,function(e,t,r){var l,i;if(e||!t||!r)return a(e);let o=n.stringify(t,r);"string"==typeof(l=o)||(i=l)&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=o:r.result=o,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nO("processSync",this.parser||this.Parser),nM("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,nk(e),t=r}),nU("processSync","process",n),t}run(e,t,n){nB(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?l(void 0,n):new Promise(l);function l(l,i){let o=n_(t);r.run(e,o,function(t,r,o){let a=r||e;t?i(t):l?l(a):n(void 0,a,o)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){nk(e),n=t,r=!0}),nU("runSync","run",r),n}stringify(e,t){this.freeze();let n=n_(t),r=this.compiler||this.Compiler;return nM("stringify",r),nB(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nF("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?i(e):l(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function l(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");i(e.plugins),e.settings&&(r.settings=nw(!0,r.settings,e.settings))}function i(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)o(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;o(e,t)}else l(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,l=-1;for(;++r<n.length;)if(n[r][0]===e){l=r;break}if(-1===l)n.push([e,...t]);else if(t.length>0){let[r,...i]=t,o=n[l][1];nS(o)&&nS(r)&&(r=nw(!0,o,r)),n[l]=[e,r,...i]}}}}let nz=new nR().freeze();function nO(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nM(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nF(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nB(e){if(!nS(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nU(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function n_(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new nT(e)}let nV=[],nH={allowDangerousHtml:!0},nG=/^(https?|ircs?|mailto|xmpp)$/i,n$=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nW(e){let t=function(e){let t=e.rehypePlugins||nV,n=e.remarkPlugins||nV,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nH}:nH;return nz().use(tX).use(n).use(nb,r).use(t)}(e),n=function(e){let t=e.children||"",n=new nT;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,l=t.components,o=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,u=t.urlTransform||nZ;for(let e of n$)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return na(e,function(e,t,l){if("raw"===e.type&&l&&"number"==typeof t)return a?l.children.splice(t,1):l.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in eA)if(Object.hasOwn(eA,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=eA[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=u(String(n||""),t,e))}}if("element"===e.type){let i=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!i&&r&&"number"==typeof t&&(i=!r(e,t,l)),i&&l&&"number"==typeof t)return s&&e.children?l.children.splice(t,1,...e.children):l.children.splice(t,1),t}}),function(e,t){var n,r,l,i,o;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let s=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=s,r=t.jsxDEV,a=function(e,t,l,i){let o=Array.isArray(l.children),a=es(e);return r(t,l,i,o,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");l=0,i=t.jsx,o=t.jsxs,a=function(e,t,n,r){let l=Array.isArray(n.children)?o:i;return r?l(t,n,r):l(t,n)}}let u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:s,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?X:Q,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=ek(u,e,void 0);return c&&"string"!=typeof c?c:u.create(e,u.Fragment,{children:c||void 0},void 0)}(e,{Fragment:i.Fragment,components:l,ignoreInvalidStyle:!0,jsx:i.jsx,jsxs:i.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nZ(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),l=e.indexOf("/");return -1===t||-1!==l&&t>l||-1!==n&&t>n||-1!==r&&t>r||nG.test(e.slice(0,t))?e:""}function nY(){var e;(0,a.useParams)();let t=(0,a.useRouter)(),[n,r]=(0,o.useState)(null),[l,h]=(0,o.useState)(!0),[b,w]=(0,o.useState)(0),[S,N]=(0,o.useState)(!1),[j,E]=(0,o.useState)(!1),[C,A]=(0,o.useState)(null),[T,D]=(0,o.useState)(!1),q=(0,o.useRef)(null),P=async(e,t)=>{console.log("Feedback:",{type:e,rating:t||C}),D(!0)},L=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",I=async()=>{if(!n?.id){console.error("No match ID available"),alert("匹配ID不存在，请刷新页面重试");return}try{console.log("Sending like request for match:",n.id),console.log("Match object:",n);let e=await fetch(`/api/matches/${n.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!0})});console.log("Response status:",e.status);let r=await e.json();console.log("Response data:",r),e.ok?(console.log("Successfully liked match:",n.id),t.push("/dashboard")):(console.error("Failed to like match:",r),alert("操作失败，请重试"))}catch(e){console.error("Error liking match:",e),alert("网络错误，请重试")}},R=async()=>{if(n?.id)try{console.log("Sending pass request for match:",n.id);let e=await fetch(`/api/matches/${n.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!1})});console.log("Response status:",e.status);let r=await e.json();console.log("Response data:",r),e.ok?(console.log("Successfully passed match:",n.id),t.push("/dashboard")):(console.error("Failed to pass match:",r),alert("操作失败，请重试"))}catch(e){console.error("Error passing match:",e),alert("网络错误，请重试")}};if(l)return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,i.jsx)("p",{children:"加载匹配详情..."})]})});if(!n)return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"未找到匹配信息"}),(0,i.jsx)(k(),{href:"/dashboard",children:(0,i.jsx)(s.$,{children:"返回首页"})})]})});let z=n.conversationSimulation?.conversation||[],O=n.conversationSimulation?.analysis||{},M=j?z:z.slice(0,b+1);return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50",children:[(0,i.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,i.jsxs)(k(),{href:"/dashboard",className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:[(0,i.jsx)(p.A,{className:"w-5 h-5"}),"返回"]}),(0,i.jsx)("h1",{className:"text-lg font-semibold",children:"匹配详情"}),(0,i.jsx)("div",{className:"w-16"})," "]})})}),(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,i.jsxs)(u.Zp,{className:"overflow-hidden",children:[(0,i.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl",children:n.otherUser.avatar}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:n.otherUser.name}),(0,i.jsxs)("p",{className:"text-pink-100",children:[n.otherUser.age,"岁 \xb7 ",n.otherUser.location]})]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("div",{className:"text-3xl font-bold",children:[n.compatibilityScore,"%"]}),(0,i.jsx)("div",{className:"text-pink-100",children:(e=n.compatibilityScore)>=80?"高度匹配":e>=60?"中等匹配":"低度匹配"})]})]})}),(0,i.jsxs)(u.Wu,{className:"p-6",children:[(0,i.jsx)("p",{className:"text-gray-700 mb-4",children:n.otherUser.bio}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"兴趣爱好"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:n.otherUser.interests.map((e,t)=>(0,i.jsx)(c.E,{variant:"secondary",className:"text-sm",children:e},t))})]}),"pending"===n.status&&(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsxs)(s.$,{variant:"outline",size:"lg",onClick:R,className:"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600",children:[(0,i.jsx)(d.A,{className:"w-5 h-5"}),"跳过"]}),(0,i.jsxs)(s.$,{size:"lg",onClick:I,className:"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700",children:[(0,i.jsx)(f.A,{className:"w-5 h-5"}),"喜欢"]})]}),"mutual_like"===n.status&&(0,i.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-red-50 border border-pink-200 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-pink-700",children:[(0,i.jsx)(f.A,{className:"w-5 h-5 fill-current"}),(0,i.jsx)("span",{className:"font-medium",children:"你们互相喜欢！"})]}),(0,i.jsx)("p",{className:"text-center text-sm text-pink-600 mt-2",children:"可以开始联系对方了 \uD83D\uDC95"})]}),"passed"===n.status&&(0,i.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-gray-600",children:[(0,i.jsx)(d.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"font-medium",children:"已跳过此匹配"})]})}),"liked"===n.status&&(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-blue-700",children:[(0,i.jsx)(f.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"font-medium",children:"你已表示喜欢"})]}),(0,i.jsx)("p",{className:"text-center text-sm text-blue-600 mt-2",children:"等待对方回应..."})]})]})]}),(0,i.jsxs)(u.Zp,{children:[(0,i.jsxs)(u.aR,{children:[(0,i.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD16"}),"红娘深度分析"]}),(0,i.jsx)(u.BT,{children:"基于双方资料的智能匹配分析"})]}),(0,i.jsxs)(u.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"匹配解析"}),(0,i.jsx)("div",{className:"prose prose-sm max-w-none text-gray-700",children:(0,i.jsx)(nW,{components:{h1:({children:e})=>(0,i.jsx)("h1",{className:"text-lg font-bold mb-2",children:e}),h2:({children:e})=>(0,i.jsx)("h2",{className:"text-base font-semibold mb-2",children:e}),h3:({children:e})=>(0,i.jsx)("h3",{className:"text-sm font-medium mb-1",children:e}),p:({children:e})=>(0,i.jsx)("p",{className:"mb-3 leading-relaxed",children:e}),ul:({children:e})=>(0,i.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1",children:e}),ol:({children:e})=>(0,i.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1",children:e}),li:({children:e})=>(0,i.jsx)("li",{className:"text-sm",children:e}),strong:({children:e})=>(0,i.jsx)("strong",{className:"font-semibold text-gray-800",children:e}),em:({children:e})=>(0,i.jsx)("em",{className:"italic text-gray-600",children:e})},children:n.aiAnalysis?.explanation||"暂无详细分析"})})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-3 text-green-600",children:"匹配优势"}),(0,i.jsx)("ul",{className:"space-y-2",children:n.aiAnalysis?.strengths?.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),e]},t))||(0,i.jsx)("li",{className:"text-sm text-gray-500",children:"暂无数据"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-3 text-yellow-600",children:"注意事项"}),(0,i.jsx)("ul",{className:"space-y-2",children:n.aiAnalysis?.challenges?.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),e]},t))||(0,i.jsx)("li",{className:"text-sm text-gray-500",children:"暂无数据"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-3 text-blue-600",children:"建议话题"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:n.aiAnalysis?.suggestions?.map((e,t)=>(0,i.jsx)(c.E,{variant:"outline",className:"text-blue-600 border-blue-200",children:e},t))||(0,i.jsx)("span",{className:"text-sm text-gray-500",children:"暂无建议话题"})})]})]})]}),n.aiAnalysis?.datePlan&&(0,i.jsxs)(u.Zp,{children:[(0,i.jsxs)(u.aR,{children:[(0,i.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC95"}),"专属约会计划"]}),(0,i.jsx)(u.BT,{children:"AI 为你们量身定制的约会建议"})]}),(0,i.jsxs)(u.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2 text-pink-600",children:"约会主题"}),(0,i.jsx)("p",{className:"text-gray-700",children:n.aiAnalysis.datePlan.theme}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:n.aiAnalysis.datePlan.concept})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-3 text-blue-600",children:"约会安排"}),(0,i.jsx)("div",{className:"space-y-3",children:n.aiAnalysis.datePlan.timeline?.map((e,t)=>(0,i.jsxs)("div",{className:"flex gap-4 p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-blue-600 min-w-[80px]",children:e.time}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("div",{className:"font-medium",children:e.activity}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:e.location}),(0,i.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:e.reason})]})]},t))})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2 text-green-600",children:"推荐地点"}),(0,i.jsx)("ul",{className:"space-y-1",children:n.aiAnalysis.datePlan.recommendations?.locations?.map((e,t)=>(0,i.jsxs)("li",{className:"text-sm text-gray-700 flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),e]},t))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2 text-purple-600",children:"贴心提醒"}),(0,i.jsx)("ul",{className:"space-y-1",children:n.aiAnalysis.datePlan.recommendations?.tips?.map((e,t)=>(0,i.jsxs)("li",{className:"text-sm text-gray-700 flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-1.5 h-1.5 bg-purple-500 rounded-full"}),e]},t))})]})]}),(0,i.jsxs)("div",{className:"flex gap-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"预算建议："}),n.aiAnalysis.datePlan.budget]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"约会时长："}),n.aiAnalysis.datePlan.duration]})]})]})]}),(0,i.jsxs)(u.Zp,{children:[(0,i.jsx)(u.aR,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCAC"}),"对话模拟"]}),(0,i.jsx)(u.BT,{children:"预测你们第一次聊天的场景"})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[!S&&0===b&&(0,i.jsxs)(s.$,{onClick:()=>{n?.conversationSimulation?.conversation&&(N(!0),w(0),E(!1),q.current=setInterval(()=>{w(e=>{let t=e+1;return t>=n.conversationSimulation.conversation.length?(N(!1),E(!0),q.current&&clearInterval(q.current),e):t})},2e3))},variant:"outline",size:"sm",children:[(0,i.jsx)(m,{className:"w-4 h-4 mr-2"}),"播放对话"]}),S&&(0,i.jsxs)(s.$,{onClick:()=>{N(!1),q.current&&clearInterval(q.current)},variant:"outline",size:"sm",children:[(0,i.jsx)(g,{className:"w-4 h-4 mr-2"}),"暂停"]}),b>0&&!j&&(0,i.jsx)(s.$,{onClick:()=>{N(!1),E(!0),q.current&&clearInterval(q.current),w(n?.conversationSimulation?.conversation?.length||0)},variant:"outline",size:"sm",children:"显示全部"})]})]})}),(0,i.jsxs)(u.Wu,{children:[(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto",children:[M.map((e,t)=>{let r=e.speaker===n.otherUser?.name;return(0,i.jsx)("div",{className:`flex ${r?"justify-start":"justify-end"} ${t===b&&S?"animate-pulse":""}`,children:(0,i.jsxs)("div",{className:`max-w-[70%] p-3 rounded-lg transition-all duration-500 ${r?"bg-white border shadow-sm text-gray-800":"bg-gradient-to-r from-blue-500 to-blue-600 text-white"} ${t<=b?"opacity-100 transform translate-y-0":"opacity-0 transform translate-y-4"}`,children:[(0,i.jsx)("div",{className:`text-xs mb-1 ${r?"text-gray-500":"text-blue-100"}`,children:e.speaker}),(0,i.jsx)("div",{className:"text-sm leading-relaxed",children:e.message})]})},t)}),S&&b<z.length&&(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsxs)("div",{className:"flex space-x-1",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),j&&(0,i.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,i.jsx)("h4",{className:"font-medium",children:"对话分析"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-sm",children:"对话流畅度"}),(0,i.jsxs)("span",{className:`text-sm font-medium ${L(O.conversationFlow||0)}`,children:[O.conversationFlow||0,"%"]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-sm",children:"价值观契合"}),(0,i.jsxs)("span",{className:`text-sm font-medium ${L(O.valueAlignment||0)}`,children:[O.valueAlignment||0,"%"]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-sm",children:"沟通匹配度"}),(0,i.jsxs)("span",{className:`text-sm font-medium ${L(O.communicationMatch||0)}`,children:[O.communicationMatch||0,"%"]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-sm",children:"整体兼容性"}),(0,i.jsxs)("span",{className:`text-sm font-medium ${L(O.overallCompatibility||0)}`,children:[O.overallCompatibility||0,"%"]})]})]})]}),O?.commonTopics&&O.commonTopics.length>0&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"text-sm font-medium mb-2",children:"共同话题"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:O.commonTopics.map((e,t)=>(0,i.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},t))})]})]}),j&&(0,i.jsxs)("div",{className:"mt-6 pt-6 border-t",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"这个对话模拟准确吗？"}),T?(0,i.jsx)("div",{className:"text-green-600 text-sm bg-green-50 p-3 rounded-md",children:"✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。"}):(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm",children:"准确度评分："}),(0,i.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(e=>(0,i.jsx)("button",{onClick:()=>A(e),className:`p-1 transition-colors ${C&&C>=e?"text-yellow-500":"text-gray-300 hover:text-yellow-400"}`,children:(0,i.jsx)(y.A,{className:"w-4 h-4 fill-current"})},e))})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsxs)(s.$,{variant:"outline",size:"sm",onClick:()=>P("accurate"),className:"flex items-center gap-2",children:[(0,i.jsx)(x,{className:"w-4 h-4"}),"很准确"]}),(0,i.jsxs)(s.$,{variant:"outline",size:"sm",onClick:()=>P("inaccurate"),className:"flex items-center gap-2",children:[(0,i.jsx)(v,{className:"w-4 h-4"}),"不太准确"]})]})]})]})]})]})]})]})}},53344:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65840:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,l.default)(e),i="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,l=e.value;i?t(r,l,e):l&&((n=n||{})[r]=l)}}),n};var l=r(n(44517))},77473:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},80777:()=>{},84219:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},86908:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,l=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||l.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,s):e.replace(i,s)).replace(r,a))}},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a,metadata:()=>o});var r=n(37413),l=n(7339),i=n.n(l);n(61135);let o={title:"寡佬AI - 智能恋爱匹配平台",description:"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"};function a({children:e}){return(0,r.jsx)("html",{lang:"zh-CN",children:(0,r.jsx)("body",{className:i().className,children:e})})}},96834:(e,t,n)=>{"use strict";n.d(t,{E:()=>a});var r=n(60687);n(43210);var l=n(24224),i=n(4780);let o=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function a({className:e,variant:t,...n}){return(0,r.jsx)("div",{className:(0,i.cn)(o({variant:t}),e),...n})}},97592:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,435,924,814],()=>n(43593));module.exports=r})();