(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:o}=t(39844);e.exports=o("/home/<USER>/workspace/indie/lingxiai-gemini/node_modules/next/dist/client/app-dir/link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13267:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22633:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32440:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33873:e=>{"use strict";e.exports=require("path")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73515:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},73557:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=t(65239),s=t(48088),l=t(88170),n=t.n(l),i=t(30893),a={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>i[e]);t.d(r,a);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78734)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["/home/<USER>/workspace/indie/lingxiai-gemini/src/app/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78734:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ej});var o=t(37413),s=t(4536),l=t.n(s),n=t(61120);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){var s;let e,l,a=(s=t,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,r){let t={...r};for(let o in r){let s=e[o],l=r[o];/^on[A-Z]/.test(o)?s&&l?t[o]=(...e)=>{let r=l(...e);return s(...e),r}:s&&(t[o]=s):"style"===o?t[o]={...s,...l}:"className"===o&&(t[o]=[s,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,o=e.map(e=>{let o=i(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():i(e[r],null)}}}}(r,a):a),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...l}=e,i=n.Children.toArray(s),a=i.find(c);if(a){let e=a.props.children,s=i.map(r=>r!==a?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(r,{...l,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}("Slot"),d=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}function p(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var l=r.length;for(t=0;t<l;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}let u=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,m=e=>{let r=f(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),x(t,r)||b(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},x=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),s=o?x(e.slice(1),o):void 0;if(s)return s;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},h=/^\[(.+)\]$/,b=e=>{if(h.test(e)){let r=h.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},f=e=>{let{theme:r,prefix:t}=e,o={nextPart:new Map,validators:[]};return w(Object.entries(e.classGroups),t).forEach(([e,t])=>{g(t,o,e,r)}),o},g=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:v(r,e)).classGroupId=t;return}if("function"==typeof e)return y(e)?void g(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{g(s,v(r,e),t,o)})})},v=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},y=e=>e.isThemeGetter,w=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,j=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,l)=>{t.set(s,l),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},N=e=>{let{separator:r,experimentalParseClassName:t}=e,o=1===r.length,s=r[0],l=r.length,n=e=>{let t,n=[],i=0,a=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===i){if(c===s&&(o||e.slice(d,d+l)===r)){n.push(e.slice(a,d)),a=d+l;continue}if("/"===c){t=d;continue}}"["===c?i++:"]"===c&&i--}let d=0===n.length?e:e.substring(a),c=d.startsWith("!"),p=c?d.substring(1):d;return{modifiers:n,hasImportantModifier:c,baseClassName:p,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};return t?e=>t({className:e,parseClassName:n}):n},k=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},z=e=>({cache:j(e.cacheSize),parseClassName:N(e),...m(e)}),P=/\s+/,A=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s}=r,l=[],n=e.trim().split(P),i="";for(let e=n.length-1;e>=0;e-=1){let r=n[e],{modifiers:a,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=t(r),u=!!p,m=o(u?c.substring(0,p):c);if(!m){if(!u||!(m=o(c))){i=r+(i.length>0?" "+i:i);continue}u=!1}let x=k(a).join(":"),h=d?x+"!":x,b=h+m;if(l.includes(b))continue;l.push(b);let f=s(m,u);for(let e=0;e<f.length;++e){let r=f[e];l.push(h+r)}i=r+(i.length>0?" "+i:i)}return i};function C(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=I(e))&&(o&&(o+=" "),o+=r);return o}let I=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=I(e[o]))&&(t&&(t+=" "),t+=r);return t},_=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},R=/^\[(?:([a-z-]+):)?(.+)\]$/i,S=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),M=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,G=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,O=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,q=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,W=e=>T(e)||E.has(e)||S.test(e),V=e=>ee(e,"length",er),T=e=>!!e&&!Number.isNaN(Number(e)),Z=e=>ee(e,"number",T),B=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&T(e.slice(0,-1)),H=e=>R.test(e),L=e=>M.test(e),F=new Set(["length","size","percentage"]),K=e=>ee(e,F,et),U=e=>ee(e,"position",et),X=new Set(["image","url"]),J=e=>ee(e,X,es),Q=e=>ee(e,"",eo),Y=()=>!0,ee=(e,r,t)=>{let o=R.exec(e);return!!o&&(o[1]?"string"==typeof r?o[1]===r:r.has(o[1]):t(o[2]))},er=e=>G.test(e)&&!$.test(e),et=()=>!1,eo=e=>O.test(e),es=e=>q.test(e);Symbol.toStringTag;let el=function(e,...r){let t,o,s,l=function(i){return o=(t=z(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,l=n,n(i)};function n(e){let r=o(e);if(r)return r;let l=A(e,t);return s(e,l),l}return function(){return l(C.apply(null,arguments))}}(()=>{let e=_("colors"),r=_("spacing"),t=_("blur"),o=_("brightness"),s=_("borderColor"),l=_("borderRadius"),n=_("borderSpacing"),i=_("borderWidth"),a=_("contrast"),d=_("grayscale"),c=_("hueRotate"),p=_("invert"),u=_("gap"),m=_("gradientColorStops"),x=_("gradientColorStopPositions"),h=_("inset"),b=_("margin"),f=_("opacity"),g=_("padding"),v=_("saturate"),y=_("scale"),w=_("sepia"),j=_("skew"),N=_("space"),k=_("translate"),z=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto",H,r],C=()=>[H,r],I=()=>["",W,V],R=()=>["auto",T,H],S=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],E=()=>["solid","dashed","dotted","double","none"],M=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],G=()=>["start","end","center","between","around","evenly","stretch"],$=()=>["","0",H],O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],q=()=>[T,H];return{cacheSize:500,separator:":",theme:{colors:[Y],spacing:[W,V],blur:["none","",L,H],brightness:q(),borderColor:[e],borderRadius:["none","","full",L,H],borderSpacing:C(),borderWidth:I(),contrast:q(),grayscale:$(),hueRotate:q(),invert:$(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[D,V],inset:A(),margin:A(),opacity:q(),padding:C(),saturate:q(),scale:q(),sepia:$(),skew:q(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...S(),H]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",B,H]}],basis:[{basis:A()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:$()}],shrink:[{shrink:$()}],order:[{order:["first","last","none",B,H]}],"grid-cols":[{"grid-cols":[Y]}],"col-start-end":[{col:["auto",{span:["full",B,H]},H]}],"col-start":[{"col-start":R()}],"col-end":[{"col-end":R()}],"grid-rows":[{"grid-rows":[Y]}],"row-start-end":[{row:["auto",{span:[B,H]},H]}],"row-start":[{"row-start":R()}],"row-end":[{"row-end":R()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[u]}],"gap-x":[{"gap-x":[u]}],"gap-y":[{"gap-y":[u]}],"justify-content":[{justify:["normal",...G()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...G(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...G(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,r]}],"min-w":[{"min-w":[H,r,"min","max","fit"]}],"max-w":[{"max-w":[H,r,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[H,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,r,"auto","min","max","fit"]}],"font-size":[{text:["base",L,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Z]}],"font-family":[{font:[Y]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",T,Z]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",W,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...E(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",W,V]}],"underline-offset":[{"underline-offset":["auto",W,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...S(),U]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",K]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},J]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...E(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:E()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...E()]}],"outline-offset":[{"outline-offset":[W,H]}],"outline-w":[{outline:[W,V]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[W,V]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,Q]}],"shadow-color":[{shadow:[Y]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...M(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":M()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",L,H]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[p]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:q()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:q()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[B,H]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[W,V,Z]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function en(...e){return el(p(e))}let ei=((e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return p(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:l}=r,n=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],o=null==l?void 0:l[e];if(null===r)return null;let n=u(r)||u(o);return s[e][n]}),i=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return p(e,n,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...i}[r]):({...l,...i})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ea=n.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...l},n)=>(0,o.jsx)(s?a:"button",{className:en(ei({variant:r,size:t,className:e})),ref:n,...l}));ea.displayName="Button";let ed=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:en("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ed.displayName="Card";let ec=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:en("flex flex-col space-y-1.5 p-6",e),...r}));ec.displayName="CardHeader";let ep=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:en("text-2xl font-semibold leading-none tracking-tight",e),...r}));ep.displayName="CardTitle";let eu=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:en("text-sm text-muted-foreground",e),...r}));eu.displayName="CardDescription",n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:en("p-6 pt-0",e),...r})).displayName="CardContent",n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:en("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";var em={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ex=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var eh=(e,r)=>{let t=(0,n.forwardRef)(({color:t="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:l,children:i,...a},d)=>(0,n.createElement)("svg",{ref:d,...em,width:o,height:o,stroke:t,strokeWidth:l?24*Number(s)/Number(o):s,className:`lucide lucide-${ex(e)}`,...a},[...r.map(([e,r])=>(0,n.createElement)(e,r)),...(Array.isArray(i)?i:[i])||[]]));return t.displayName=`${e}`,t};let eb=eh("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ef=eh("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),eg=eh("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),ev=eh("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),ey=eh("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),ew=eh("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);function ej(){return(0,o.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50",children:[(0,o.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(eb,{className:"w-8 h-8 text-pink-600"}),(0,o.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent",children:"寡佬AI"})]}),(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsx)(l(),{href:"/auth/login",children:(0,o.jsx)(ea,{variant:"outline",children:"登录"})}),(0,o.jsx)(l(),{href:"/auth/register",children:(0,o.jsx)(ea,{children:"注册"})})]})]})})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,o.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["用AI找到你的",(0,o.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent",children:"灵魂伴侣"})]}),(0,o.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"基于先进的AI技术，深度分析性格特征和价值观，为您精准匹配最合适的伴侣。 不再是简单的外表匹配，而是真正的心灵契合。"}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,o.jsx)(l(),{href:"/auth/register",children:(0,o.jsx)(ea,{size:"lg",className:"bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700",children:"开始寻找真爱"})}),(0,o.jsx)(ea,{size:"lg",variant:"outline",children:"了解更多"})]})]})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8 bg-white/50",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"为什么选择寡佬AI？"}),(0,o.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"我们不只是另一个交友应用，而是您找到真爱的智能助手"})]}),(0,o.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(ef,{className:"w-12 h-12 text-blue-600 mb-4"}),(0,o.jsx)(ep,{children:"AI深度分析"}),(0,o.jsx)(eu,{children:"基于Gemini AI技术，深度分析用户的性格特征、价值观和生活方式， 生成详细的人格画像"})]})}),(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(eg,{className:"w-12 h-12 text-green-600 mb-4"}),(0,o.jsx)(ep,{children:"对话模拟"}),(0,o.jsx)(eu,{children:"AI模拟你们的第一次对话，预测沟通风格和话题契合度， 让你提前了解彼此的交流方式"})]})}),(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(eb,{className:"w-12 h-12 text-pink-600 mb-4"}),(0,o.jsx)(ep,{children:"精准匹配"}),(0,o.jsx)(eu,{children:"综合考虑性格互补、价值观一致、兴趣重叠等多个维度， 计算出科学的兼容性分数"})]})}),(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(ev,{className:"w-12 h-12 text-purple-600 mb-4"}),(0,o.jsx)(ep,{children:"隐私保护"}),(0,o.jsx)(eu,{children:"双向确认机制，只有互相喜欢才能开始聊天， 保护用户隐私和安全"})]})}),(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(ey,{className:"w-12 h-12 text-yellow-600 mb-4"}),(0,o.jsx)(ep,{children:"持续学习"}),(0,o.jsx)(eu,{children:"AI会根据用户反馈不断学习和优化， 提供越来越准确的匹配建议"})]})}),(0,o.jsx)(ed,{className:"border-0 shadow-lg hover:shadow-xl transition-shadow",children:(0,o.jsxs)(ec,{children:[(0,o.jsx)(ew,{className:"w-12 h-12 text-indigo-600 mb-4"}),(0,o.jsx)(ep,{children:"真实用户"}),(0,o.jsx)(eu,{children:"严格的用户验证机制，确保平台上都是真实、 认真寻找伴侣的用户"})]})})]})]})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"如何开始？"}),(0,o.jsx)("p",{className:"text-lg text-gray-600",children:"简单三步，开启你的智能恋爱之旅"})]}),(0,o.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4",children:"1"}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"完善资料"}),(0,o.jsx)("p",{className:"text-gray-600",children:"详细填写个人信息、兴趣爱好和理想伴侣要求， 让AI更好地了解你"})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4",children:"2"}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"AI分析匹配"}),(0,o.jsx)("p",{className:"text-gray-600",children:"AI深度分析你的人格特征，为你推荐最合适的潜在伴侣， 并提供详细的匹配分析"})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4",children:"3"}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"开始聊天"}),(0,o.jsx)("p",{className:"text-gray-600",children:"双方互相喜欢后即可开始聊天， 基于AI建议的话题开启美好的对话"})]})]})]})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-blue-600",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"准备好找到你的灵魂伴侣了吗？"}),(0,o.jsx)("p",{className:"text-xl text-pink-100 mb-8",children:"加入数千名用户，让AI帮你找到真正合适的人"}),(0,o.jsx)(l(),{href:"/auth/register",children:(0,o.jsx)(ea,{size:"lg",className:"bg-white text-pink-600 hover:bg-gray-100",children:"立即开始"})})]})}),(0,o.jsx)("footer",{className:"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"grid md:grid-cols-4 gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,o.jsx)(eb,{className:"w-6 h-6 text-pink-600"}),(0,o.jsx)("h3",{className:"text-lg font-bold",children:"寡佬AI"})]}),(0,o.jsx)("p",{className:"text-gray-400",children:"用AI技术重新定义恋爱匹配， 为每个人找到最合适的伴侣。"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"font-semibold mb-4",children:"产品"}),(0,o.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"功能介绍"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"定价"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"安全保障"})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"font-semibold mb-4",children:"支持"}),(0,o.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"帮助中心"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"联系我们"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"用户反馈"})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"font-semibold mb-4",children:"法律"}),(0,o.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"隐私政策"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"服务条款"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"#",className:"hover:text-white",children:"Cookie政策"})})]})]})]}),(0,o.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,o.jsx)("p",{children:"\xa9 2024 寡佬AI. 保留所有权利。"})})]})})]})}},80777:()=>{},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>n});var o=t(37413),s=t(7339),l=t.n(s);t(61135);let n={title:"寡佬AI - 智能恋爱匹配平台",description:"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"};function i({children:e}){return(0,o.jsx)("html",{lang:"zh-CN",children:(0,o.jsx)("body",{className:l().className,children:e})})}},97592:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,435,814],()=>t(73557));module.exports=o})();