(()=>{var e={};e.id=865,e.ids=[865],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,n)=>{"use strict";n.d(t,{cn:()=>s});var r=n(49384),a=n(82348);function s(...e){return(0,a.QP)((0,r.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14163:(e,t,n)=>{"use strict";n.d(t,{hO:()=>l,sG:()=>o});var r=n(43210),a=n(51215),s=n(8730),i=n(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,s.TL)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?n:t,{...s,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19377:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(43210),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var i=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,children:c,...d},u)=>(0,r.createElement)("svg",{ref:u,...a,width:i,height:i,stroke:n,strokeWidth:l?24*Number(o)/Number(i):o,className:`lucide lucide-${s(e)}`,...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...(Array.isArray(c)?c:[c])||[]]));return n.displayName=`${e}`,n}},22633:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,n)=>{"use strict";n.d(t,{$:()=>c});var r=n(60687),a=n(43210),s=n(8730),i=n(24224),o=n(4780);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:n,asChild:a=!1,...i},c)=>{let d=a?s.DX:"button";return(0,r.jsx)(d,{className:(0,o.cn)(l({variant:t,size:n,className:e})),ref:c,...i})});c.displayName="Button"},29777:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},32440:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},33873:e=>{"use strict";e.exports=require("path")},35793:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var r=n(65239),a=n(48088),s=n(88170),i=n.n(s),o=n(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let c={children:["",{children:["matches",{children:["matrix",{children:["[requestId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,36073)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/matches/matrix/[requestId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/home/<USER>/workspace/indie/lingxiai-gemini/src/app/matches/matrix/[requestId]/page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/matches/matrix/[requestId]/page",pathname:"/matches/matrix/[requestId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36073:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/matches/matrix/[requestId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/matches/matrix/[requestId]/page.tsx","default")},36133:(e,t,n)=>{Promise.resolve().then(n.bind(n,36073))},44493:(e,t,n)=>{"use strict";n.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o});var r=n(60687),a=n(43210),s=n(4780);let i=a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("h3",{ref:n,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("p",{ref:n,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,s.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},45348:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>nr});var r,a,s,i=n(60687),o=n(43210),l=n.t(o,2),c=n(16189),d=n(44493),u=n(29523),f=n(96834);function m(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function p(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let a=o.createContext(r),s=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,c=n?.[e]?.[s]||a,d=o.useMemo(()=>l,Object.values(l));return(0,i.jsx)(c.Provider,{value:d,children:r})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[s]||a,c=o.useContext(l);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let a=n(e)[`__scope${r}`];return{...t,...a}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var h=n(98599),v=n(8730),x=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=y(t),a=r>=0?r:n+r;return a<0||a>=n?-1:a}(e,t);return -1===n?void 0:e[n]}function y(e){return e!=e||0===e?0:Math.trunc(e)}var b=globalThis?.document?o.useLayoutEffect:()=>{},w=l[" useId ".trim().toString()]||(()=>void 0),j=0;function N(e){let[t,n]=o.useState(w());return b(()=>{e||n(e=>e??String(j++))},[e]),e||(t?`radix-${t}`:"")}var E=n(14163);function C(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var R=l[" useInsertionEffect ".trim().toString()]||b;function k({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,s,i]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),s=o.useRef(t);return R(()=>{s.current=t},[t]),o.useEffect(()=>{a.current!==n&&(s.current?.(n),a.current=n)},[n,a]),[n,r,s]}({defaultProp:t,onChange:n}),l=void 0!==e,c=l?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[c,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&i.current?.(n)}else s(t)},[l,e,s,i])]}Symbol("RADIX:SYNC_STATE");var A=o.createContext(void 0);function S(e){let t=o.useContext(A);return e||t||"ltr"}var P="rovingFocusGroup.onEntryFocus",M={bubbles:!1,cancelable:!0},I="RovingFocusGroup",[D,T,O]=function(e){let t=e+"CollectionProvider",[n,r]=p(t),[a,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=o.useRef(null),s=o.useRef(new Map).current;return(0,i.jsx)(a,{scope:t,itemMap:s,collectionRef:r,children:n})};l.displayName=t;let c=e+"CollectionSlot",d=(0,v.TL)(c),u=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,a=s(c,n),o=(0,h.s)(t,a.collectionRef);return(0,i.jsx)(d,{ref:o,children:r})});u.displayName=c;let f=e+"CollectionItemSlot",m="data-radix-collection-item",x=(0,v.TL)(f),g=o.forwardRef((e,t)=>{let{scope:n,children:r,...a}=e,l=o.useRef(null),c=(0,h.s)(t,l),d=s(f,n);return o.useEffect(()=>(d.itemMap.set(l,{ref:l,...a}),()=>void d.itemMap.delete(l))),(0,i.jsx)(x,{...{[m]:""},ref:c,children:r})});return g.displayName=f,[{Provider:l,Slot:u,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(I),[L,F]=p(I,[O]),[_,$]=L(I),W=o.forwardRef((e,t)=>(0,i.jsx)(D.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(D.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(G,{...e,ref:t})})}));W.displayName=I;var G=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:s,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...p}=e,v=o.useRef(null),x=(0,h.s)(t,v),g=S(s),[y,b]=k({prop:l,defaultProp:c??null,onChange:d,caller:I}),[w,j]=o.useState(!1),N=C(u),R=T(n),A=o.useRef(!1),[D,O]=o.useState(0);return o.useEffect(()=>{let e=v.current;if(e)return e.addEventListener(P,N),()=>e.removeEventListener(P,N)},[N]),(0,i.jsx)(_,{scope:n,orientation:r,dir:g,loop:a,currentTabStopId:y,onItemFocus:o.useCallback(e=>b(e),[b]),onItemShiftTab:o.useCallback(()=>j(!0),[]),onFocusableItemAdd:o.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>O(e=>e-1),[]),children:(0,i.jsx)(E.sG.div,{tabIndex:w||0===D?-1:0,"data-orientation":r,...p,ref:x,style:{outline:"none",...e.style},onMouseDown:m(e.onMouseDown,()=>{A.current=!0}),onFocus:m(e.onFocus,e=>{let t=!A.current;if(e.target===e.currentTarget&&t&&!w){let t=new CustomEvent(P,M);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=R().filter(e=>e.focusable);K([e.find(e=>e.active),e.find(e=>e.id===y),...e].filter(Boolean).map(e=>e.ref.current),f)}}A.current=!1}),onBlur:m(e.onBlur,()=>j(!1))})})}),q="RovingFocusGroupItem",B=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:s,children:l,...c}=e,d=N(),u=s||d,f=$(q,n),p=f.currentTabStopId===u,h=T(n),{onFocusableItemAdd:v,onFocusableItemRemove:x,currentTabStopId:g}=f;return o.useEffect(()=>{if(r)return v(),()=>x()},[r,v,x]),(0,i.jsx)(D.ItemSlot,{scope:n,id:u,focusable:r,active:a,children:(0,i.jsx)(E.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:m(e.onMouseDown,e=>{r?f.onItemFocus(u):e.preventDefault()}),onFocus:m(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:m(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let a=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return z[a]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>K(n))}}),children:"function"==typeof l?l({isCurrentTabStop:p,hasTabStop:null!=g}):l})})});B.displayName=q;var z={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function K(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var U=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,a]=o.useState(),s=o.useRef(null),i=o.useRef(e),l=o.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>n[e][t]??e,t));return o.useEffect(()=>{let e=Z(s.current);l.current="mounted"===c?e:"none"},[c]),b(()=>{let t=s.current,n=i.current;if(n!==e){let r=l.current,a=Z(t);e?d("MOUNT"):"none"===a||t?.display==="none"?d("UNMOUNT"):n&&r!==a?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),b(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let a=Z(s.current).includes(n.animationName);if(n.target===r&&a&&(d("ANIMATION_END"),!i.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},a=e=>{e.target===r&&(l.current=Z(s.current))};return r.addEventListener("animationstart",a),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",a),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),s=(0,h.s)(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||r.isPresent?o.cloneElement(a,{ref:s}):null};function Z(e){return e?.animationName||"none"}U.displayName="Presence";var H="Tabs",[X,V]=p(H,[F]),Y=F(),[J,Q]=X(H),ee=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:a,defaultValue:s,orientation:o="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=S(l),[f,m]=k({prop:r,onChange:a,defaultProp:s??"",caller:H});return(0,i.jsx)(J,{scope:n,baseId:N(),value:f,onValueChange:m,orientation:o,dir:u,activationMode:c,children:(0,i.jsx)(E.sG.div,{dir:u,"data-orientation":o,...d,ref:t})})});ee.displayName=H;var et="TabsList",en=o.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...a}=e,s=Q(et,n),o=Y(n);return(0,i.jsx)(W,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:r,children:(0,i.jsx)(E.sG.div,{role:"tablist","aria-orientation":s.orientation,...a,ref:t})})});en.displayName=et;var er="TabsTrigger",ea=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...s}=e,o=Q(er,n),l=Y(n),c=eo(o.baseId,r),d=el(o.baseId,r),u=r===o.value;return(0,i.jsx)(B,{asChild:!0,...l,focusable:!a,active:u,children:(0,i.jsx)(E.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...s,ref:t,onMouseDown:m(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:m(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:m(e.onFocus,()=>{let e="manual"!==o.activationMode;u||a||!e||o.onValueChange(r)})})})});ea.displayName=er;var es="TabsContent",ei=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:a,children:s,...l}=e,c=Q(es,n),d=eo(c.baseId,r),u=el(c.baseId,r),f=r===c.value,m=o.useRef(f);return o.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.jsx)(U,{present:a||f,children:({present:n})=>(0,i.jsx)(E.sG.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&s})})});function eo(e,t){return`${e}-trigger-${t}`}function el(e,t){return`${e}-content-${t}`}ei.displayName=es;var ec=n(4780);let ed=o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(en,{ref:n,className:(0,ec.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));ed.displayName=en.displayName;let eu=o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(ea,{ref:n,className:(0,ec.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));eu.displayName=ea.displayName;let ef=o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(ei,{ref:n,className:(0,ec.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));ef.displayName=ei.displayName;var em="dismissableLayer.update",ep=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),eh=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:l,onInteractOutside:c,onDismiss:d,...u}=e,f=o.useContext(ep),[p,v]=o.useState(null),x=p?.ownerDocument??globalThis?.document,[,g]=o.useState({}),y=(0,h.s)(t,e=>v(e)),b=Array.from(f.layers),[w]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),j=b.indexOf(w),N=p?b.indexOf(p):-1,R=f.layersWithOutsidePointerEventsDisabled.size>0,k=N>=j,A=function(e,t=globalThis?.document){let n=C(e),r=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){ex("dismissableLayer.pointerDownOutside",n,s,{discrete:!0})},s={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=r,t.addEventListener("click",a.current,{once:!0})):r()}else t.removeEventListener("click",a.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));k&&!n&&(s?.(e),c?.(e),e.defaultPrevented||d?.())},x),S=function(e,t=globalThis?.document){let n=C(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&ex("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(l?.(e),c?.(e),e.defaultPrevented||d?.())},x);return!function(e,t=globalThis?.document){let n=C(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===f.layers.size-1&&(r?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},x),o.useEffect(()=>{if(p)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(a=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),ev(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=a)}},[p,x,n,f]),o.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),ev())},[p,f]),o.useEffect(()=>{let e=()=>g({});return document.addEventListener(em,e),()=>document.removeEventListener(em,e)},[]),(0,i.jsx)(E.sG.div,{...u,ref:y,style:{pointerEvents:R?k?"auto":"none":void 0,...e.style},onFocusCapture:m(e.onFocusCapture,S.onFocusCapture),onBlurCapture:m(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:m(e.onPointerDownCapture,A.onPointerDownCapture)})});function ev(){let e=new CustomEvent(em);document.dispatchEvent(e)}function ex(e,t,n,{discrete:r}){let a=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),r?(0,E.hO)(a,s):a.dispatchEvent(s)}eh.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(ep),r=o.useRef(null),a=(0,h.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,i.jsx)(E.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var eg="focusScope.autoFocusOnMount",ey="focusScope.autoFocusOnUnmount",eb={bubbles:!1,cancelable:!0},ew=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:s,...l}=e,[c,d]=o.useState(null),u=C(a),f=C(s),m=o.useRef(null),p=(0,h.s)(t,e=>d(e)),v=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(v.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:eE(m.current,{select:!0})},t=function(e){if(v.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||eE(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&eE(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,v.paused]),o.useEffect(()=>{if(c){eC.add(v);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(eg,eb);c.addEventListener(eg,u),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(eE(r,{select:t}),document.activeElement!==n)return}(ej(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&eE(c))}return()=>{c.removeEventListener(eg,u),setTimeout(()=>{let t=new CustomEvent(ey,eb);c.addEventListener(ey,f),c.dispatchEvent(t),t.defaultPrevented||eE(e??document.body,{select:!0}),c.removeEventListener(ey,f),eC.remove(v)},0)}}},[c,u,f,v]);let x=o.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[r,s]=function(e){let t=ej(e);return[eN(t,e),eN(t.reverse(),e)]}(t);r&&s?e.shiftKey||a!==s?e.shiftKey&&a===r&&(e.preventDefault(),n&&eE(s,{select:!0})):(e.preventDefault(),n&&eE(r,{select:!0})):a===t&&e.preventDefault()}},[n,r,v.paused]);return(0,i.jsx)(E.sG.div,{tabIndex:-1,...l,ref:p,onKeyDown:x})});function ej(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function eN(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function eE(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}ew.displayName="FocusScope";var eC=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=eR(e,t)).unshift(t)},remove(t){e=eR(e,t),e[0]?.resume()}}}();function eR(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var ek=n(51215),eA=o.forwardRef((e,t)=>{let{container:n,...r}=e,[a,s]=o.useState(!1);b(()=>s(!0),[]);let l=n||a&&globalThis?.document?.body;return l?ek.createPortal((0,i.jsx)(E.sG.div,{...r,ref:t}),l):null});eA.displayName="Portal";var eS=0;function eP(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var eM=function(){return(eM=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function eI(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n}Object.create;Object.create;var eD=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),eT="width-before-scroll-bar";function eO(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var eL="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,eF=new WeakMap;function e_(e){return e}var e$=function(e){void 0===e&&(e={});var t,n,r,a,s=(t=null,void 0===n&&(n=e_),r=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,a);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){a=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var s=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(s)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return s.options=eM({async:!0,ssr:!1},e),s}(),eW=function(){},eG=o.forwardRef(function(e,t){var n,r,a,s,i=o.useRef(null),l=o.useState({onScrollCapture:eW,onWheelCapture:eW,onTouchMoveCapture:eW}),c=l[0],d=l[1],u=e.forwardProps,f=e.children,m=e.className,p=e.removeScrollBar,h=e.enabled,v=e.shards,x=e.sideCar,g=e.noRelative,y=e.noIsolation,b=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,E=eI(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[i,t],r=function(e){return n.forEach(function(t){return eO(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,s=a.facade,eL(function(){var e=eF.get(s);if(e){var t=new Set(e),r=new Set(n),a=s.current;t.forEach(function(e){r.has(e)||eO(e,null)}),r.forEach(function(e){t.has(e)||eO(e,a)})}eF.set(s,n)},[n]),s),R=eM(eM({},E),c);return o.createElement(o.Fragment,null,h&&o.createElement(x,{sideCar:e$,removeScrollBar:p,shards:v,noRelative:g,noIsolation:y,inert:b,setCallbacks:d,allowPinchZoom:!!w,lockRef:i,gapMode:N}),u?o.cloneElement(o.Children.only(f),eM(eM({},R),{ref:C})):o.createElement(void 0===j?"div":j,eM({},R,{className:m,ref:C}),f))});eG.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},eG.classNames={fullWidth:eT,zeroRight:eD};var eq=function(e){var t=e.sideCar,n=eI(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return o.createElement(r,eM({},n))};eq.isSideCarExport=!0;var eB=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ez=function(){var e=eB();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},eK=function(){var e=ez();return function(t){return e(t.styles,t.dynamic),null}},eU={left:0,top:0,right:0,gap:0},eZ=function(e){return parseInt(e||"",10)||0},eH=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[eZ(n),eZ(r),eZ(a)]},eX=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eU;var t=eH(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},eV=eK(),eY="data-scroll-locked",eJ=function(e,t,n,r){var a=e.left,s=e.top,i=e.right,o=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(o,"px ").concat(r,";\n  }\n  body[").concat(eY,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(o,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(eD," {\n    right: ").concat(o,"px ").concat(r,";\n  }\n  \n  .").concat(eT," {\n    margin-right: ").concat(o,"px ").concat(r,";\n  }\n  \n  .").concat(eD," .").concat(eD," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(eT," .").concat(eT," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(eY,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},eQ=function(){var e=parseInt(document.body.getAttribute(eY)||"0",10);return isFinite(e)?e:0},e0=function(){o.useEffect(function(){return document.body.setAttribute(eY,(eQ()+1).toString()),function(){var e=eQ()-1;e<=0?document.body.removeAttribute(eY):document.body.setAttribute(eY,e.toString())}},[])},e1=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=void 0===r?"margin":r;e0();var s=o.useMemo(function(){return eX(a)},[a]);return o.createElement(eV,{styles:eJ(s,!t,a,n?"":"!important")})},e2=!1;if("undefined"!=typeof window)try{var e3=Object.defineProperty({},"passive",{get:function(){return e2=!0,!0}});window.addEventListener("test",e3,e3),window.removeEventListener("test",e3,e3)}catch(e){e2=!1}var e4=!!e2&&{passive:!1},e5=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},e7=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),e8(e,r)){var a=e6(e,r);if(a[1]>a[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},e8=function(e,t){return"v"===e?e5(t,"overflowY"):e5(t,"overflowX")},e6=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},e9=function(e,t,n,r,a){var s,i=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),o=i*r,l=n.target,c=t.contains(l),d=!1,u=o>0,f=0,m=0;do{if(!l)break;var p=e6(e,l),h=p[0],v=p[1]-p[2]-i*h;(h||v)&&e8(e,l)&&(f+=v,m+=h);var x=l.parentNode;l=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return u&&(a&&1>Math.abs(f)||!a&&o>f)?d=!0:!u&&(a&&1>Math.abs(m)||!a&&-o>m)&&(d=!0),d},te=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},tt=function(e){return[e.deltaX,e.deltaY]},tn=function(e){return e&&"current"in e?e.current:e},tr=0,ta=[];let ts=(r=function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),a=o.useState(tr++)[0],s=o.useState(eK)[0],i=o.useRef(e);o.useEffect(function(){i.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,a=0,s=t.length;a<s;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(tn),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var a,s=te(e),o=n.current,l="deltaX"in e?e.deltaX:o[0]-s[0],c="deltaY"in e?e.deltaY:o[1]-s[1],d=e.target,u=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=e7(u,d);if(!f)return!0;if(f?a=u:(a="v"===u?"h":"v",f=e7(u,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=a),!a)return!0;var m=r.current||a;return e9(m,t,e,"h"===m?l:c,!0)},[]),c=o.useCallback(function(e){if(ta.length&&ta[ta.length-1]===s){var n="deltaY"in e?tt(e):te(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var a=(i.current.shards||[]).map(tn).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?l(e,a[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=o.useCallback(function(e,n,r,a){var s={name:e,delta:n,target:r,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),u=o.useCallback(function(e){n.current=te(e),r.current=void 0},[]),f=o.useCallback(function(t){d(t.type,tt(t),t.target,l(t,e.lockRef.current))},[]),m=o.useCallback(function(t){d(t.type,te(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return ta.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",c,e4),document.addEventListener("touchmove",c,e4),document.addEventListener("touchstart",u,e4),function(){ta=ta.filter(function(e){return e!==s}),document.removeEventListener("wheel",c,e4),document.removeEventListener("touchmove",c,e4),document.removeEventListener("touchstart",u,e4)}},[]);var p=e.removeScrollBar,h=e.inert;return o.createElement(o.Fragment,null,h?o.createElement(s,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,p?o.createElement(e1,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},e$.useMedium(r),eq);var ti=o.forwardRef(function(e,t){return o.createElement(eG,eM({},e,{ref:t,sideCar:ts}))});ti.classNames=eG.classNames;var to=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tl=new WeakMap,tc=new WeakMap,td={},tu=0,tf=function(e){return e&&(e.host||tf(e.parentNode))},tm=function(e,t,n,r){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tf(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});td[n]||(td[n]=new WeakMap);var s=td[n],i=[],o=new Set,l=new Set(a),c=function(e){!e||o.has(e)||(o.add(e),c(e.parentNode))};a.forEach(c);var d=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(o.has(e))d(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(tl.get(e)||0)+1,c=(s.get(e)||0)+1;tl.set(e,l),s.set(e,c),i.push(e),1===l&&a&&tc.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),o.clear(),tu++,function(){i.forEach(function(e){var t=tl.get(e)-1,a=s.get(e)-1;tl.set(e,t),s.set(e,a),t||(tc.has(e)||e.removeAttribute(r),tc.delete(e)),a||e.removeAttribute(n)}),--tu||(tl=new WeakMap,tl=new WeakMap,tc=new WeakMap,td={})}},tp=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=t||to(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live], script"))),tm(r,a,n,"aria-hidden")):function(){return null}},th="Dialog",[tv,tx]=p(th),[tg,ty]=tv(th),tb=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:s,modal:l=!0}=e,c=o.useRef(null),d=o.useRef(null),[u,f]=k({prop:r,defaultProp:a??!1,onChange:s,caller:th});return(0,i.jsx)(tg,{scope:t,triggerRef:c,contentRef:d,contentId:N(),titleId:N(),descriptionId:N(),open:u,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:l,children:n})};tb.displayName=th;var tw="DialogTrigger",tj=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=ty(tw,n),s=(0,h.s)(t,a.triggerRef);return(0,i.jsx)(E.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":tq(a.open),...r,ref:s,onClick:m(e.onClick,a.onOpenToggle)})});tj.displayName=tw;var tN="DialogPortal",[tE,tC]=tv(tN,{forceMount:void 0}),tR=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:a}=e,s=ty(tN,t);return(0,i.jsx)(tE,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,i.jsx)(U,{present:n||s.open,children:(0,i.jsx)(eA,{asChild:!0,container:a,children:e})}))})};tR.displayName=tN;var tk="DialogOverlay",tA=o.forwardRef((e,t)=>{let n=tC(tk,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,s=ty(tk,e.__scopeDialog);return s.modal?(0,i.jsx)(U,{present:r||s.open,children:(0,i.jsx)(tP,{...a,ref:t})}):null});tA.displayName=tk;var tS=(0,v.TL)("DialogOverlay.RemoveScroll"),tP=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=ty(tk,n);return(0,i.jsx)(ti,{as:tS,allowPinchZoom:!0,shards:[a.contentRef],children:(0,i.jsx)(E.sG.div,{"data-state":tq(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),tM="DialogContent",tI=o.forwardRef((e,t)=>{let n=tC(tM,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,s=ty(tM,e.__scopeDialog);return(0,i.jsx)(U,{present:r||s.open,children:s.modal?(0,i.jsx)(tD,{...a,ref:t}):(0,i.jsx)(tT,{...a,ref:t})})});tI.displayName=tM;var tD=o.forwardRef((e,t)=>{let n=ty(tM,e.__scopeDialog),r=o.useRef(null),a=(0,h.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return tp(e)},[]),(0,i.jsx)(tO,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:m(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:m(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:m(e.onFocusOutside,e=>e.preventDefault())})}),tT=o.forwardRef((e,t)=>{let n=ty(tM,e.__scopeDialog),r=o.useRef(!1),a=o.useRef(!1);return(0,i.jsx)(tO,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;n.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),tO=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,c=ty(tM,n),d=o.useRef(null),u=(0,h.s)(t,d);return o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??eP()),document.body.insertAdjacentElement("beforeend",e[1]??eP()),eS++,()=>{1===eS&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),eS--}},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ew,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,i.jsx)(eh,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":tq(c.open),...l,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(tU,{titleId:c.titleId}),(0,i.jsx)(tZ,{contentRef:d,descriptionId:c.descriptionId})]})]})}),tL="DialogTitle",tF=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=ty(tL,n);return(0,i.jsx)(E.sG.h2,{id:a.titleId,...r,ref:t})});tF.displayName=tL;var t_="DialogDescription",t$=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=ty(t_,n);return(0,i.jsx)(E.sG.p,{id:a.descriptionId,...r,ref:t})});t$.displayName=t_;var tW="DialogClose",tG=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=ty(tW,n);return(0,i.jsx)(E.sG.button,{type:"button",...r,ref:t,onClick:m(e.onClick,()=>a.onOpenChange(!1))})});function tq(e){return e?"open":"closed"}tG.displayName=tW;var tB="DialogTitleWarning",[tz,tK]=function(e,t){let n=o.createContext(t),r=e=>{let{children:t,...r}=e,a=o.useMemo(()=>r,Object.values(r));return(0,i.jsx)(n.Provider,{value:a,children:t})};return r.displayName=e+"Provider",[r,function(r){let a=o.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(tB,{contentName:tM,titleName:tL,docsSlug:"dialog"}),tU=({titleId:e})=>{let t=tK(tB),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},tZ=({contentRef:e,descriptionId:t})=>{let n=tK("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return o.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},tH=n(77473);let tX=o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(tA,{ref:n,className:(0,ec.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));tX.displayName=tA.displayName;let tV=o.forwardRef(({className:e,children:t,...n},r)=>(0,i.jsxs)(tR,{children:[(0,i.jsx)(tX,{}),(0,i.jsxs)(tI,{ref:r,className:(0,ec.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,(0,i.jsxs)(tG,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,i.jsx)(tH.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));tV.displayName=tI.displayName;let tY=({className:e,...t})=>(0,i.jsx)("div",{className:(0,ec.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});tY.displayName="DialogHeader";let tJ=o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(tF,{ref:n,className:(0,ec.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));tJ.displayName=tF.displayName,o.forwardRef(({className:e,...t},n)=>(0,i.jsx)(t$,{ref:n,className:(0,ec.cn)("text-sm text-muted-foreground",e),...t})).displayName=t$.displayName;var tQ=n(84219),t0=n(29777),t1=n(19377);let t2=(0,t1.A)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),t3=(0,t1.A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),t4=(0,t1.A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);function t5({matrix:e,onCandidateDecision:t}){let[n,r]=(0,o.useState)({}),[a,s]=(0,o.useState)({}),l=async(e,n)=>{s(t=>({...t,[e]:!0}));try{await t(e,n),r(t=>({...t,[e]:n}))}catch(e){console.error("决策失败:",e)}finally{s(t=>({...t,[e]:!1}))}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"您的专属推荐"}),(0,i.jsx)("p",{className:"text-gray-600",children:"AI红娘为您精心挑选的5位候选人"}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["生成时间: ",new Date(e.generatedAt).toLocaleString("zh-CN")]})]}),(0,i.jsx)(t7,{topMatch:e.topMatch,decision:n[e.topMatch.candidate.candidateId],loading:a[e.topMatch.candidate.candidateId],onDecision:l}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center gap-2",children:[(0,i.jsx)(tQ.A,{className:"w-5 h-5 text-yellow-500"}),"其他潜力候选人"]}),(0,i.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:e.potentialMatches.map((e,t)=>(0,i.jsx)(t8,{match:e,rank:t+2,decision:n[e.candidate.candidateId],loading:a[e.candidate.candidateId],onDecision:l},e.candidate.candidateId))})]})]})}function t7({topMatch:e,decision:t,loading:n,onDecision:r}){let[a,s]=(0,o.useState)(!1),{candidate:l,relationshipInsight:c,conversationSimulation:m,datePlan:p}=e;return(0,i.jsxs)(d.Zp,{className:"border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50",children:[(0,i.jsxs)(d.aR,{className:"text-center",children:[(0,i.jsx)("div",{className:"flex justify-center mb-2",children:(0,i.jsxs)(f.E,{className:"bg-pink-500 text-white px-3 py-1",children:[(0,i.jsx)(tQ.A,{className:"w-4 h-4 mr-1"}),"首席推荐"]})}),(0,i.jsx)(d.ZB,{className:"text-2xl",children:l.personalitySummary}),(0,i.jsxs)("div",{className:"flex justify-center items-center gap-2",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-pink-600",children:l.compatibilityScore}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"匹配度"})]})]}),(0,i.jsxs)(d.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900",children:"关系亮点"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:l.highlights.map((e,t)=>(0,i.jsx)(f.E,{variant:"secondary",className:"text-xs",children:e},t))})]}),(0,i.jsxs)(tb,{open:a,onOpenChange:s,children:[(0,i.jsx)(tj,{asChild:!0,children:(0,i.jsx)(u.$,{variant:"outline",className:"w-full",children:"查看完整分析报告"})}),(0,i.jsxs)(tV,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,i.jsx)(tY,{children:(0,i.jsx)(tJ,{children:"首席推荐 - 完整分析报告"})}),(0,i.jsx)(t9,{candidate:l,relationshipInsight:c,conversationSimulation:m,datePlan:p})]})]}),(0,i.jsx)(t6,{candidateId:l.candidateId,decision:t,loading:n,onDecision:r})]})]})}function t8({match:e,rank:t,decision:n,loading:r,onDecision:a}){let{candidate:s}=e;return(0,i.jsxs)(d.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,i.jsx)(d.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)(f.E,{variant:"outline",className:"mb-2",children:["#",t]}),(0,i.jsx)(d.ZB,{className:"text-lg",children:s.personalitySummary})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-xl font-bold text-blue-600",children:s.compatibilityScore}),(0,i.jsx)("div",{className:"text-xs text-gray-500",children:"匹配度"})]})]})}),(0,i.jsxs)(d.Wu,{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"亮点"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:s.highlights.slice(0,3).map((e,t)=>(0,i.jsx)(f.E,{variant:"secondary",className:"text-xs",children:e},t))})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[e.compatibilityReason.slice(0,100),"..."]}),(0,i.jsx)(t6,{candidateId:s.candidateId,decision:n,loading:r,onDecision:a})]})]})}function t6({candidateId:e,decision:t,loading:n,onDecision:r}){return t?(0,i.jsx)("div",{className:"text-center py-2",children:(0,i.jsx)(f.E,{variant:"liked"===t?"default":"secondary",children:"liked"===t?"已喜欢 ❤️":"已跳过 ⏭️"})}):(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(u.$,{variant:"outline",size:"sm",className:"flex-1 border-red-200 hover:bg-red-50",onClick:()=>r(e,"skipped"),disabled:n,children:[(0,i.jsx)(tH.A,{className:"w-4 h-4 mr-1"}),"跳过"]}),(0,i.jsxs)(u.$,{size:"sm",className:"flex-1 bg-pink-500 hover:bg-pink-600",onClick:()=>r(e,"liked"),disabled:n,children:[(0,i.jsx)(t0.A,{className:"w-4 h-4 mr-1"}),"喜欢"]})]})}function t9({candidate:e,relationshipInsight:t,conversationSimulation:n,datePlan:r}){return(0,i.jsxs)(ee,{defaultValue:"analysis",className:"w-full",children:[(0,i.jsxs)(ed,{className:"grid w-full grid-cols-4",children:[(0,i.jsx)(eu,{value:"analysis",children:"关系分析"}),(0,i.jsx)(eu,{value:"conversation",children:"模拟对话"}),(0,i.jsx)(eu,{value:"date",children:"约会计划"}),(0,i.jsx)(eu,{value:"profile",children:"详细资料"})]}),(0,i.jsxs)(ef,{value:"analysis",className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"关系优势"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:t.strengths.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"潜在挑战"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:t.challenges.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"相处建议"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:t.suggestions.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]})]}),(0,i.jsx)(ef,{value:"conversation",className:"space-y-4",children:(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,i.jsx)(t2,{className:"w-4 h-4"}),n.scenario]}),(0,i.jsx)("div",{className:"space-y-3",children:n.messages.map((e,t)=>(0,i.jsxs)("div",{className:`p-3 rounded-lg ${"user"===e.speaker?"bg-blue-100 ml-8":"bg-white mr-8"}`,children:[(0,i.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:["user"===e.speaker?"你":"对方",e.emotion&&` (${e.emotion})`]}),(0,i.jsx)("div",{className:"text-sm",children:e.content})]},t))}),(0,i.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 rounded-lg",children:[(0,i.jsx)("h5",{className:"text-sm font-medium mb-1",children:"对话分析"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:n.analysis})]})]})}),(0,i.jsx)(ef,{value:"date",className:"space-y-4",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,i.jsx)(t3,{className:"w-4 h-4"}),r.title]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:r.description}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h5",{className:"font-medium mb-1 flex items-center gap-1",children:[(0,i.jsx)(t4,{className:"w-3 h-3"}),"地点"]}),(0,i.jsx)("p",{children:r.location})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"时长"}),(0,i.jsx)("p",{children:r.duration})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"预算"}),(0,i.jsx)("p",{children:r.budget})]})]}),(0,i.jsxs)("div",{className:"mt-3",children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"活动安排"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:r.activities.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]}),(0,i.jsxs)("div",{className:"mt-3 p-3 bg-white rounded-lg",children:[(0,i.jsx)("h5",{className:"text-sm font-medium mb-1",children:"推荐理由"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:r.reasoning})]})]})}),(0,i.jsx)(ef,{value:"profile",className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"兼容性分数"}),(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[e.compatibilityScore,"/100"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"人格摘要"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.personalitySummary})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"匹配推理"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.reasoning})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"关系亮点"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.highlights.map((e,t)=>(0,i.jsx)(f.E,{variant:"secondary",children:e},t))})]}),e.challenges&&e.challenges.length>0&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium mb-1",children:"潜在挑战"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:e.challenges.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]})]})})]})}var ne=n(53344);let nt=(0,t1.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),nn=(0,t1.A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);function nr(){let e=(0,c.useParams)(),t=(0,c.useRouter)(),{toast:n}=function(){let[e,t]=(0,o.useState)([]);return{toast:(0,o.useCallback)(e=>{console.log(`Toast: ${e.title}`,e.description),t(t=>[...t,e]),setTimeout(()=>{t(e=>e.slice(1))},3e3)},[]),toasts:e}}(),r=e.requestId,[a,s]=(0,o.useState)(null),[l,d]=(0,o.useState)("loading"),[f,m]=(0,o.useState)(""),[p,h]=(0,o.useState)(0),v=async(e,t)=>{try{let a=await fetch(`/api/matches/matrix/${r}/candidates/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({decision:t})}),s=await a.json();if(s.success)n({title:"liked"===t?"已喜欢":"已跳过",description:s.message,variant:(s.mutualMatch,"default")}),s.mutualMatch&&n({title:"\uD83C\uDF89 恭喜！",description:"你们互相喜欢了！可以开始聊天了。",variant:"default"});else throw Error(s.message||"操作失败")}catch(e){console.error("决策失败:",e),n({title:"操作失败",description:e instanceof Error?e.message:"请重试",variant:"destructive"})}},x=async()=>{try{let e=await fetch("/api/matches/matrix",{method:"POST"}),n=await e.json();if(n.success)t.push(`/matches/matrix/${n.requestId}`);else throw Error(n.message||"重新生成失败")}catch(e){console.error("重新生成失败:",e),n({title:"重新生成失败",description:e instanceof Error?e.message:"请重试",variant:"destructive"})}};return(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)(u.$,{variant:"ghost",onClick:()=>t.push("/dashboard"),className:"flex items-center gap-2",children:[(0,i.jsx)(ne.A,{className:"w-4 h-4"}),"返回主页"]}),"completed"===l&&(0,i.jsxs)(u.$,{variant:"outline",onClick:x,className:"flex items-center gap-2",children:[(0,i.jsx)(nt,{className:"w-4 h-4"}),"重新生成"]})]}),"loading"===l||"processing"===l?(0,i.jsx)(na,{message:f,pollCount:p}):"failed"===l?(0,i.jsx)(ns,{message:f,onRetry:x}):a?(0,i.jsx)(t5,{matrix:a,onCandidateDecision:v}):(0,i.jsx)("div",{className:"text-center py-12",children:(0,i.jsx)("p",{className:"text-gray-500",children:"没有找到匹配数据"})})]})}function na({message:e,pollCount:t}){return(0,i.jsx)(d.Zp,{className:"max-w-md mx-auto",children:(0,i.jsxs)(d.Wu,{className:"text-center py-12",children:[(0,i.jsx)(nn,{className:"w-12 h-12 animate-spin mx-auto mb-4 text-pink-500"}),(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"AI红娘正在工作中..."}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:e||"正在分析您的资料并寻找最佳匹配"}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["预计还需要 ",Math.max(0,120-3*t)," 秒"]}),(0,i.jsx)("div",{className:"mt-4 w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-pink-500 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(100,3*t/120*100)}%`}})})]})})}function ns({message:e,onRetry:t}){return(0,i.jsx)(d.Zp,{className:"max-w-md mx-auto",children:(0,i.jsxs)(d.Wu,{className:"text-center py-12",children:[(0,i.jsx)("div",{className:"text-red-500 text-6xl mb-4",children:"\uD83D\uDE14"}),(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"生成失败"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6",children:e||"匹配矩阵生成失败，请重试"}),(0,i.jsx)(u.$,{onClick:t,className:"w-full",children:"重新生成"})]})})}},49285:(e,t,n)=>{Promise.resolve().then(n.bind(n,45348))},53344:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77473:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},80777:()=>{},84219:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19377).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o,metadata:()=>i});var r=n(37413),a=n(7339),s=n.n(a);n(61135);let i={title:"寡佬AI - 智能恋爱匹配平台",description:"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"};function o({children:e}){return(0,r.jsx)("html",{lang:"zh-CN",children:(0,r.jsx)("body",{className:s().className,children:e})})}},96834:(e,t,n)=>{"use strict";n.d(t,{E:()=>o});var r=n(60687);n(43210);var a=n(24224),s=n(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...n}){return(0,r.jsx)("div",{className:(0,s.cn)(i({variant:t}),e),...n})}},97592:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,435,924],()=>n(35793));module.exports=r})();