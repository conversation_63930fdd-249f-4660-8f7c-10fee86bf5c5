1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[6874,["874","static/chunks/874-5853b36c417a5f43.js","974","static/chunks/app/page-749a983b7555de6b.js"],""]
5:I[9665,[],"OutletBoundary"]
8:I[4911,[],"AsyncMetadataOutlet"]
a:I[9665,[],"ViewportBoundary"]
c:I[9665,[],"MetadataBoundary"]
e:I[6614,[],""]
:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/bdf212b7d06b5e36.css","style"]
0:{"P":null,"b":"LeNf8RDWmSdIUJ3rc0r7p","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/bdf212b7d06b5e36.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"zh-CN","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50","children":[["$","header",null,{"className":"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"flex justify-between items-center h-16","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-8 h-8 text-pink-600","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}],["$","h1",null,{"className":"text-2xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent","children":"寡佬AI"}]]}],["$","div",null,{"className":"flex items-center gap-4","children":[["$","$L4",null,{"href":"/auth/login","children":["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2","ref":"$undefined","children":"登录"}]}],["$","$L4",null,{"href":"/auth/register","children":["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2","ref":"$undefined","children":"注册"}]}]]}]]}]}]}],["$","section",null,{"className":"py-20 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-4xl mx-auto text-center","children":[["$","h1",null,{"className":"text-5xl md:text-6xl font-bold text-gray-900 mb-6","children":["用AI找到你的",["$","span",null,{"className":"bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent","children":"灵魂伴侣"}]]}],["$","p",null,{"className":"text-xl text-gray-600 mb-8 max-w-2xl mx-auto","children":"基于先进的AI技术，深度分析性格特征和价值观，为您精准匹配最合适的伴侣。 不再是简单的外表匹配，而是真正的心灵契合。"}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","$L4",null,{"href":"/auth/register","children":["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 rounded-md px-8 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700","ref":"$undefined","children":"开始寻找真爱"}]}],["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 rounded-md px-8","ref":"$undefined","children":"了解更多"}]]}]]}]}],["$","section",null,{"className":"py-20 px-4 sm:px-6 lg:px-8 bg-white/50","children":["$","div",null,{"className":"max-w-7xl mx-auto","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-gray-900 mb-4","children":"为什么选择寡佬AI？"}],["$","p",null,{"className":"text-lg text-gray-600 max-w-2xl mx-auto","children":"我们不只是另一个交友应用，而是您找到真爱的智能助手"}]]}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-3 gap-8","children":[["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-blue-600 mb-4","children":[["$","path","1mhkh5",{"d":"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z"}],["$","path","1d6s00",{"d":"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"AI深度分析"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"基于Gemini AI技术，深度分析用户的性格特征、价值观和生活方式， 生成详细的人格画像"}]]}]}],["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-green-600 mb-4","children":[["$","path","v2veuj",{"d":"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"对话模拟"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"AI模拟你们的第一次对话，预测沟通风格和话题契合度， 让你提前了解彼此的交流方式"}]]}]}],["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-pink-600 mb-4","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"精准匹配"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"综合考虑性格互补、价值观一致、兴趣重叠等多个维度， 计算出科学的兼容性分数"}]]}]}],["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-purple-600 mb-4","children":[["$","path","3xmgem",{"d":"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"隐私保护"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"双向确认机制，只有互相喜欢才能开始聊天， 保护用户隐私和安全"}]]}]}],["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-yellow-600 mb-4","children":[["$","polygon","8f66p6",{"points":"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"持续学习"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"AI会根据用户反馈不断学习和优化， 提供越来越准确的匹配建议"}]]}]}],["$","div",null,{"ref":"$undefined","className":"rounded-lg bg-card text-card-foreground border-0 shadow-lg hover:shadow-xl transition-shadow","children":["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-12 h-12 text-indigo-600 mb-4","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","path","1da9ce",{"d":"M16 3.13a4 4 0 0 1 0 7.75"}],"$undefined"]}],["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"真实用户"}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"严格的用户验证机制，确保平台上都是真实、 认真寻找伴侣的用户"}]]}]}]]}]]}]}],["$","section",null,{"className":"py-20 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-7xl mx-auto","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-gray-900 mb-4","children":"如何开始？"}],["$","p",null,{"className":"text-lg text-gray-600","children":"简单三步，开启你的智能恋爱之旅"}]]}],["$","div",null,{"className":"grid md:grid-cols-3 gap-8","children":[["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4","children":"1"}],["$","h3",null,{"className":"text-xl font-semibold mb-2","children":"完善资料"}],["$","p",null,{"className":"text-gray-600","children":"详细填写个人信息、兴趣爱好和理想伴侣要求， 让AI更好地了解你"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4","children":"2"}],["$","h3",null,{"className":"text-xl font-semibold mb-2","children":"AI分析匹配"}],["$","p",null,{"className":"text-gray-600","children":"AI深度分析你的人格特征，为你推荐最合适的潜在伴侣， 并提供详细的匹配分析"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4","children":"3"}],["$","h3",null,{"className":"text-xl font-semibold mb-2","children":"开始聊天"}],["$","p",null,{"className":"text-gray-600","children":"双方互相喜欢后即可开始聊天， 基于AI建议的话题开启美好的对话"}]]}]]}]]}]}],["$","section",null,{"className":"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-blue-600","children":["$","div",null,{"className":"max-w-4xl mx-auto text-center","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-white mb-6","children":"准备好找到你的灵魂伴侣了吗？"}],["$","p",null,{"className":"text-xl text-pink-100 mb-8","children":"加入数千名用户，让AI帮你找到真正合适的人"}],["$","$L4",null,{"href":"/auth/register","children":["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-11 rounded-md px-8 bg-white text-pink-600 hover:bg-gray-100","ref":"$undefined","children":"立即开始"}]}]]}]}],["$","footer",null,{"className":"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-7xl mx-auto","children":[["$","div",null,{"className":"grid md:grid-cols-4 gap-8","children":[["$","div",null,{"children":[["$","div",null,{"className":"flex items-center gap-2 mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-6 h-6 text-pink-600","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}],["$","h3",null,{"className":"text-lg font-bold","children":"寡佬AI"}]]}],["$","p",null,{"className":"text-gray-400","children":"用AI技术重新定义恋爱匹配， 为每个人找到最合适的伴侣。"}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-4","children":"产品"}],["$","ul",null,{"className":"space-y-2 text-gray-400","children":[["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"功能介绍"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"定价"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"安全保障"}]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-4","children":"支持"}],["$","ul",null,{"className":"space-y-2 text-gray-400","children":[["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"帮助中心"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"联系我们"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"用户反馈"}]}]]}]]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-semibold mb-4","children":"法律"}],["$","ul",null,{"className":"space-y-2 text-gray-400","children":[["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"隐私政策"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"服务条款"}]}],["$","li",null,{"children":["$","a",null,{"href":"#","className":"hover:text-white","children":"Cookie政策"}]}]]}]]}]]}],["$","div",null,{"className":"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400","children":["$","p",null,{"children":"© 2024 寡佬AI. 保留所有权利。"}]}]]}]}]]}],null,["$","$L5",null,{"children":["$L6","$L7",["$","$L8",null,{"promise":"$@9"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","4B1Bzdr3MtqX9xLkKItq4v",{"children":[["$","$La",null,{"children":"$Lb"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
f:"$Sreact.suspense"
10:I[4911,[],"AsyncMetadata"]
d:["$","div",null,{"hidden":true,"children":["$","$f",null,{"fallback":null,"children":["$","$L10",null,{"promise":"$@11"}]}]}]
7:null
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
6:null
9:{"metadata":[["$","title","0",{"children":"寡佬AI - 智能恋爱匹配平台"}],["$","meta","1",{"name":"description","content":"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"}]],"error":null,"digest":"$undefined"}
11:{"metadata":"$9:metadata","error":null,"digest":"$undefined"}
