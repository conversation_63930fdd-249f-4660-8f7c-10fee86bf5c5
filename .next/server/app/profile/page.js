(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(49384),i=t(82348);function n(...e){return(0,i.QP)((0,s.$)(e))}},8636:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(60687),i=t(43210),n=t(29523),a=t(89667),o=t(14163),l=i.forwardRef((e,r)=>(0,s.jsx)(o.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var d=t(24224),c=t(4780);let u=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),p=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)(l,{ref:t,className:(0,c.cn)(u(),e),...r}));p.displayName=l.displayName;let m=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));m.displayName="Textarea";var x=t(44493),h=t(79481),f=t(16189),g=t(54286);function b(){let[e,r]=(0,i.useState)({name:"",age:0,gender:"",location:"",bio:"",interests:[],selfDescription:"",lookingFor:"",relationshipGoals:""}),[t,o]=(0,i.useState)(!1),[l,d]=(0,i.useState)(""),[c,u]=(0,i.useState)(""),[b,v]=(0,i.useState)(""),[j,y]=(0,i.useState)(!1),[w,N]=(0,i.useState)(!1),[k,P]=(0,i.useState)(!1),C=(0,f.useRouter)();(0,h.U)();let A=()=>{b.trim()&&!e.interests.includes(b.trim())&&(r(e=>({...e,interests:[...e.interests,b.trim()]})),v(""))},q=e=>{r(r=>({...r,interests:r.interests.filter(r=>r!==e)}))},F=async()=>{N(!0),d("");try{let t=await fetch("/api/profile/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,age:e.age,gender:e.gender,location:e.location,interests:e.interests})});if(t.ok){let e=await t.json();r(r=>({...r,bio:e.bio||r.bio,selfDescription:e.selfDescription||r.selfDescription,lookingFor:e.lookingFor||r.lookingFor,relationshipGoals:e.relationshipGoals||r.relationshipGoals})),u("AI已为您生成个人资料示例，您可以根据需要进行修改！")}else d("AI生成失败，请重试")}catch(e){console.error("Error generating AI profile:",e),d("AI生成失败，请重试")}finally{N(!1)}},R=async r=>{r.preventDefault(),o(!0),d(""),u("");try{let r=await fetch("/api/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(r.ok){u("资料保存成功！");let e=new URLSearchParams(window.location.search);"true"===e.get("welcome")?setTimeout(()=>{C.push("/dashboard")},1500):P(!0)}else{let e=await r.json();d(e.error||"保存失败，请重试")}}catch(e){d("保存失败，请重试")}finally{o(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 py-8",children:[(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4",children:(0,s.jsxs)(x.Zp,{children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{children:j?"欢迎加入寡佬AI！":"个人资料"}),(0,s.jsx)(x.BT,{children:j?"请完善您的个人信息，让AI为您找到最合适的伴侣":"完善您的个人信息，让AI更好地为您匹配"}),j&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"\uD83C\uDF89 注册成功！完善资料后即可开始您的智能匹配之旅。"})})]}),(0,s.jsx)(x.Wu,{children:(0,s.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[l&&(0,s.jsx)("div",{className:"text-red-500 text-sm",children:l}),c&&(0,s.jsx)("div",{className:"text-green-500 text-sm",children:c}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"name",children:"姓名"}),(0,s.jsx)(a.p,{id:"name",value:e.name,onChange:e=>r(r=>({...r,name:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"age",children:"年龄"}),(0,s.jsx)(a.p,{id:"age",type:"number",value:e.age||"",onChange:e=>r(r=>({...r,age:parseInt(e.target.value)||0})),required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"gender",children:"性别"}),(0,s.jsxs)("select",{id:"gender",value:e.gender,onChange:e=>r(r=>({...r,gender:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm",required:!0,children:[(0,s.jsx)("option",{value:"",children:"请选择"}),(0,s.jsx)("option",{value:"male",children:"男"}),(0,s.jsx)("option",{value:"female",children:"女"}),(0,s.jsx)("option",{value:"other",children:"其他"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"location",children:"所在地"}),(0,s.jsx)(a.p,{id:"location",value:e.location,onChange:e=>r(r=>({...r,location:e.target.value})),required:!0})]})]}),!e.bio&&!e.selfDescription&&!e.lookingFor&&!e.relationshipGoals&&(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-blue-600"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:"AI智能生成"})]})}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"让AI根据您的基本信息生成个人资料示例，您可以在此基础上进行修改。"}),(0,s.jsx)(n.$,{type:"button",onClick:F,disabled:w||!e.name||!e.age||!e.gender,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:w?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"w-4 h-4 mr-2 animate-spin"}),"AI生成中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"一键生成个人资料"]})}),(!e.name||!e.age||!e.gender)&&(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"请先填写姓名、年龄和性别信息"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"bio",children:"个人简介"}),(0,s.jsx)(m,{id:"bio",value:e.bio,onChange:e=>r(r=>({...r,bio:e.target.value})),placeholder:"简单介绍一下自己...",rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{children:"兴趣爱好"}),(0,s.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,s.jsx)(a.p,{value:b,onChange:e=>v(e.target.value),placeholder:"添加兴趣爱好",onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),A())}),(0,s.jsx)(n.$,{type:"button",onClick:A,children:"添加"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.interests.map((e,r)=>(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center gap-1",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>q(e),className:"text-blue-600 hover:text-blue-800",children:"\xd7"})]},r))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"selfDescription",children:"自我描述"}),(0,s.jsx)(m,{id:"selfDescription",value:e.selfDescription,onChange:e=>r(r=>({...r,selfDescription:e.target.value})),placeholder:"详细描述一下自己的性格、价值观等...",rows:4})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"lookingFor",children:"寻找对象"}),(0,s.jsx)(m,{id:"lookingFor",value:e.lookingFor,onChange:e=>r(r=>({...r,lookingFor:e.target.value})),placeholder:"描述您理想的伴侣...",rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p,{htmlFor:"relationshipGoals",children:"感情目标"}),(0,s.jsx)(m,{id:"relationshipGoals",value:e.relationshipGoals,onChange:e=>r(r=>({...r,relationshipGoals:e.target.value})),placeholder:"您希望建立什么样的关系？",rows:2})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",disabled:t,children:t?"保存中...":"保存资料"})]})})]})}),k&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto bg-white shadow-2xl",children:[(0,s.jsxs)(x.aR,{className:"text-center pb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(g.A,{className:"w-8 h-8 text-green-600"})}),(0,s.jsx)(x.ZB,{className:"text-xl font-bold text-gray-900 mb-2",children:"资料保存成功！"}),(0,s.jsx)(x.BT,{className:"text-gray-600",children:"您的个人资料已更新，现在可以开始寻找匹配了"})]}),(0,s.jsx)(x.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(n.$,{variant:"outline",className:"flex-1",onClick:()=>P(!1),children:"继续编辑"}),(0,s.jsx)(n.$,{className:"flex-1 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700",onClick:()=>{P(!1),C.push("/dashboard")},children:"开始匹配"})]})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>o});var s=t(43210),i=t(51215),n=t(8730),a=t(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),i=s.forwardRef((e,s)=>{let{asChild:i,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?t:r,{...n,ref:s})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{});function l(e,r){e&&i.flushSync(()=>e.dispatchEvent(r))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19377:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var a=(e,r)=>{let t=(0,s.forwardRef)(({color:t="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:l,children:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...i,width:a,height:a,stroke:t,strokeWidth:l?24*Number(o)/Number(a):o,className:`lucide lucide-${n(e)}`,...c},[...r.map(([e,r])=>(0,s.createElement)(e,r)),...(Array.isArray(d)?d:[d])||[]]));return t.displayName=`${e}`,t}},22633:()=>{},27910:e=>{"use strict";e.exports=require("stream")},27944:(e,r,t)=>{Promise.resolve().then(t.bind(t,8636))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687),i=t(43210),n=t(8730),a=t(24224),o=t(4780);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:r,size:t,asChild:i=!1,...a},d)=>{let c=i?n.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(l({variant:r,size:t,className:e})),ref:d,...a})});d.displayName="Button"},32440:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41096:(e,r,t)=>{Promise.resolve().then(t.bind(t,75758))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o});var s=t(60687),i=t(43210),n=t(4780);let a=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let o=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},47990:()=>{},54286:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19377).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/profile/page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,r,t)=>{"use strict";t.d(r,{U:()=>i});var s=t(5745);let i=()=>(0,s.createClientComponentClient)()},79551:e=>{"use strict";e.exports=require("url")},80777:()=>{},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(60687),i=t(43210),n=t(4780);let a=i.forwardRef(({className:e,type:r,...t},i)=>(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...t}));a.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},93497:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75758)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/home/<USER>/workspace/indie/lingxiai-gemini/src/app/profile/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413),i=t(7339),n=t.n(i);t(61135);let a={title:"寡佬AI - 智能恋爱匹配平台",description:"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"};function o({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsx)("body",{className:n().className,children:e})})}},94735:e=>{"use strict";e.exports=require("events")},97592:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,435,924,745],()=>t(93497));module.exports=s})();