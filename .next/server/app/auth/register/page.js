(()=>{var e={};e.id=364,e.ids=[364],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(49384),i=t(82348);function n(...e){return(0,i.QP)((0,s.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18721:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(65239),i=t(48088),n=t(88170),o=t.n(n),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59401)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/auth/register/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/home/<USER>/workspace/indie/lingxiai-gemini/src/app/auth/register/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22633:()=>{},24507:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),i=t(43210),n=t(79481),o=t(29523),a=t(89667),d=t(44493),l=t(85814),u=t.n(l),c=t(16189);function p(){let[e,r]=(0,i.useState)(""),[t,l]=(0,i.useState)(""),[p,m]=(0,i.useState)(""),[x,h]=(0,i.useState)(!1),[f,g]=(0,i.useState)(""),[v,b]=(0,i.useState)("");(0,c.useRouter)();let y=(0,n.U)(),j=async s=>{if(s.preventDefault(),h(!0),g(""),b(""),t!==p){g("密码不匹配"),h(!1);return}try{let{error:s}=await y.auth.signUp({email:e,password:t,options:{emailRedirectTo:`${window.location.origin}/auth/callback`}});s?g(s.message):(b("注册成功！请检查您的邮箱并点击验证链接完成注册。"),r(""),l(""),m(""))}catch(e){g("An unexpected error occurred")}finally{h(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"注册"}),(0,s.jsx)(d.BT,{children:"创建您的寡佬AI账户"})]}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[f&&(0,s.jsx)("div",{className:"text-red-500 text-sm",children:f}),v&&(0,s.jsx)("div",{className:"text-green-500 text-sm",children:v}),(0,s.jsx)("div",{children:(0,s.jsx)(a.p,{type:"email",placeholder:"邮箱",value:e,onChange:e=>r(e.target.value),required:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(a.p,{type:"password",placeholder:"密码",value:t,onChange:e=>l(e.target.value),required:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(a.p,{type:"password",placeholder:"确认密码",value:p,onChange:e=>m(e.target.value),required:!0})}),(0,s.jsx)(o.$,{type:"submit",className:"w-full",disabled:x,children:x?"注册中...":"注册"})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["已有账户？"," ",(0,s.jsx)(u(),{href:"/auth/login",className:"text-blue-600 hover:underline",children:"登录"})]})})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(60687),i=t(43210),n=t(8730),o=t(24224),a=t(4780);let d=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef(({className:e,variant:r,size:t,asChild:i=!1,...o},l)=>{let u=i?n.DX:"button";return(0,s.jsx)(u,{className:(0,a.cn)(d({variant:r,size:t,className:e})),ref:l,...o})});l.displayName="Button"},32440:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>u,ZB:()=>d,Zp:()=>o,aR:()=>a});var s=t(60687),i=t(43210),n=t(4780);let o=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card";let a=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));a.displayName="CardHeader";let d=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let u=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));u.displayName="CardContent",i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59401:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/auth/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/auth/register/page.tsx","default")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79173:(e,r,t)=>{Promise.resolve().then(t.bind(t,24507))},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,r,t)=>{"use strict";t.d(r,{U:()=>i});var s=t(5745);let i=()=>(0,s.createClientComponentClient)()},79551:e=>{"use strict";e.exports=require("url")},80777:()=>{},81630:e=>{"use strict";e.exports=require("http")},85253:(e,r,t)=>{Promise.resolve().then(t.bind(t,59401))},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687),i=t(43210),n=t(4780);let o=i.forwardRef(({className:e,type:r,...t},i)=>(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...t}));o.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>o});var s=t(37413),i=t(7339),n=t.n(i);t(61135);let o={title:"寡佬AI - 智能恋爱匹配平台",description:"基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣"};function a({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsx)("body",{className:n().className,children:e})})}},94735:e=>{"use strict";e.exports=require("events")},97592:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,435,924,814,745],()=>t(18721));module.exports=s})();