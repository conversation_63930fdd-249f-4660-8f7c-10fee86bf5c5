{"/_not-found/page": "app/_not-found/page.js", "/api/auth/sync/route": "app/api/auth/sync/route.js", "/api/feedback/route": "app/api/feedback/route.js", "/api/health/route": "app/api/health/route.js", "/api/matches/[id]/route": "app/api/matches/[id]/route.js", "/api/matches/matrix/[requestId]/candidates/[candidateId]/route": "app/api/matches/matrix/[requestId]/candidates/[candidateId]/route.js", "/api/matches/matrix/route": "app/api/matches/matrix/route.js", "/api/matches/route": "app/api/matches/route.js", "/api/profile/generate/route": "app/api/profile/generate/route.js", "/api/profile/route": "app/api/profile/route.js", "/api/worker/process-queue/route": "app/api/worker/process-queue/route.js", "/auth/callback/route": "app/auth/callback/route.js", "/auth/login/page": "app/auth/login/page.js", "/auth/register/page": "app/auth/register/page.js", "/dashboard/page": "app/dashboard/page.js", "/match/[id]/page": "app/match/[id]/page.js", "/matches/matrix/[requestId]/page": "app/matches/matrix/[requestId]/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js"}