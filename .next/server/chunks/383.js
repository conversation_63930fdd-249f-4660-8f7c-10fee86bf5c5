"use strict";exports.id=383,exports.ids=[383],exports.modules={5730:(e,t,i)=>{i.d(t,{i:()=>r,is:()=>n});let r=Symbol.for("drizzle:entityKind");function n(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,r))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=Object.getPrototypeOf(e).constructor;if(i)for(;i;){if(r in i&&i[r]===t[r])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")},9253:(e,t,i)=>{i.d(t,{KM:()=>o,vE:()=>h,xQ:()=>c});var r=i(5730),n=i(79608),s=i(51024),l=i(65734);class a extends l.u{static [r.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u extends l.u{static [r.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function h(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="string"?new u(i,r.withTimezone??!1,r.precision):new a(i,r?.withTimezone??!1,r?.precision)}},9528:(e,t,i)=>{i.d(t,{Xd:()=>o,kB:()=>u});var r=i(5730),n=i(79608),s=i(51024),l=i(65734);class a extends l.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [r.i]="PgTimeBuilder";build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function u(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new a(i,r.withTimezone??!1,r.precision)}},9594:(e,t,i)=>{i.d(t,{dL:()=>a,uR:()=>o});var r=i(5730),n=i(96657),s=i(51024);class l extends s.pe{static [r.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,n.ll)`gen_random_uuid()`)}build(e){return new a(e,this.config)}}class a extends s.Kl{static [r.i]="PgUUID";getSQLType(){return"uuid"}}function o(e){return new l(e??"")}},10007:(e,t,i)=>{i.d(t,{V:()=>n});var r=i(5730);class n{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [r.i]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}},12772:(e,t,i)=>{i.d(t,{n:()=>r});let r=Symbol.for("drizzle:ViewBaseConfig")},24717:(e,t,i)=>{i.d(t,{HE:()=>c,Io:()=>p,Lf:()=>m,XI:()=>d,e:()=>l});var r=i(5730),n=i(86214);let s=Symbol.for("drizzle:Schema"),l=Symbol.for("drizzle:Columns"),a=Symbol.for("drizzle:ExtraConfigColumns"),o=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder"),f=Symbol.for("drizzle:IsDrizzleTable");class d{static [r.i]="Table";static Symbol={Name:n.E,Schema:s,OriginalName:o,Columns:l,ExtraConfigColumns:a,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[n.E];[o];[s];[l];[a];[u];[c]=!1;[f]=!0;[h]=void 0;constructor(e,t,i){this[n.E]=this[o]=e,this[s]=t,this[u]=i}}function p(e){return e[n.E]}function m(e){return`${e[s]??"public"}.${e[n.E]}`}},29334:(e,t,i)=>{i.d(t,{Qq:()=>o});var r=i(5730),n=i(79608),s=i(51024);class l extends s.pe{static [r.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new a(e,this.config)}}class a extends s.Kl{static [r.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function o(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new l(i,r)}},34359:(e,t,i)=>{i.d(t,{Pq:()=>a,iX:()=>l});var r=i(5730),n=i(51024);class s extends n.pe{static [r.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new l(e,this.config)}}class l extends n.Kl{static [r.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function a(e){return new s(e??"")}},34509:(e,t,i)=>{i.d(t,{Fx:()=>a,kn:()=>l});var r=i(5730),n=i(51024);class s extends n.pe{static [r.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new l(e,this.config)}}class l extends n.Kl{static [r.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function a(e){return new s(e??"")}},37193:(e,t,i)=>{i.d(t,{dw:()=>c,p6:()=>h,qw:()=>o});var r=i(5730),n=i(79608),s=i(51024),l=i(65734);class a extends l.u{static [r.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u extends l.u{static [r.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgDateString";getSQLType(){return"date"}}function h(e,t){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="date"?new a(i):new u(i)}},37383:(e,t,i)=>{i.d(t,{f:()=>eO});var r=i(43971),n=i(5730);class s{static [n.i]="ConsoleLogWriter";write(e){console.log(e)}}class l{static [n.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new s}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),r=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${r}`)}}class a{static [n.i]="NoopLogger";logQuery(){}}var o=i(10007),u=i(96657),c=i(24717),h=i(12772);class f{constructor(e){this.table=e}static [n.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class d{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [n.i]="TableAliasProxyHandler";get(e,t){if(t===c.XI.Symbol.IsAlias)return!0;if(t===c.XI.Symbol.Name||this.replaceOriginalName&&t===c.XI.Symbol.OriginalName)return this.alias;if(t===h.n)return{...e[h.n],name:this.alias,isAlias:!0};if(t===c.XI.Symbol.Columns){let t=e[c.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(r=>{i[r]=new Proxy(t[r],new f(new Proxy(e,this)))}),i}let i=e[t];return(0,n.is)(i,o.V)?new Proxy(i,new f(new Proxy(e,this))):i}}class p{constructor(e){this.alias=e}static [n.i]=null;get(e,t){return"sourceTable"===t?m(e.sourceTable,this.alias):e[t]}}function m(e,t){return new Proxy(e,new d(t,!1))}function g(e,t){return new Proxy(e,new f(new Proxy(e.table,new d(t,!1))))}function y(e,t){return new u.Xs.Aliased(b(e.sql,t),e.fieldAlias)}function b(e,t){return u.ll.join(e.queryChunks.map(e=>(0,n.is)(e,o.V)?g(e,t):(0,n.is)(e,u.Xs)?b(e,t):(0,n.is)(e,u.Xs.Aliased)?y(e,t):e))}function w(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function v(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,i)=>e+(0===i?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function S(e){return e}class T{static [n.i]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?w:"camelCase"===e?v:S}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[c.XI.Symbol.Schema]??"public",i=e.table[c.XI.Symbol.OriginalName],r=`${t}.${i}.${e.name}`;return this.cache[r]||this.cacheTable(e.table),this.cache[r]}cacheTable(e){let t=e[c.XI.Symbol.Schema]??"public",i=e[c.XI.Symbol.OriginalName],r=`${t}.${i}`;if(!this.cachedTables[r]){for(let t of Object.values(e[c.XI.Symbol.Columns])){let e=`${r}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[r]=!0}}clearCache(){this.cache={},this.cachedTables={}}}class P extends Error{static [n.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class N extends P{static [n.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}var $=i(51024),x=i(34509),A=i(34359),C=i(72170),O=i(9528),q=i(9253),_=i(37193),j=i(9594),E=i(92768);class I{static [n.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new B(e,this.columns,this.name)}}class B{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [n.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[E.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}var L=i(94634);function k(e){return(0,u.ll)`${e} asc`}function D(e){return(0,u.ll)`${e} desc`}class z{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[c.XI.Symbol.Name]}static [n.i]="Relation";referencedTableName;fieldName}class Q{constructor(e,t){this.table=e,this.config=t}static [n.i]="Relations"}class F extends z{constructor(e,t,i,r){super(e,t,i?.relationName),this.config=i,this.isNullable=r}static [n.i]="One";withFieldName(e){let t=new F(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class V extends z{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [n.i]="Many";withFieldName(e){let t=new V(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function U(e){return{one:function(t,i){return new F(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new V(e,t,i)}}}var X=i(67083),K=i(79608);class M extends u.Ss{static [n.i]="PgViewBase"}class R{static [n.i]="PgDialect";casing;constructor(e){this.casing=new T(e?.casing)}async migrate(e,t,i){let r="string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",n="string"==typeof i?"drizzle":i.migrationsSchema??"drizzle",s=(0,u.ll)`
			CREATE TABLE IF NOT EXISTS ${u.ll.identifier(n)}.${u.ll.identifier(r)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,u.ll)`CREATE SCHEMA IF NOT EXISTS ${u.ll.identifier(n)}`),await t.execute(s);let l=(await t.all((0,u.ll)`select id, hash, created_at from ${u.ll.identifier(n)}.${u.ll.identifier(r)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let i of e)if(!l||Number(l.created_at)<i.folderMillis){for(let e of i.sql)await t.execute(u.ll.raw(e));await t.execute((0,u.ll)`insert into ${u.ll.identifier(n)}.${u.ll.identifier(r)} ("hash", "created_at") values(${i.hash}, ${i.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,u.ll)`with `];for(let[i,r]of e.entries())t.push((0,u.ll)`${u.ll.identifier(r._.alias)} as (${r._.sql})`),i<e.length-1&&t.push((0,u.ll)`, `);return t.push((0,u.ll)` `),u.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:r}){let n=this.buildWithCTE(r),s=i?(0,u.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,l=t?(0,u.ll)` where ${t}`:void 0;return(0,u.ll)`${n}delete from ${e}${l}${s}`}buildUpdateSet(e,t){let i=e[c.XI.Symbol.Columns],r=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),n=r.length;return u.ll.join(r.flatMap((e,r)=>{let s=i[e],l=t[e]??u.ll.param(s.onUpdateFn(),s),a=(0,u.ll)`${u.ll.identifier(this.casing.getColumnCasing(s))} = ${l}`;return r<n-1?[a,u.ll.raw(", ")]:[a]}))}buildUpdateQuery({table:e,set:t,where:i,returning:r,withList:n,from:s,joins:l}){let a=this.buildWithCTE(n),o=e[E.mu.Symbol.Name],c=e[E.mu.Symbol.Schema],h=e[E.mu.Symbol.OriginalName],f=o===h?void 0:o,d=(0,u.ll)`${c?(0,u.ll)`${u.ll.identifier(c)}.`:void 0}${u.ll.identifier(h)}${f&&(0,u.ll)` ${u.ll.identifier(f)}`}`,p=this.buildUpdateSet(e,t),m=s&&u.ll.join([u.ll.raw(" from "),this.buildFromTable(s)]),g=this.buildJoins(l),y=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!s})}`:void 0,b=i?(0,u.ll)` where ${i}`:void 0;return(0,u.ll)`${a}update ${d} set ${p}${m}${g}${b}${y}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,r=e.flatMap(({field:e},r)=>{let s=[];if((0,n.is)(e,u.Xs.Aliased)&&e.isSelectionField)s.push(u.ll.identifier(e.fieldAlias));else if((0,n.is)(e,u.Xs.Aliased)||(0,n.is)(e,u.Xs)){let i=(0,n.is)(e,u.Xs.Aliased)?e.sql:e;t?s.push(new u.Xs(i.queryChunks.map(e=>(0,n.is)(e,$.Kl)?u.ll.identifier(this.casing.getColumnCasing(e)):e))):s.push(i),(0,n.is)(e,u.Xs.Aliased)&&s.push((0,u.ll)` as ${u.ll.identifier(e.fieldAlias)}`)}else(0,n.is)(e,o.V)&&(t?s.push(u.ll.identifier(this.casing.getColumnCasing(e))):s.push(e));return r<i-1&&s.push((0,u.ll)`, `),s});return u.ll.join(r)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[i,r]of e.entries()){0===i&&t.push((0,u.ll)` `);let s=r.table,l=r.lateral?(0,u.ll)` lateral`:void 0;if((0,n.is)(s,E.mu)){let e=s[E.mu.Symbol.Name],i=s[E.mu.Symbol.Schema],n=s[E.mu.Symbol.OriginalName],a=e===n?void 0:r.alias;t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${l} ${i?(0,u.ll)`${u.ll.identifier(i)}.`:void 0}${u.ll.identifier(n)}${a&&(0,u.ll)` ${u.ll.identifier(a)}`} on ${r.on}`)}else if((0,n.is)(s,u.Ss)){let e=s[h.n].name,i=s[h.n].schema,n=s[h.n].originalName,a=e===n?void 0:r.alias;t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${l} ${i?(0,u.ll)`${u.ll.identifier(i)}.`:void 0}${u.ll.identifier(n)}${a&&(0,u.ll)` ${u.ll.identifier(a)}`} on ${r.on}`)}else t.push((0,u.ll)`${u.ll.raw(r.joinType)} join${l} ${s} on ${r.on}`);i<e.length-1&&t.push((0,u.ll)` `)}return u.ll.join(t)}buildFromTable(e){if((0,n.is)(e,c.XI)&&e[c.XI.Symbol.OriginalName]!==e[c.XI.Symbol.Name]){let t=(0,u.ll)`${u.ll.identifier(e[c.XI.Symbol.OriginalName])}`;return e[c.XI.Symbol.Schema]&&(t=(0,u.ll)`${u.ll.identifier(e[c.XI.Symbol.Schema])}.${t}`),(0,u.ll)`${t} ${u.ll.identifier(e[c.XI.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:r,having:s,table:l,joins:a,orderBy:f,groupBy:d,limit:p,offset:m,lockingClause:g,distinct:y,setOperators:b}){let w,v,S,T=i??(0,K.He)(t);for(let e of T){let t;if((0,n.is)(e.field,o.V)&&(0,c.Io)(e.field.table)!==((0,n.is)(l,X.n)?l._.alias:(0,n.is)(l,M)?l[h.n].name:(0,n.is)(l,u.Xs)?void 0:(0,c.Io)(l))&&(t=e.field.table,!a?.some(({alias:e})=>e===(t[c.XI.Symbol.IsAlias]?(0,c.Io)(t):t[c.XI.Symbol.BaseName])))){let t=(0,c.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let P=!a||0===a.length,N=this.buildWithCTE(e);y&&(w=!0===y?(0,u.ll)` distinct`:(0,u.ll)` distinct on (${u.ll.join(y.on,(0,u.ll)`, `)})`);let $=this.buildSelection(T,{isSingleTable:P}),x=this.buildFromTable(l),A=this.buildJoins(a),C=r?(0,u.ll)` where ${r}`:void 0,O=s?(0,u.ll)` having ${s}`:void 0;f&&f.length>0&&(v=(0,u.ll)` order by ${u.ll.join(f,(0,u.ll)`, `)}`),d&&d.length>0&&(S=(0,u.ll)` group by ${u.ll.join(d,(0,u.ll)`, `)}`);let q="object"==typeof p||"number"==typeof p&&p>=0?(0,u.ll)` limit ${p}`:void 0,_=m?(0,u.ll)` offset ${m}`:void 0,j=u.ll.empty();if(g){let e=(0,u.ll)` for ${u.ll.raw(g.strength)}`;g.config.of&&e.append((0,u.ll)` of ${u.ll.join(Array.isArray(g.config.of)?g.config.of:[g.config.of],(0,u.ll)`, `)}`),g.config.noWait?e.append((0,u.ll)` no wait`):g.config.skipLocked&&e.append((0,u.ll)` skip locked`),j.append(e)}let E=(0,u.ll)`${N}select${w} ${$} from ${x}${A}${C}${S}${O}${v}${q}${_}${j}`;return b.length>0?this.buildSetOperations(E,b):E}buildSetOperations(e,t){let[i,...r]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===r.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),r)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:r,limit:s,orderBy:l,offset:a}}){let o,c=(0,u.ll)`(${e.getSQL()}) `,h=(0,u.ll)`(${r.getSQL()})`;if(l&&l.length>0){let e=[];for(let t of l)if((0,n.is)(t,$.Kl))e.push(u.ll.identifier(t.name));else if((0,n.is)(t,u.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,n.is)(i,$.Kl)&&(t.queryChunks[e]=u.ll.identifier(i.name))}e.push((0,u.ll)`${t}`)}else e.push((0,u.ll)`${t}`);o=(0,u.ll)` order by ${u.ll.join(e,(0,u.ll)`, `)} `}let f="object"==typeof s||"number"==typeof s&&s>=0?(0,u.ll)` limit ${s}`:void 0,d=u.ll.raw(`${t} ${i?"all ":""}`),p=a?(0,u.ll)` offset ${a}`:void 0;return(0,u.ll)`${c}${d}${h}${o}${f}${p}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:r,withList:s,select:l,overridingSystemValue_:a}){let o=[],h=Object.entries(e[c.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),f=h.map(([,e])=>u.ll.identifier(this.casing.getColumnCasing(e)));if(l)(0,n.is)(t,u.Xs)?o.push(t):o.push(t.getSQL());else for(let[e,i]of(o.push(u.ll.raw("values ")),t.entries())){let r=[];for(let[e,t]of h){let s=i[e];if(void 0===s||(0,n.is)(s,u.Iw)&&void 0===s.value)if(void 0!==t.defaultFn){let e=t.defaultFn(),i=(0,n.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else if(t.default||void 0===t.onUpdateFn)r.push((0,u.ll)`default`);else{let e=t.onUpdateFn(),i=(0,n.is)(e,u.Xs)?e:u.ll.param(e,t);r.push(i)}else r.push(s)}o.push(r),e<t.length-1&&o.push((0,u.ll)`, `)}let d=this.buildWithCTE(s),p=u.ll.join(o),m=r?(0,u.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,g=i?(0,u.ll)` on conflict ${i}`:void 0,y=!0===a?(0,u.ll)`overriding system value `:void 0;return(0,u.ll)`${d}insert into ${e} ${f} ${y}${p}${g}${m}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:i}){let r=t?(0,u.ll)` concurrently`:void 0,n=i?(0,u.ll)` with no data`:void 0;return(0,u.ll)`refresh materialized view${r} ${e}${n}`}prepareTyping(e){if((0,n.is)(e,x.kn)||(0,n.is)(e,A.iX))return"json";if((0,n.is)(e,C.Z5))return"decimal";if((0,n.is)(e,O.Xd))return"time";if((0,n.is)(e,q.KM)||(0,n.is)(e,q.xQ))return"timestamp";if((0,n.is)(e,_.qw)||(0,n.is)(e,_.dw))return"date";else if((0,n.is)(e,j.dL))return"uuid";else return"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:r,tableConfig:s,queryConfig:l,tableAlias:a,nestedQueryRelation:h,joinOn:f}){let d,p=[],w,v,S=[],T,N=[];if(!0===l)p=Object.entries(s.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:g(t,a),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let r=Object.fromEntries(Object.entries(s.columns).map(([e,t])=>[e,g(t,a)]));if(l.where){let e="function"==typeof l.where?l.where(r,{and:L.Uo,between:L.Tq,eq:L.eq,exists:L.t2,gt:L.gt,gte:L.RO,ilike:L.B3,inArray:L.RV,isNull:L.kZ,isNotNull:L.Pe,like:L.mj,lt:L.lt,lte:L.wJ,ne:L.ne,not:L.AU,notBetween:L.o8,notExists:L.KJ,notLike:L.RK,notIlike:L.q1,notInArray:L.KL,or:L.or,sql:u.ll}):l.where;T=e&&b(e,a)}let h=[],f=[];if(l.columns){let e=!1;for(let[t,i]of Object.entries(l.columns))void 0!==i&&t in s.columns&&(e||!0!==i||(e=!0),f.push(t));f.length>0&&(f=e?f.filter(e=>l.columns?.[e]===!0):Object.keys(s.columns).filter(e=>!f.includes(e)))}else f=Object.keys(s.columns);for(let e of f){let t=s.columns[e];h.push({tsKey:e,value:t})}let d=[];if(l.with&&(d=Object.entries(l.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:s.relations[e]}))),l.extras)for(let[e,t]of Object.entries("function"==typeof l.extras?l.extras(r,{sql:u.ll}):l.extras))h.push({tsKey:e,value:y(t,a)});for(let{tsKey:e,value:t}of h)p.push({dbKey:(0,n.is)(t,u.Xs.Aliased)?t.fieldAlias:s.columns[e].name,tsKey:e,field:(0,n.is)(t,o.V)?g(t,a):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let m="function"==typeof l.orderBy?l.orderBy(r,{sql:u.ll,asc:k,desc:D}):l.orderBy??[];for(let{tsKey:r,queryConfig:s,relation:h}of(Array.isArray(m)||(m=[m]),S=m.map(e=>(0,n.is)(e,o.V)?g(e,a):b(e,a)),w=l.limit,v=l.offset,d)){let l=function(e,t,i){if((0,n.is)(i,F)&&i.config)return{fields:i.config.fields,references:i.config.references};let r=t[(0,c.Lf)(i.referencedTable)];if(!r)throw Error(`Table "${i.referencedTable[c.XI.Symbol.Name]}" not found in schema`);let s=e[r];if(!s)throw Error(`Table "${r}" not found in schema`);let l=i.sourceTable,a=t[(0,c.Lf)(l)];if(!a)throw Error(`Table "${l[c.XI.Symbol.Name]}" not found in schema`);let o=[];for(let e of Object.values(s.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&o.push(e);if(o.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${r}"`):Error(`There are multiple relations between "${r}" and "${i.sourceTable[c.XI.Symbol.Name]}". Please specify relation name`);if(o[0]&&(0,n.is)(o[0],F)&&o[0].config)return{fields:o[0].config.references,references:o[0].config.fields};throw Error(`There is not enough information to infer relation "${a}.${i.fieldName}"`)}(t,i,h),o=i[(0,c.Lf)(h.referencedTable)],f=`${a}_${r}`,d=(0,L.Uo)(...l.fields.map((e,t)=>(0,L.eq)(g(l.references[t],f),g(e,a)))),m=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:e[o],tableConfig:t[o],queryConfig:(0,n.is)(h,F)?!0===s?{limit:1}:{...s,limit:1}:s,tableAlias:f,joinOn:d,nestedQueryRelation:h}),y=(0,u.ll)`${u.ll.identifier(f)}.${u.ll.identifier("data")}`.as(r);N.push({on:(0,u.ll)`true`,table:new X.n(m.sql,{},f),alias:f,joinType:"left",lateral:!0}),p.push({dbKey:r,tsKey:r,field:y,relationTableTsKey:o,isJson:!0,selection:m.selection})}}if(0===p.length)throw new P({message:`No fields selected for table "${s.tsName}" ("${a}")`});if(T=(0,L.Uo)(f,T),h){let e=(0,u.ll)`json_build_array(${u.ll.join(p.map(({field:e,tsKey:t,isJson:i})=>i?(0,u.ll)`${u.ll.identifier(`${a}_${t}`)}.${u.ll.identifier("data")}`:(0,n.is)(e,u.Xs.Aliased)?e.sql:e),(0,u.ll)`, `)})`;(0,n.is)(h,V)&&(e=(0,u.ll)`coalesce(json_agg(${e}${S.length>0?(0,u.ll)` order by ${u.ll.join(S,(0,u.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:s.tsName,selection:p}];void 0!==w||void 0!==v||S.length>0?(d=this.buildSelectQuery({table:m(r,a),fields:{},fieldsFlat:[{path:[],field:u.ll.raw("*")}],where:T,limit:w,offset:v,orderBy:S,setOperators:[]}),T=void 0,w=void 0,v=void 0,S=[]):d=m(r,a),d=this.buildSelectQuery({table:(0,n.is)(d,E.mu)?d:new X.n(d,{},a),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,n.is)(e,o.V)?g(e,a):e})),joins:N,where:T,limit:w,offset:v,orderBy:S,setOperators:[]})}else d=this.buildSelectQuery({table:m(r,a),fields:{},fieldsFlat:p.map(({field:e})=>({path:[],field:(0,n.is)(e,o.V)?g(e,a):e})),joins:N,where:T,limit:w,offset:v,orderBy:S,setOperators:[]});return{tableTsKey:s.tsName,sql:d,selection:p}}}class J{static [n.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===h.n)return{...e[h.n],selectedFields:new Proxy(e[h.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,n.is)(e,X.n)?e._.selectedFields:(0,n.is)(e,u.Ss)?e[h.n].selectedFields:e)[t];if((0,n.is)(i,u.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,n.is)(i,u.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,n.is)(i,o.V)?this.config.alias?new Proxy(i,new f(new Proxy(i.table,new d(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new J(this.config))}}class G{static [n.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class W{static [n.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}var H=i(99511);class Y{static [n.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t,i=!!this.fields;return t=this.fields?this.fields:(0,n.is)(e,X.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,n.is)(e,M)?e[h.n].selectedFields:(0,n.is)(e,u.Xs)?{}:(0,K.YD)(e),void 0===this.authToken?new ee({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}):new ee({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class Z extends G{static [n.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:i,session:r,dialect:n,withList:s,distinct:l}){super(),this.config={withList:s,table:e,fields:{...t},distinct:l,setOperators:[]},this.isPartialSelect=i,this.session=r,this.dialect=n,this._={selectedFields:t},this.tableName=(0,K.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e){return(t,i)=>{let r=this.tableName,s=(0,K.zN)(t);if("string"==typeof s&&this.config.joins?.some(e=>e.alias===s))throw Error(`Alias "${s}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof r&&(this.config.fields={[r]:this.config.fields}),"string"==typeof s&&!(0,n.is)(t,u.Xs))){let e=(0,n.is)(t,X.n)?t._.selectedFields:(0,n.is)(t,u.Ss)?t[h.n].selectedFields:t[c.XI.Symbol.Columns];this.config.fields[s]=e}if("function"==typeof i&&(i=i(new Proxy(this.config.fields,new J({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:i,table:t,joinType:e,alias:s}),"string"==typeof s)switch(e){case"left":this.joinsNotNullableMap[s]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[s]=!0;break;case"inner":this.joinsNotNullableMap[s]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[s]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");createSetOperator(e,t){return i=>{let r="function"==typeof i?i(ei()):i;if(!(0,K.DV)(this.getSelectedFields(),r.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:r}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new J({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new J({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new J({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new J({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new X.n(this.getSQL(),this.config.fields,e),new J({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new J({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class ee extends Z{static [n.i]="PgSelect";_prepare(e){let{session:t,config:i,dialect:r,joinsNotNullableMap:n,authToken:s}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");return H.k.startActiveSpan("drizzle.prepareQuery",()=>{let l=(0,K.He)(i.fields),a=t.prepareQuery(r.sqlToQuery(this.getSQL()),l,e,!0);return a.joinsNotNullableMap=n,void 0===s?a:a.setToken(s)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function et(e,t){return(i,r,...n)=>{let s=[r,...n].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of s)if(!(0,K.DV)(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(s)}}(0,K.XJ)(ee,[W]);let ei=()=>({union:er,unionAll:en,intersect:es,intersectAll:el,except:ea,exceptAll:eo}),er=et("union",!1),en=et("union",!0),es=et("intersect",!1),el=et("intersect",!0),ea=et("except",!1),eo=et("except",!0);class eu{static [n.i]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,n.is)(e,R)?e:void 0,this.dialectConfig=(0,n.is)(e,R)?void 0:e}$with(e){let t=this;return{as:i=>("function"==typeof i&&(i=i(t)),new Proxy(new X.J(i.getSQL(),i.getSelectedFields(),e,!0),new J({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(i){return new Y({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new Y({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,i){return new Y({fields:i??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new Y({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new Y({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new Y({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new R(this.dialectConfig)),this.dialect}}class ec{constructor(e,t,i,r){this.table=e,this.session=t,this.dialect=i,this.withList=r}static [n.i]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return void 0===this.authToken?new eh(this.table,(0,K.q)(this.table,e),this.session,this.dialect,this.withList):new eh(this.table,(0,K.q)(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class eh extends W{constructor(e,t,i,r,n){super(),this.session=i,this.dialect=r,this.config={set:t,table:e,withList:n,joins:[]},this.tableName=(0,K.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [n.i]="PgUpdate";config;tableName;joinsNotNullableMap;from(e){let t=(0,K.zN)(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return(0,n.is)(e,E.mu)?e[c.XI.Symbol.Columns]:(0,n.is)(e,X.n)?e._.selectedFields:e[h.n].selectedFields}createJoin(e){return(t,i)=>{let r=(0,K.zN)(t);if("string"==typeof r&&this.config.joins.some(e=>e.alias===r))throw Error(`Alias "${r}" is already used in this query`);if("function"==typeof i){let e=this.config.from&&!(0,n.is)(this.config.from,u.Xs)?this.getTableLikeFields(this.config.from):void 0;i=i(new Proxy(this.config.table[c.XI.Symbol.Columns],new J({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new J({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:i,table:t,joinType:e,alias:r}),"string"==typeof r)switch(e){case"left":this.joinsNotNullableMap[r]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!0;break;case"inner":this.joinsNotNullableMap[r]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[r]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[c.XI.Symbol.Columns]),this.config.from)){let t=(0,K.zN)(this.config.from);if("string"==typeof t&&this.config.from&&!(0,n.is)(this.config.from,u.Xs)){let i=this.getTableLikeFields(this.config.from);e[t]=i}for(let t of this.config.joins){let i=(0,K.zN)(t.table);if("string"==typeof i&&!(0,n.is)(t.table,u.Xs)){let r=this.getTableLikeFields(t.table);e[i]=r}}}return this.config.returning=(0,K.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);$dynamic(){return this}}class ef{constructor(e,t,i,r,n){this.table=e,this.session=t,this.dialect=i,this.withList=r,this.overridingSystemValue_=n}static [n.i]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[c.XI.Symbol.Columns];for(let r of Object.keys(e)){let s=e[r];t[r]=(0,n.is)(s,u.Xs)?s:new u.Iw(s,i[r])}return t});return void 0===this.authToken?new ed(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_):new ed(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new eu):e;if(!(0,n.is)(t,u.Xs)&&!(0,K.DV)(this.table[c.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new ed(this.table,t,this.session,this.dialect,this.withList,!0)}}class ed extends W{constructor(e,t,i,r,n,s,l){super(),this.session=i,this.dialect=r,this.config={table:e,values:t,withList:n,select:s,overridingSystemValue_:l}}static [n.i]="PgInsert";config;returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returning=(0,K.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,u.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let i=e.where?(0,u.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,u.ll)`(${u.ll.raw(t)})${i} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,u.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,u.ll)` where ${e.targetWhere}`:void 0,r=e.setWhere?(0,u.ll)` where ${e.setWhere}`:void 0,n=this.dialect.buildUpdateSet(this.config.table,(0,K.q)(this.config.table,e.set)),s="";return s=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=(0,u.ll)`(${u.ll.raw(s)})${i} do update set ${n}${t}${r}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));$dynamic(){return this}}class ep extends W{constructor(e,t,i,r){super(),this.session=t,this.dialect=i,this.config={table:e,withList:r}}static [n.i]="PgDelete";config;where(e){return this.config.where=e,this}returning(e=this.config.table[c.XI.Symbol.Columns]){return this.config.returning=(0,K.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));$dynamic(){return this}}class em extends u.Xs{constructor(e){super(em.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=em.buildCount(e.source,e.filters)}sql;token;static [n.i]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return(0,u.ll)`(select count(*) from ${e}${u.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,u.ll)`select count(*) as count from ${e}${u.ll.raw(" where ").if(t)}${t};`}setToken(e){this.token=e}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}class eg{constructor(e,t,i,r,n,s,l){this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=l}static [n.i]="PgRelationalQueryBuilder";findMany(e){return new ey(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new ey(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class ey extends W{constructor(e,t,i,r,n,s,l,a,o){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=l,this.config=a,this.mode=o}static [n.i]="PgRelationalQuery";_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:i}=this._toSQL();return this.session.prepareQuery(i,void 0,e,!0,(e,i)=>{let r=e.map(e=>(function e(t,i,r,s,l=e=>e){let a={};for(let[c,h]of s.entries())if(h.isJson){let s=i.relations[h.tsKey],o=r[c],u="string"==typeof o?JSON.parse(o):o;a[h.tsKey]=(0,n.is)(s,F)?u&&e(t,t[h.relationTableTsKey],u,h.selection,l):u.map(i=>e(t,t[h.relationTableTsKey],i,h.selection,l))}else{let e,t=l(r[c]),i=h.field;e=(0,n.is)(i,o.V)?i:(0,n.is)(i,u.Xs)?i.decoder:i.sql.decoder,a[h.tsKey]=null===t?null:e.mapFromDriverValue(t)}return a})(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?r[0]:r})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class eb extends W{constructor(e,t,i,r){super(),this.execute=e,this.sql=t,this.query=i,this.mapBatchResult=r}static [n.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class ew extends W{constructor(e,t,i){super(),this.session=t,this.dialect=i,this.config={view:e}}static [n.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return H.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>H.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class ev{constructor(e,t,i){if(this.dialect=e,this.session=t,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[r,n]of Object.entries(this._.schema))this.query[r]=new eg(i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[r],n,e,t)}static [n.i]="PgDatabase";query;$with(e){let t=this;return{as:i=>("function"==typeof i&&(i=i(new eu(t.dialect))),new Proxy(new X.J(i.getSQL(),i.getSelectedFields(),e,!0),new J({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}$count(e,t){return new em({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(i){return new Y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new Y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(i,r){return new Y({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:i}})},update:function(i){return new ec(i,t.session,t.dialect,e)},insert:function(i){return new ef(i,t.session,t.dialect,e)},delete:function(i){return new ep(i,t.session,t.dialect,e)}}}select(e){return new Y({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new Y({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new Y({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new ec(e,this.session,this.dialect)}insert(e){return new ef(e,this.session,this.dialect)}delete(e){return new ep(e,this.session,this.dialect)}refreshMaterializedView(e){return new ew(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?u.ll.raw(e):e.getSQL(),i=this.dialect.sqlToQuery(t),r=this.session.prepareQuery(i,void 0,void 0,!1);return new eb(()=>r.execute(void 0,this.authToken),t,i,e=>r.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}class eS{constructor(e){this.query=e}authToken;getQuery(){return this.query}mapResult(e,t){return e}setToken(e){return this.authToken=e,this}static [n.i]="PgPreparedQuery";joinsNotNullableMap}class eT{constructor(e){this.dialect=e}static [n.i]="PgSession";execute(e,t){return H.k.startActiveSpan("drizzle.operation",()=>H.k.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).setToken(t).execute(void 0,t))}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}async count(e,t){return Number((await this.execute(e,t))[0].count)}}class eP extends ev{constructor(e,t,i,r=0){super(e,t,i),this.schema=i,this.nestedIndex=r}static [n.i]="PgTransaction";rollback(){throw new N}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),u.ll.raw(t.join(" "))}setTransaction(e){return this.session.execute((0,u.ll)`set transaction ${this.getTransactionConfigSQL(e)}`)}}class eN extends eS{constructor(e,t,i,r,n,s,l){super({sql:t,params:i}),this.client=e,this.queryString=t,this.params=i,this.logger=r,this.fields=n,this._isResponseInArrayMode=s,this.customResultMapper=l}static [n.i]="PostgresJsPreparedQuery";async execute(e={}){return H.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,u.Ct)(this.params,e);t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i);let{fields:r,queryString:n,client:s,joinsNotNullableMap:l,customResultMapper:a}=this;if(!r&&!a)return H.k.startActiveSpan("drizzle.driver.execute",()=>s.unsafe(n,i));let o=await H.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":n,"drizzle.query.params":JSON.stringify(i)}),s.unsafe(n,i).values()));return H.k.startActiveSpan("drizzle.mapResponse",()=>a?a(o):o.map(e=>(0,K.a6)(r,e,l)))})}all(e={}){return H.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,u.Ct)(this.params,e);return t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i),H.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.client.unsafe(this.queryString,i)))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class e$ extends eT{constructor(e,t,i,r={}){super(t),this.client=e,this.schema=i,this.options=r,this.logger=r.logger??new a}static [n.i]="PostgresJsSession";logger;prepareQuery(e,t,i,r,n){return new eN(this.client,e.sql,e.params,this.logger,t,r,n)}query(e,t){return this.logger.logQuery(e,t),this.client.unsafe(e,t).values()}queryObjects(e,t){return this.client.unsafe(e,t)}transaction(e,t){return this.client.begin(async i=>{let r=new e$(i,this.dialect,this.schema,this.options),n=new ex(this.dialect,r,this.schema);return t&&await n.setTransaction(t),e(n)})}}class ex extends eP{constructor(e,t,i,r=0){super(e,t,i,r),this.session=t}static [n.i]="PostgresJsTransaction";transaction(e){return this.session.client.savepoint(t=>{let i=new e$(t,this.dialect,this.schema,this.session.options);return e(new ex(this.dialect,i,this.schema))})}}class eA extends ev{static [n.i]="PostgresJsDatabase"}function eC(e,t={}){let i,r,s=e=>e;for(let t of["1184","1082","1083","1114"])e.options.parsers[t]=s,e.options.serializers[t]=s;e.options.serializers["114"]=s,e.options.serializers["3802"]=s;let a=new R({casing:t.casing});if(!0===t.logger?i=new l:!1!==t.logger&&(i=t.logger),t.schema){let e=function(e,t){1===Object.keys(e).length&&"default"in e&&!(0,n.is)(e.default,c.XI)&&(e=e.default);let i={},r={},s={};for(let[l,a]of Object.entries(e))if((0,n.is)(a,c.XI)){let e=(0,c.Lf)(a),t=r[e];for(let r of(i[e]=l,s[l]={tsName:l,dbName:a[c.XI.Symbol.Name],schema:a[c.XI.Symbol.Schema],columns:a[c.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(a[c.XI.Symbol.Columns])))r.primary&&s[l].primaryKey.push(r);let o=a[c.XI.Symbol.ExtraConfigBuilder]?.(a[c.XI.Symbol.ExtraConfigColumns]);if(o)for(let e of Object.values(o))(0,n.is)(e,I)&&s[l].primaryKey.push(...e.columns)}else if((0,n.is)(a,Q)){let e,n=(0,c.Lf)(a.table),l=i[n];for(let[i,o]of Object.entries(a.config(t(a.table))))if(l){let t=s[l];t.relations[i]=o,e&&t.primaryKey.push(...e)}else n in r||(r[n]={relations:{},primaryKey:e}),r[n].relations[i]=o}return{tables:s,tableNamesMap:i}}(t.schema,U);r={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let o=new e$(e,a,r,{logger:i}),u=new eA(a,o,r);return u.$client=e,u}function eO(...e){if("string"==typeof e[0])return eC((0,r.A)(e[0]),e[1]);if((0,K.Lq)(e[0])){let{connection:t,client:i,...n}=e[0];if(i)return eC(i,n);if("object"==typeof t&&void 0!==t.url){let{url:e,...i}=t;return eC((0,r.A)(e,i),n)}return eC((0,r.A)(t),n)}return eC(e[0],e[1])}(eO||(eO={})).mock=function(e){return eC({},e)}},43971:(e,t,i)=>{i.d(t,{A:()=>ev});var r=i(21820),n=i(29021);let s=new Map,l=new Map,a=Symbol("OriginError"),o={};class u extends Promise{constructor(e,t,i,r,n={}){let l,o;super((e,t)=>{l=e,o=t}),this.tagged=Array.isArray(e.raw),this.strings=e,this.args=t,this.handler=i,this.canceller=r,this.options=n,this.state=null,this.statement=null,this.resolve=e=>(this.active=!1,l(e)),this.reject=e=>(this.active=!1,o(e)),this.active=!1,this.cancelled=null,this.executed=!1,this.signature="",this[a]=this.handler.debug?Error():this.tagged&&function(e){if(s.has(e))return s.get(e);let t=Error.stackTraceLimit;return Error.stackTraceLimit=4,s.set(e,Error()),Error.stackTraceLimit=t,s.get(e)}(this.strings)}get origin(){return(this.handler.debug?this[a].stack:this.tagged&&l.has(this.strings)?l.get(this.strings):l.set(this.strings,this[a].stack).get(this.strings))||""}static get[Symbol.species](){return Promise}cancel(){return this.canceller&&(this.canceller(this),this.canceller=null)}simple(){return this.options.simple=!0,this.options.prepare=!1,this}async readable(){return this.simple(),this.streaming=!0,this}async writable(){return this.simple(),this.streaming=!0,this}cursor(e=1,t){let i;return(this.options.simple=!1,"function"==typeof e&&(t=e,e=1),this.cursorRows=e,"function"==typeof t)?(this.cursorFn=t,this):{[Symbol.asyncIterator]:()=>({next:()=>{if(this.executed&&!this.active)return{done:!0};i&&i();let e=new Promise((e,t)=>{this.cursorFn=t=>(e({value:t,done:!1}),new Promise(e=>i=e)),this.resolve=()=>(this.active=!1,e({done:!0})),this.reject=e=>(this.active=!1,t(e))});return this.execute(),e},return:()=>(i&&i(o),{done:!0})})}}describe(){return this.options.simple=!1,this.onlyDescribe=this.options.prepare=!0,this}stream(){throw Error(".stream has been renamed to .forEach")}forEach(e){return this.forEachFn=e,this.handle(),this}raw(){return this.isRaw=!0,this}values(){return this.isRaw="values",this}async handle(){!this.executed&&(this.executed=!0)&&await 1&&this.handler(this)}execute(){return this.handle(),this}then(){return this.handle(),super.then.apply(this,arguments)}catch(){return this.handle(),super.catch.apply(this,arguments)}finally(){return this.handle(),super.finally.apply(this,arguments)}}class c extends Error{constructor(e){super(e.message),this.name=this.constructor.name,Object.assign(this,e)}}let h={connection:function e(t,i,r){let{host:n,port:s}=r||i,l=Object.assign(Error("write "+t+" "+(i.path||n+":"+s)),{code:t,errno:t,address:i.path||n},i.path?{}:{port:s});return Error.captureStackTrace(l,e),l},postgres:function e(t){let i=new c(t);return Error.captureStackTrace(i,e),i},generic:function e(t,i){let r=Object.assign(Error(t+": "+i),{code:t});return Error.captureStackTrace(r,e),r},notSupported:function e(t){let i=Object.assign(Error(t+" (B) is not supported"),{code:"MESSAGE_NOT_SUPPORTED",name:t});return Error.captureStackTrace(i,e),i}};class f{then(){$()}catch(){$()}finally(){$()}}class d extends f{constructor(e){super(),this.value=_(e)}}class p extends f{constructor(e,t,i){super(),this.value=e,this.type=t,this.array=i}}class m extends f{constructor(e,t){super(),this.first=e,this.rest=t}build(e,t,i,r){let n=N.map(([t,i])=>({fn:i,i:e.search(t)})).sort((e,t)=>e.i-t.i).pop();return -1===n.i?q(this.first,r):n.fn(this.first,this.rest,t,i,r)}}function g(e,t,i,r){let n=e instanceof p?e.value:e;if(void 0===n&&(e instanceof p?e.value=r.transform.undefined:n=e=r.transform.undefined,void 0===n))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return"$"+i.push(e instanceof p?(t.push(e.value),e.array?e.array[e.type||j(e.value)]||e.type||function e(t){return Array.isArray(t)?e(t[0]):1009*("string"==typeof t)}(e.value):e.type):(t.push(e),j(e)))}let y=O({string:{to:25,from:null,serialize:e=>""+e},number:{to:0,from:[21,23,26,700,701],serialize:e=>""+e,parse:e=>+e},json:{to:114,from:[114,3802],serialize:e=>JSON.stringify(e),parse:e=>JSON.parse(e)},boolean:{to:16,from:16,serialize:e=>!0===e?"t":"f",parse:e=>"t"===e},date:{to:1184,from:[1082,1114,1184],serialize:e=>(e instanceof Date?e:new Date(e)).toISOString(),parse:e=>new Date(e)},bytea:{to:17,from:17,serialize:e=>"\\x"+Buffer.from(e).toString("hex"),parse:e=>Buffer.from(e.slice(2),"hex")}});function b(e,t,i,r,n,s){for(let l=1;l<e.strings.length;l++)t+=w(t,i,r,n,s)+e.strings[l],i=e.args[l];return t}function w(e,t,i,r,n){return t instanceof m?t.build(e,i,r,n):t instanceof u?v(t,i,r,n):t instanceof d?t.value:t&&t[0]instanceof u?t.reduce((e,t)=>e+" "+v(t,i,r,n),""):g(t,i,r,n)}function v(e,t,i,r){return e.fragment=!0,b(e,e.strings[0],e.args[0],t,i,r)}function S(e,t,i,r,n){return e.map(e=>"("+r.map(r=>w("values",e[r],t,i,n)).join(",")+")").join(",")}function T(e,t,i,r,n){let s=Array.isArray(e[0]),l=t.length?t.flat():Object.keys(s?e[0]:e);return S(s?e:[e],i,r,l,n)}function P(e,t,i,r,n){let s;return("string"==typeof e&&(e=[e].concat(t)),Array.isArray(e))?q(e,n):(t.length?t.flat():Object.keys(e)).map(t=>((s=e[t])instanceof u?v(s,i,r,n):s instanceof d?s.value:g(s,i,r,n))+" as "+_(n.transform.column.to?n.transform.column.to(t):t)).join(",")}let N=Object.entries({values:T,in:(...e)=>{let t=T(...e);return"()"===t?"(null)":t},select:P,as:P,returning:P,"\\(":P,update:(e,t,i,r,n)=>(t.length?t.flat():Object.keys(e)).map(t=>_(n.transform.column.to?n.transform.column.to(t):t)+"="+w("values",e[t],i,r,n)),insert(e,t,i,r,n){let s=t.length?t.flat():Object.keys(Array.isArray(e)?e[0]:e);return"("+q(s,n)+")values"+S(Array.isArray(e)?e:[e],i,r,s,n)}}).map(([e,t])=>[RegExp("((?:^|[\\s(])"+e+"(?:$|[\\s(]))(?![\\s\\S]*\\1)","i"),t]);function $(){throw h.generic("NOT_TAGGED_CALL","Query not called as a tagged template literal")}let x=y.serializers,A=y.parsers,C=function(e){let t=O(e||{});return{serializers:Object.assign({},x,t.serializers),parsers:Object.assign({},A,t.parsers)}};function O(e){return Object.keys(e).reduce((t,i)=>(e[i].from&&[].concat(e[i].from).forEach(r=>t.parsers[r]=e[i].parse),e[i].serialize&&(t.serializers[e[i].to]=e[i].serialize,e[i].from&&[].concat(e[i].from).forEach(r=>t.serializers[r]=e[i].serialize)),t),{parsers:{},serializers:{}})}function q(e,{transform:{column:t}}){return e.map(e=>_(t.to?t.to(e):e)).join(",")}let _=function(e){return'"'+e.replace(/"/g,'""').replace(/\./g,'"."')+'"'},j=function e(t){return t instanceof p?t.type:t instanceof Date?1184:t instanceof Uint8Array?17:!0===t||!1===t?16:"bigint"==typeof t?20:Array.isArray(t)?e(t[0]):0},E=/\\/g,I=/"/g,B=function e(t,i,r,n){if(!1===Array.isArray(t))return t;if(!t.length)return"{}";let s=t[0],l=1020===n?";":",";return Array.isArray(s)&&!s.type?"{"+t.map(t=>e(t,i,r,n)).join(l)+"}":"{"+t.map(e=>{if(void 0===e&&void 0===(e=r.transform.undefined))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return null===e?"null":'"'+(i?i(e.type?e.value:e):""+e).replace(E,"\\\\").replace(I,'\\"')+'"'}).join(l)+"}"},L={i:0,char:null,str:"",quoted:!1,last:0},k=e=>{let t=e[0];for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},D=e=>{let t=e[0].toUpperCase();for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},z=e=>e.replace(/_/g,"-"),Q=e=>e.replace(/([A-Z])/g,"_$1").toLowerCase(),F=e=>(e.slice(0,1)+e.slice(1).replace(/([A-Z])/g,"_$1")).toLowerCase(),V=e=>e.replace(/-/g,"_");function U(e){return function t(i,r){return"object"==typeof i&&null!==i&&(114===r.type||3802===r.type)?Array.isArray(i)?i.map(e=>t(e,r)):Object.entries(i).reduce((i,[n,s])=>Object.assign(i,{[e(n)]:t(s,r)}),{}):i}}k.column={from:k},k.value={from:U(k)},Q.column={to:Q};let X={...k};X.column.to=Q,D.column={from:D},D.value={from:U(D)},F.column={to:F};let K={...D};K.column.to=F,z.column={from:z},z.value={from:U(z)},V.column={to:V};let M={...z};M.column.to=V;var R=i(91645),J=i(34631),G=i(55511),W=i(27910),H=i(74998);class Y extends Array{constructor(){super(),Object.defineProperties(this,{count:{value:null,writable:!0},state:{value:null,writable:!0},command:{value:null,writable:!0},columns:{value:null,writable:!0},statement:{value:null,writable:!0}})}static get[Symbol.species](){return Array}}let Z=function(e=[]){let t=e.slice(),i=0;return{get length(){return t.length-i},remove:e=>{let i=t.indexOf(e);return -1===i?null:(t.splice(i,1),e)},push:e=>(t.push(e),e),shift:()=>{let e=t[i++];return i===t.length?(i=0,t=[]):t[i-1]=void 0,e}}},ee=Buffer.allocUnsafe(256),et=Object.assign(function(){return et.i=0,et},"BCcDdEFfHPpQSX".split("").reduce((e,t)=>{let i=t.charCodeAt(0);return e[t]=()=>(ee[0]=i,et.i=5,et),e},{}),{N:"\0",i:0,inc:e=>(et.i+=e,et),str(e){let t=Buffer.byteLength(e);return ei(t),et.i+=ee.write(e,et.i,t,"utf8"),et},i16:e=>(ei(2),ee.writeUInt16BE(e,et.i),et.i+=2,et),i32:(e,t)=>(t||0===t?ee.writeUInt32BE(e,t):(ei(4),ee.writeUInt32BE(e,et.i),et.i+=4),et),z:e=>(ei(e),ee.fill(0,et.i,et.i+e),et.i+=e,et),raw:e=>(ee=Buffer.concat([ee.subarray(0,et.i),e]),et.i=ee.length,et),end(e=1){ee.writeUInt32BE(et.i-e,e);let t=ee.subarray(0,et.i);return et.i=0,ee=Buffer.allocUnsafe(256),t}});function ei(e){if(ee.length-et.i<e){let t=ee,i=t.length;ee=Buffer.allocUnsafe(i+(i>>1)+e),t.copy(ee)}}let er=function e(t,i={},{onopen:r=ec,onend:n=ec,onclose:s=ec}={}){let{ssl:l,max:a,user:c,host:f,port:d,database:p,parsers:m,transform:y,onnotice:w,onnotify:v,onparameter:S,max_pipeline:T,keep_alive:P,backoff:N,target_session_attrs:$}=t,x=Z(),A=en++,C={pid:null,secret:null},O=eg(eM,t.idle_timeout),q=eg(eM,t.max_lifetime),_=eg(function(){eX(h.connection("CONNECT_TIMEOUT",t,j)),j.destroy()},t.connect_timeout),j=null,E,I=new Y,k=Buffer.alloc(0),D=t.fetch_types,z={},Q={},F=Math.random().toString(36).slice(2),V=1,U=0,X=0,K=0,M=0,ee=0,ei=0,er=0,ef=null,ey=null,eb=!1,ew=null,ev=null,eS=null,eT=null,eP=null,eN=null,e$=null,ex=null,eA=null,eC=null,eO={queue:i.closed,idleTimer:O,connect(e){eS=e,eF()},terminate:eR,execute:ej,cancel:e_,end:eM,count:0,id:A};return i.closed&&i.closed.push(eO),eO;async function eq(){let e;try{e=t.socket?await Promise.resolve(t.socket(t)):new R.Socket}catch(e){eU(e);return}return e.on("error",eU),e.on("close",eJ),e.on("drain",eD),e}async function e_({pid:e,secret:t},i,r){try{E=et().i32(16).i32(0x4d2162e).i32(e).i32(t).end(16),await eQ(),j.once("error",r),j.once("close",i)}catch(e){r(e)}}function ej(e){if(eb)return eK(e,h.connection("CONNECTION_DESTROYED",t));if(!e.cancelled)try{return e.state=C,eA?x.push(e):(eA=e).active=!0,function(e){let i=[],r=[],n=b(e,e.strings[0],e.args[0],i,r,t);e.tagged||e.args.forEach(e=>g(e,i,r,t)),e.prepare=t.prepare&&(!("prepare"in e.options)||e.options.prepare),e.string=n,e.signature=e.prepare&&r+n,e.onlyDescribe&&delete Q[e.signature],e.parameters=e.parameters||i,e.prepared=e.prepare&&e.signature in Q,e.describeFirst=e.onlyDescribe||i.length&&!e.prepared,e.statement=e.prepared?Q[e.signature]:{string:n,types:r,name:e.prepare?F+V++:""},"function"==typeof t.debug&&t.debug(A,n,i,r)}(e),eB(function(e){var t;if(e.parameters.length>=65534)throw h.generic("MAX_PARAMETERS_EXCEEDED","Max number of parameters (65534) exceeded");return e.options.simple?et().Q().str(e.statement.string+et.N).end():e.describeFirst?Buffer.concat([eE(e),el]):e.prepare?e.prepared?eI(e):Buffer.concat([eE(e),eI(e)]):(t=e,Buffer.concat([e4(t.statement.string,t.parameters,t.statement.types),eu,eI(t)]))}(e))&&!e.describeFirst&&!e.cursorFn&&x.length<T&&(!e.options.onexecute||e.options.onexecute(eO))}catch(e){return 0===x.length&&eB(es),eX(e),!0}}function eE(e){return Buffer.concat([e4(e.statement.string,e.parameters,e.statement.types,e.statement.name),function(e,t=""){return et().D().str("S").str(t+et.N).end()}("S",e.statement.name)])}function eI(e){return Buffer.concat([function(e,i,r="",n=""){let s,l;return et().B().str(n+et.N).str(r+et.N).i16(0).i16(e.length),e.forEach((r,n)=>{if(null===r)return et.i32(0xffffffff);l=i[n],e[n]=r=l in t.serializers?t.serializers[l](r):""+r,s=et.i,et.inc(4).str(r).i32(et.i-s-4,s)}),et.i16(0),et.end()}(e.parameters,e.statement.types,e.statement.name,e.cursorName),e.cursorFn?e5("",e.cursorRows):eo])}function eB(e,t){return(eN=eN?Buffer.concat([eN,e]):Buffer.from(e),t||eN.length>=1024)?eL(t):(null===ey&&(ey=setImmediate(eL)),!0)}function eL(e){let t=j.write(eN,e);return null!==ey&&clearImmediate(ey),eN=ey=null,t}async function ek(){if(eB(ea),!await new Promise(e=>j.once("data",t=>e(83===t[0])))&&"prefer"===l)return eV();j.removeAllListeners(),(j=J.connect({socket:j,servername:R.isIP(j.host)?void 0:j.host,..."require"===l||"allow"===l||"prefer"===l?{rejectUnauthorized:!1}:"verify-full"===l?{}:"object"==typeof l?l:{}})).on("secureConnect",eV),j.on("error",eU),j.on("close",eJ),j.on("drain",eD)}function eD(){eA||r(eO)}function ez(i){if(!ew||(ew.push(i),!((X-=i.length)>0)))for(k=ew?Buffer.concat(ew,ee-X):0===k.length?i:Buffer.concat([k,i],k.length+i.length);k.length>4;){if((ee=k.readUInt32BE(1))>=k.length){X=ee-k.length,ew=[k];break}try{!function(i,n=i[0]){(68===n?function(e){let t,i,r,n=7,s=eA.isRaw?Array(eA.statement.columns.length):{};for(let l=0;l<eA.statement.columns.length;l++)i=eA.statement.columns[l],t=e.readInt32BE(n),n+=4,r=-1===t?null:!0===eA.isRaw?e.subarray(n,n+=t):void 0===i.parser?e.toString("utf8",n,n+=t):!0===i.parser.array?i.parser(e.toString("utf8",n+1,n+=t)):i.parser(e.toString("utf8",n,n+=t)),eA.isRaw?s[l]=!0===eA.isRaw?r:y.value.from?y.value.from(r,i):r:s[i.name]=y.value.from?y.value.from(r,i):r;eA.forEachFn?eA.forEachFn(y.row.from?y.row.from(s):s,I):I[er++]=y.row.from?y.row.from(s):s}:100===n?function(e){eP&&(eP.push(e.subarray(5))||j.pause())}:65===n?function(e){if(!v)return;let t=9;for(;0!==e[t++];);v(e.toString("utf8",9,t-1),e.toString("utf8",t,e.length-1))}:83===n?function(e){let[i,r]=e.toString("utf8",5,e.length-1).split(et.N);z[i]=r,t.parameters[i]!==r&&(t.parameters[i]=r,S&&S(i,r))}:90===n?function(i){if(eA&&eA.options.simple&&eA.resolve(ev||I),eA=ev=null,I=new Y,_.cancel(),eS){if($)if(z.in_hot_standby&&z.default_transaction_read_only){var n,s;if(n=$,s=z,"read-write"===n&&"on"===s.default_transaction_read_only||"read-only"===n&&"off"===s.default_transaction_read_only||"primary"===n&&"on"===s.in_hot_standby||"standby"===n&&"off"===s.in_hot_standby||"prefer-standby"===n&&"off"===s.in_hot_standby&&t.host[M])return eR()}else{let e=new u([`
      show transaction_read_only;
      select pg_catalog.pg_is_in_recovery()
    `],[],ej,null,{simple:!0});e.resolve=([[e],[t]])=>{z.default_transaction_read_only=e.transaction_read_only,z.in_hot_standby=t.pg_is_in_recovery?"on":"off"},e.execute();return}return D?(eS.reserve&&(eS=null),e3()):(eS&&!eS.reserve&&ej(eS),t.shared.retries=M=0,void(eS=null))}for(;x.length&&(eA=x.shift())&&(eA.active=!0,eA.cancelled);)e(t).cancel(eA.state,eA.cancelled.resolve,eA.cancelled.reject);eA||(eO.reserved?eO.reserved.release||73!==i[5]?eO.reserved():eT?eR():(eO.reserved=null,r(eO)):eT?eR():r(eO))}:67===n?function(e){er=0;for(let t=e.length-1;t>0;t--)if(32===e[t]&&e[t+1]<58&&null===I.count&&(I.count=+e.toString("utf8",t+1,e.length-1)),e[t-1]>=65){I.command=e.toString("utf8",5,t),I.state=C;break}return(eC&&(eC(),eC=null),"BEGIN"!==I.command||1===a||eO.reserved)?eA.options.simple?eG():void(eA.cursorFn&&(I.count&&eA.cursorFn(I),eB(es)),eA.resolve(I)):eX(h.generic("UNSAFE_TRANSACTION","Only use sql.begin, sql.reserved or max: 1"))}:50===n?eG:49===n?function(){eA.parsing=!1}:116===n?function(e){let t=e.readUInt16BE(5);for(let i=0;i<t;++i)eA.statement.types[i]||(eA.statement.types[i]=e.readUInt32BE(7+4*i));eA.prepare&&(Q[eA.signature]=eA.statement),eA.describeFirst&&!eA.onlyDescribe&&(eB(eI(eA)),eA.describeFirst=!1)}:84===n?function(e){let t;I.command&&((ev=ev||[I]).push(I=new Y),I.count=null,eA.statement.columns=null);let i=e.readUInt16BE(5),r=7;eA.statement.columns=Array(i);for(let n=0;n<i;++n){for(t=r;0!==e[r++];);let i=e.readUInt32BE(r),s=e.readUInt16BE(r+4),l=e.readUInt32BE(r+6);eA.statement.columns[n]={name:y.column.from?y.column.from(e.toString("utf8",t,r-1)):e.toString("utf8",t,r-1),parser:m[l],table:i,number:s,type:l},r+=18}if(I.statement=eA.statement,eA.onlyDescribe)return eA.resolve(eA.statement),eB(es)}:82===n?eW:110===n?function(){if(I.statement=eA.statement,I.statement.columns=[],eA.onlyDescribe)return eA.resolve(eA.statement),eB(es)}:75===n?function(e){C.pid=e.readUInt32BE(5),C.secret=e.readUInt32BE(9)}:69===n?function(e){var t,i;eA&&(eA.cursorFn||eA.describeFirst)&&eB(es);let r=h.postgres(ed(e));eA&&eA.retried?eX(eA.retried):eA&&eA.prepared&&eh.has(r.routine)?(t=eA,i=r,delete Q[t.signature],t.retried=i,ej(t)):eX(r)}:115===n?e2:51===n?function(){I.count&&eA.cursorFn(I),eA.resolve(I)}:71===n?function(){eP=new W.Writable({autoDestroy:!0,write(e,t,i){j.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),j.write(et().f().str(e+et.N).end()),eP=null},final(e){j.write(et().c().end()),eC=e}}),eA.resolve(eP)}:78===n?function(e){w?w(ed(e)):console.log(ed(e))}:72===n?function(){eP=new W.Readable({read(){j.resume()}}),eA.resolve(eP)}:99===n?function(){eP&&eP.push(null),eP=null}:73===n?function(){}:86===n?function(){eX(h.notSupported("FunctionCallResponse"))}:118===n?function(){eX(h.notSupported("NegotiateProtocolVersion"))}:87===n?function(){eP=new W.Duplex({autoDestroy:!0,read(){j.resume()},write(e,t,i){j.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),j.write(et().f().str(e+et.N).end()),eP=null},final(e){j.write(et().c().end()),eC=e}}),eA.resolve(eP)}:function(e){console.error("Postgres.js : Unknown Message:",e[0])})(i)}(k.subarray(0,ee+1))}catch(e){eA&&(eA.cursorFn||eA.describeFirst)&&eB(es),eX(e)}k=k.subarray(ee+1),X=0,ew=null}}async function eQ(){if(eb=!1,z={},j||(j=await eq()),j){if(_.start(),t.socket)return l?ek():eV();if(j.on("connect",l?ek:eV),t.path)return j.connect(t.path);j.ssl=l,j.connect(d[K],f[K]),j.host=f[K],j.port=d[K],K=(K+1)%d.length}}function eF(){setTimeout(eQ,U?U+ei-H.performance.now():0)}function eV(){try{Q={},D=t.fetch_types,F=Math.random().toString(36).slice(2),V=1,q.start(),j.on("data",ez),P&&j.setKeepAlive&&j.setKeepAlive(!0,1e3*P);let e=E||et().inc(4).i16(3).z(2).str(Object.entries(Object.assign({user:c,database:p,client_encoding:"UTF8"},t.connection)).filter(([,e])=>e).map(([e,t])=>e+et.N+t).join(et.N)).z(2).end(0);eB(e)}catch(e){eU(e)}}function eU(e){if(eO.queue!==i.connecting||!t.host[M+1])for(eX(e);x.length;)eK(x.shift(),e)}function eX(e){eP&&(eP.destroy(e),eP=null),eA&&eK(eA,e),eS&&(eK(eS,e),eS=null)}function eK(e,i){if(e.reserve)return e.reject(i);i&&"object"==typeof i||(i=Error(i)),"query"in i||"parameters"in i||Object.defineProperties(i,{stack:{value:i.stack+e.origin.replace(/.*\n/,"\n"),enumerable:t.debug},query:{value:e.string,enumerable:t.debug},parameters:{value:e.parameters,enumerable:t.debug},args:{value:e.args,enumerable:t.debug},types:{value:e.statement&&e.statement.types,enumerable:t.debug}}),e.reject(i)}function eM(){return eT||(eO.reserved||n(eO),eO.reserved||eS||eA||0!==x.length?eT=new Promise(e=>e$=e):(eR(),new Promise(e=>j&&"closed"!==j.readyState?j.once("close",e):e())))}function eR(){eb=!0,(eP||eA||eS||x.length)&&eU(h.connection("CONNECTION_DESTROYED",t)),clearImmediate(ey),j&&(j.removeListener("data",ez),j.removeListener("connect",eV),"open"===j.readyState&&j.end(et().X().end())),e$&&(e$(),eT=e$=null)}async function eJ(e){if(k=Buffer.alloc(0),X=0,ew=null,clearImmediate(ey),j.removeListener("data",ez),j.removeListener("connect",eV),O.cancel(),q.cancel(),_.cancel(),j.removeAllListeners(),j=null,eS)return eF();!e&&(eA||x.length)&&eU(h.connection("CONNECTION_CLOSED",t,j)),U=H.performance.now(),e&&t.shared.retries++,ei=("function"==typeof N?N(t.shared.retries):N)*1e3,s(eO,h.connection("CONNECTION_CLOSED",t,j))}function eG(){I.statement||(I.statement=eA.statement),I.columns=eA.statement.columns}async function eW(e,t=e.readUInt32BE(5)){(3===t?eH:5===t?eY:10===t?eZ:11===t?e0:12===t?function(e){e.toString("utf8",9).split(et.N,1)[0].slice(2)!==ef&&(eX(h.generic("SASL_SIGNATURE_MISMATCH","The server did not return the correct signature")),j.destroy())}:0!==t?function(e,t){console.error("Postgres.js : Unknown Auth:",t)}:ec)(e,t)}async function eH(){let e=await e1();eB(et().p().str(e).z(1).end())}async function eY(e){let t="md5"+await ep(Buffer.concat([Buffer.from(await ep(await e1()+c)),e.subarray(9)]));eB(et().p().str(t).z(1).end())}async function eZ(){ex=(await G.randomBytes(18)).toString("base64"),et().p().str("SCRAM-SHA-256"+et.N);let e=et.i;eB(et.inc(4).str("n,,n=*,r="+ex).i32(et.i-e-4,e).end())}async function e0(e){var t;let i=e.toString("utf8",9).split(",").reduce((e,t)=>(e[t[0]]=t.slice(2),e),{}),r=await G.pbkdf2Sync(await e1(),Buffer.from(i.s,"base64"),parseInt(i.i),32,"sha256"),n=await em(r,"Client Key"),s="n=*,r="+ex+",r="+i.r+",s="+i.s+",i="+i.i+",c=biws,r="+i.r;ef=(await em(await em(r,"Server Key"),s)).toString("base64");let l="c=biws,r="+i.r+",p="+(function(e,t){let i=Math.max(e.length,t.length),r=Buffer.allocUnsafe(i);for(let n=0;n<i;n++)r[n]=e[n]^t[n];return r})(n,Buffer.from(await em(await (t=n,G.createHash("sha256").update(t).digest()),s))).toString("base64");eB(et().p().str(l).end())}function e1(){return Promise.resolve("function"==typeof t.pass?t.pass():t.pass)}async function e3(){D=!1,(await new u([`
      select b.oid, b.typarray
      from pg_catalog.pg_type a
      left join pg_catalog.pg_type b on b.oid = a.typelem
      where a.typcategory = 'A'
      group by b.oid, b.typarray
      order by b.oid
    `],[],ej)).forEach(({oid:e,typarray:i})=>(function(e,i){if(t.parsers[i]&&t.serializers[i])return;let r=t.parsers[e];t.shared.typeArrayMap[e]=i,t.parsers[i]=e=>(L.i=L.last=0,function e(t,i,r,n){let s=[],l=1020===n?";":",";for(;t.i<i.length;t.i++){if(t.char=i[t.i],t.quoted)"\\"===t.char?t.str+=i[++t.i]:'"'===t.char?(s.push(r?r(t.str):t.str),t.str="",t.quoted='"'===i[t.i+1],t.last=t.i+2):t.str+=t.char;else if('"'===t.char)t.quoted=!0;else if("{"===t.char)t.last=++t.i,s.push(e(t,i,r,n));else if("}"===t.char){t.quoted=!1,t.last<t.i&&s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1;break}else t.char===l&&"}"!==t.p&&'"'!==t.p&&(s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1);t.p=t.char}return t.last<t.i&&s.push(r?r(i.slice(t.last,t.i+1)):i.slice(t.last,t.i+1)),s}(L,e,r,i)),t.parsers[i].array=!0,t.serializers[i]=r=>B(r,t.serializers[e],t,i)})(e,i))}async function e2(){try{let e=await Promise.resolve(eA.cursorFn(I));er=0,e===o?eB(function(e=""){return Buffer.concat([et().C().str("P").str(e+et.N).end(),et().S().end()])}(eA.portal)):(I=new Y,eB(e5("",eA.cursorRows)))}catch(e){eB(es),eA.reject(e)}}function e4(e,t,i,r=""){return et().P().str(r+et.N).str(e+et.N).i16(t.length),t.forEach((e,t)=>et.i32(i[t]||0)),et.end()}function e5(e="",t=0){return Buffer.concat([et().E().str(e+et.N).i32(t).end(),el])}},en=1,es=et().S().end(),el=et().H().end(),ea=et().i32(8).i32(0x4d2162f).end(8),eo=Buffer.concat([et().E().str(et.N).i32(0).end(),es]),eu=et().D().str("S").str(et.N).end(),ec=()=>{},eh=new Set(["FetchPreparedStatement","RevalidateCachedQuery","transformAssignedExpr"]),ef={83:"severity_local",86:"severity",67:"code",77:"message",68:"detail",72:"hint",80:"position",112:"internal_position",113:"internal_query",87:"where",115:"schema_name",116:"table_name",99:"column_name",100:"data type_name",110:"constraint_name",70:"file",76:"line",82:"routine"};function ed(e){let t={},i=5;for(let r=5;r<e.length-1;r++)0===e[r]&&(t[ef[e[i]]]=e.toString("utf8",i+1,r),i=r+1);return t}function ep(e){return G.createHash("md5").update(e).digest("hex")}function em(e,t){return G.createHmac("sha256",e).update(t).digest()}function eg(e,t){let i;if(!(t="function"==typeof t?t():t))return{cancel:ec,start:ec};return{cancel(){i&&(clearTimeout(i),i=null)},start(){i&&clearTimeout(i),i=setTimeout(r,1e3*t,arguments)}};function r(t){e.apply(null,t),i=null}}let ey=()=>{};function eb(e,t,i,r){let n,s,l,a=r.raw?Array(t.length):{};for(let o=0;o<t.length;o++)n=e[i++],s=t[o],l=110===n?null:117===n?void 0:void 0===s.parser?e.toString("utf8",i+4,i+=4+e.readUInt32BE(i)):!0===s.parser.array?s.parser(e.toString("utf8",i+5,i+=4+e.readUInt32BE(i))):s.parser(e.toString("utf8",i+4,i+=4+e.readUInt32BE(i))),r.raw?a[o]=!0===r.raw?l:r.value.from?r.value.from(l,s):l:a[s.name]=r.value.from?r.value.from(l,s):l;return{i:i,row:r.row.from?r.row.from(a):a}}function ew(e,t,i=393216){return new Promise(async(r,n)=>{await e.begin(async e=>{let n;t||([{oid:t}]=await e`select lo_creat(-1) as oid`);let[{fd:s}]=await e`select lo_open(${t}, ${i}) as fd`,l={writable:o,readable:a,close:()=>e`select lo_close(${s})`.then(n),tell:()=>e`select lo_tell64(${s})`,read:t=>e`select loread(${s}, ${t}) as data`,write:t=>e`select lowrite(${s}, ${t})`,truncate:t=>e`select lo_truncate64(${s}, ${t})`,seek:(t,i=0)=>e`select lo_lseek64(${s}, ${t}, ${i})`,size:()=>e`
          select
            lo_lseek64(${s}, location, 0) as position,
            seek.size
          from (
            select
              lo_lseek64($1, 0, 2) as size,
              tell.location
            from (select lo_tell64($1) as location) tell
          ) seek
        `};return r(l),new Promise(async e=>n=e);async function a({highWaterMark:e=16384,start:t=0,end:i=1/0}={}){let r=i-t;return t&&await l.seek(t),new W.Readable({highWaterMark:e,async read(e){let t=e>r?e-r:e;r-=e;let[{data:i}]=await l.read(t);this.push(i),i.length<e&&this.push(null)}})}async function o({highWaterMark:e=16384,start:t=0}={}){return t&&await l.seek(t),new W.Writable({highWaterMark:e,write(e,t,i){l.write(e).then(()=>i(),i)}})}}).catch(n)})}Object.assign(eS,{PostgresError:c,toPascal:D,pascal:K,toCamel:k,camel:X,toKebab:z,kebab:M,fromPascal:F,fromCamel:Q,fromKebab:V,BigInt:{to:20,from:[20],parse:e=>BigInt(e),serialize:e=>e.toString()}});let ev=eS;function eS(e,t){let i=function(e,t){var i;if(e&&e.shared)return e;let n=process.env,s=(e&&"string"!=typeof e?e:t)||{},{url:l,multihost:a}=function(e){if(!e||"string"!=typeof e)return{url:{searchParams:new Map}};let t=e;t=decodeURIComponent((t=t.slice(t.indexOf("://")+3).split(/[?/]/)[0]).slice(t.indexOf("@")+1));let i=new URL(e.replace(t,t.split(",")[0]));return{url:{username:decodeURIComponent(i.username),password:decodeURIComponent(i.password),host:i.host,hostname:i.hostname,port:i.port,pathname:i.pathname,searchParams:i.searchParams},multihost:t.indexOf(",")>-1&&t}}(e),o=[...l.searchParams].reduce((e,[t,i])=>(e[t]=i,e),{}),u=s.hostname||s.host||a||l.hostname||n.PGHOST||"localhost",c=s.port||l.port||n.PGPORT||5432,h=s.user||s.username||l.username||n.PGUSERNAME||n.PGUSER||function(){try{return r.userInfo().username}catch(e){return process.env.USERNAME||process.env.USER||process.env.LOGNAME}}();s.no_prepare&&(s.prepare=!1),o.sslmode&&(o.ssl=o.sslmode,delete o.sslmode),"timeout"in s&&(console.log("The timeout option is deprecated, use idle_timeout instead"),s.idle_timeout=s.timeout),"system"===o.sslrootcert&&(o.ssl="verify-full");let f=["idle_timeout","connect_timeout","max_lifetime","max_pipeline","backoff","keep_alive"],d={max:10,ssl:!1,idle_timeout:null,connect_timeout:30,max_lifetime:eP,max_pipeline:100,backoff:eT,keep_alive:60,prepare:!0,debug:!1,fetch_types:!0,publications:"alltables",target_session_attrs:null};return{host:Array.isArray(u)?u:u.split(",").map(e=>e.split(":")[0]),port:Array.isArray(c)?c:u.split(",").map(e=>parseInt(e.split(":")[1]||c)),path:s.path||u.indexOf("/")>-1&&u+"/.s.PGSQL."+c,database:s.database||s.db||(l.pathname||"").slice(1)||n.PGDATABASE||h,user:h,pass:s.pass||s.password||l.password||n.PGPASSWORD||"",...Object.entries(d).reduce((e,[t,i])=>{let r=t in s?s[t]:t in o?"disable"!==o[t]&&"false"!==o[t]&&o[t]:n["PG"+t.toUpperCase()]||i;return e[t]="string"==typeof r&&f.includes(t)?+r:r,e},{}),connection:{application_name:n.PGAPPNAME||"postgres.js",...s.connection,...Object.entries(o).reduce((e,[t,i])=>(t in d||(e[t]=i),e),{})},types:s.types||{},target_session_attrs:function(e,t,i){let r=e.target_session_attrs||t.searchParams.get("target_session_attrs")||i.PGTARGETSESSIONATTRS;if(!r||["read-write","read-only","primary","standby","prefer-standby"].includes(r))return r;throw Error("target_session_attrs "+r+" is not supported")}(s,l,n),onnotice:s.onnotice,onnotify:s.onnotify,onclose:s.onclose,onparameter:s.onparameter,socket:s.socket,transform:{undefined:(i=s.transform||{undefined:void 0}).undefined,column:{from:"function"==typeof i.column?i.column:i.column&&i.column.from,to:i.column&&i.column.to},value:{from:"function"==typeof i.value?i.value:i.value&&i.value.from,to:i.value&&i.value.to},row:{from:"function"==typeof i.row?i.row:i.row&&i.row.from,to:i.row&&i.row.to}},parameters:{},shared:{retries:0,typeArrayMap:{}},...C(s.types)}}(e,t),s=i.no_subscribe||function(e,t){let i=new Map,r="postgresjs_"+Math.random().toString(36).slice(2),n={},s,l,a=!1,o=h.sql=e({...t,transform:{column:{},value:{},row:{}},max:1,fetch_types:!1,idle_timeout:null,max_lifetime:null,connection:{...t.connection,replication:"database"},onclose:async function(){a||(l=null,n.pid=n.secret=void 0,f(await d(o,r,t.publications)),i.forEach(e=>e.forEach(({onsubscribe:e})=>e())))},no_subscribe:!0}),u=o.end,c=o.close;return o.end=async()=>(a=!0,l&&await new Promise(e=>(l.once("close",e),l.end())),u()),o.close=async()=>(l&&await new Promise(e=>(l.once("close",e),l.end())),c()),h;async function h(e,a,u=ey,c=ey){e=function(e){let t=e.match(/^(\*|insert|update|delete)?:?([^.]+?\.?[^=]+)?=?(.+)?/i)||[];if(!t)throw Error("Malformed subscribe pattern: "+e);let[,i,r,n]=t;return(i||"*")+(r?":"+(-1===r.indexOf(".")?"public."+r:r):"")+(n?"="+n:"")}(e),s||(s=d(o,r,t.publications));let p={fn:a,onsubscribe:u},m=i.has(e)?i.get(e).add(p):i.set(e,new Set([p])).get(e),g=()=>{m.delete(p),0===m.size&&i.delete(e)};return s.then(e=>(f(e),u(),l&&l.on("error",c),{unsubscribe:g,state:n,sql:o}))}function f(e){l=e.stream,n.pid=e.state.pid,n.secret=e.state.secret}async function d(e,i,r){if(!r)throw Error("Missing publication names");let n=await e.unsafe(`CREATE_REPLICATION_SLOT ${i} TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`),[s]=n,l=await e.unsafe(`START_REPLICATION SLOT ${i} LOGICAL ${s.consistent_point} (proto_version '1', publication_names '${r}')`).writable(),a={lsn:Buffer.concat(s.consistent_point.split("/").map(e=>Buffer.from(("00000000"+e).slice(-8),"hex")))};return l.on("data",function(i){var r,n,s,u,c;119===i[0]?(r=i.subarray(25),n=a,s=e.options.parsers,u=o,c=t.transform,Object.entries({R:e=>{let t=1,i=n[e.readUInt32BE(t)]={schema:e.toString("utf8",t+=4,t=e.indexOf(0,t))||"pg_catalog",table:e.toString("utf8",t+1,t=e.indexOf(0,t+1)),columns:Array(e.readUInt16BE(t+=2)),keys:[]};t+=2;let r=0,l;for(;t<e.length;)(l=i.columns[r++]={key:e[t++],name:c.column.from?c.column.from(e.toString("utf8",t,t=e.indexOf(0,t))):e.toString("utf8",t,t=e.indexOf(0,t)),type:e.readUInt32BE(t+=1),parser:s[e.readUInt32BE(t)],atttypmod:e.readUInt32BE(t+=4)}).key&&i.keys.push(l),t+=4},Y:()=>{},O:()=>{},B:e=>{var t;t=e.readBigInt64BE(9),n.date=new Date(Date.UTC(2e3,0,1)+Number(t/BigInt(1e3))),n.lsn=e.subarray(1,9)},I:e=>{let t=1,i=n[e.readUInt32BE(t)],{row:r}=eb(e,i.columns,t+=7,c);u(r,{command:"insert",relation:i})},D:e=>{let t=1,i=n[e.readUInt32BE(t)],r=75===e[t+=4];u(r||79===e[t]?eb(e,i.columns,t+=3,c).row:null,{command:"delete",relation:i,key:r})},U:e=>{let t=1,i=n[e.readUInt32BE(t)],r=75===e[t+=4],s=r||79===e[t]?eb(e,i.columns,t+=3,c):null;s&&(t=s.i);let{row:l}=eb(e,i.columns,t+3,c);u(l,{command:"update",relation:i,key:r,old:s&&s.row})},T:()=>{},C:()=>{}}).reduce((e,[t,i])=>(e[t.charCodeAt(0)]=i,e),{})[r[0]](r)):107===i[0]&&i[17]&&(a.lsn=i.subarray(1,9),function(){let e=Buffer.alloc(34);e[0]=114,e.fill(a.lsn,1),e.writeBigInt64BE(BigInt(Date.now()-Date.UTC(2e3,0,1))*BigInt(1e3),25),l.write(e)}())}),l.on("error",function(e){console.error("Unexpected error during logical streaming - reconnecting",e)}),l.on("close",e.close),{stream:l,state:n.state};function o(e,t){let i=t.relation.schema+"."+t.relation.table;p("*",e,t),p("*:"+i,e,t),t.relation.keys.length&&p("*:"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t),p(t.command,e,t),p(t.command+":"+i,e,t),t.relation.keys.length&&p(t.command+":"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t)}}function p(e,t,r){i.has(e)&&i.get(e).forEach(({fn:i})=>i(t,r,e))}}(eS,{...i}),l=!1,a=Z(),f=Z(),g=Z(),y=Z(),b=Z(),w=Z(),v=Z(),S=Z(),T={connecting:f,reserved:g,closed:y,ended:b,open:w,busy:v,full:S},P=[...Array(i.max)].map(()=>er(i,T,{onopen:F,onend:Q,onclose:V})),N=$(function(e){return l?e.reject(h.connection("CONNECTION_ENDED",i,i)):w.length?I(w.shift(),e):y.length?z(y.shift(),e):void(v.length?I(v.shift(),e):a.push(e))});return Object.assign(N,{get parameters(){return i.parameters},largeObject:ew.bind(null,N),subscribe:s,CLOSE:o,END:o,PostgresError:c,options:i,reserve:O,listen:x,begin:q,close:k,end:L}),N;function $(e){return e.debug=i.debug,Object.entries(i.types).reduce((e,[t,i])=>(e[t]=e=>new p(e,i.to),e),t),Object.assign(r,{types:t,typed:t,unsafe:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([t],i,e,B,{prepare:!1,...r,simple:"simple"in r?r.simple:0===i.length})},notify:A,array:function e(t,r){return Array.isArray(t)?new p(t,r||(t.length?j(t)||25:0),i.shared.typeArrayMap):e(Array.from(arguments))},json:E,file:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([],i,i=>{n.readFile(t,"utf8",(t,r)=>{if(t)return i.reject(t);i.strings=[r],e(i)})},B,{...r,simple:"simple"in r?r.simple:0===i.length})}}),r;function t(e,t){return new p(e,t)}function r(t,...n){return t&&Array.isArray(t.raw)?new u(t,n,e,B):"string"!=typeof t||n.length?new m(t,n):new d(i.transform.column.to?i.transform.column.to(t):t)}}async function x(e,t,r){let n={fn:t,onlisten:r},s=x.sql||(x.sql=eS({...i,max:1,idle_timeout:null,max_lifetime:null,fetch_types:!1,onclose(){Object.entries(x.channels).forEach(([e,{listeners:t}])=>{delete x.channels[e],Promise.all(t.map(t=>x(e,t.fn,t.onlisten).catch(()=>{})))})},onnotify(e,t){e in x.channels&&x.channels[e].listeners.forEach(e=>e.fn(t))}})),l=x.channels||(x.channels={});if(e in l){l[e].listeners.push(n);let t=await l[e].result;return n.onlisten&&n.onlisten(),{state:t.state,unlisten:o}}l[e]={result:s`listen ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`,listeners:[n]};let a=await l[e].result;return n.onlisten&&n.onlisten(),{state:a.state,unlisten:o};async function o(){if(e in l!=!1&&(l[e].listeners=l[e].listeners.filter(e=>e!==n),!l[e].listeners.length))return delete l[e],s`unlisten ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`}}async function A(e,t){return await N`select pg_notify(${e}, ${""+t})`}async function O(){let e=Z(),t=w.length?w.shift():await new Promise((e,t)=>{let i={reserve:e,reject:t};a.push(i),y.length&&z(y.shift(),i)});_(t,g),t.reserved=()=>e.length?t.execute(e.shift()):_(t,g),t.reserved.release=!0;let i=$(function(i){t.queue===S?e.push(i):t.execute(i)||_(t,S)});return i.release=()=>{t.reserved=null,F(t)},i}async function q(e,t){t||(t=e,e="");let i=Z(),r=0,n,s=null;try{return await N.unsafe("begin "+e.replace(/[^a-z ]/ig,""),[],{onexecute:function(e){n=e,_(e,g),e.reserved=()=>i.length?e.execute(i.shift()):_(e,g)}}).execute(),await Promise.race([l(n,t),new Promise((e,t)=>n.onclose=t)])}catch(e){throw e}async function l(e,t,n){let a,o,u=$(function(t){t.catch(e=>a||(a=e)),e.queue===S?i.push(t):e.execute(t)||_(e,S)});u.savepoint=function t(i,n){return i&&Array.isArray(i.raw)?t(e=>e.apply(e,arguments)):(1==arguments.length&&(n=i,i=null),l(e,n,"s"+r+++(i?"_"+i:"")))},u.prepare=e=>s=e.replace(/[^a-z0-9$-_. ]/gi),n&&await u`savepoint ${u(n)}`;try{if(o=await new Promise((e,i)=>{let r=t(u);Promise.resolve(Array.isArray(r)?Promise.all(r):r).then(e,i)}),a)throw a}catch(e){throw await (n?u`rollback to ${u(n)}`:u`rollback`),e instanceof c&&"25P02"===e.code&&a||e}return n||(s?await u`prepare transaction '${u.unsafe(s)}'`:await u`commit`),o}}function _(e,t){return e.queue.remove(e),t.push(e),e.queue=t,t===w?e.idleTimer.start():e.idleTimer.cancel(),e}function E(e){return new p(e,3802)}function I(e,t){return e.execute(t)?_(e,v):_(e,S)}function B(e){return new Promise((t,r)=>{e.state?e.active?er(i).cancel(e.state,t,r):e.cancelled={resolve:t,reject:r}:(a.remove(e),e.cancelled=!0,e.reject(h.generic("57014","canceling statement due to user request")),t())})}async function L({timeout:e=null}={}){let t;return l||(await 1,l=Promise.race([new Promise(i=>null!==e&&(t=setTimeout(D,1e3*e,i))),Promise.all(P.map(e=>e.end()).concat(x.sql?x.sql.end({timeout:0}):[],s.sql?s.sql.end({timeout:0}):[]))]).then(()=>clearTimeout(t)))}async function k(){await Promise.all(P.map(e=>e.end()))}async function D(e){for(await Promise.all(P.map(e=>e.terminate()));a.length;)a.shift().reject(h.connection("CONNECTION_DESTROYED",i));e()}function z(e,t){return _(e,f),e.connect(t),e}function Q(e){_(e,b)}function F(e){if(0===a.length)return _(e,w);let t=Math.ceil(a.length/(f.length+1)),i=!0;for(;i&&a.length&&t-- >0;){let t=a.shift();if(t.reserve)return t.reserve(e);i=e.execute(t)}i?_(e,v):_(e,S)}function V(e,t){_(e,y),e.reserved=null,e.onclose&&(e.onclose(t),e.onclose=null),i.onclose&&i.onclose(e.id),a.length&&z(e,a.shift())}}function eT(e){return(.5+Math.random()/2)*Math.min(3**e/100,20)}function eP(){return 60*(30+30*Math.random())}},51024:(e,t,i)=>{i.d(t,{Kl:()=>g,pe:()=>m});var r=i(5730);class n{static [r.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}var s=i(10007),l=i(86214);class a{static [r.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:r}=e();return{name:t,columns:i,foreignTable:r[0].table,foreignColumns:r}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new o(e,this)}}class o{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [r.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),r=t.map(e=>e.name),n=i.map(e=>e.name),s=[this.table[l.E],...r,i[0].table[l.E],...n];return e??`${s.join("_")}_fk`}}var u=i(69167);function c(e,t){return`${e[l.E]}_${t.join("_")}_unique`}class h{constructor(e,t){this.name=t,this.columns=e}static [r.i]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new d(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class f{static [r.i]=null;name;constructor(e){this.name=e}on(...e){return new h(e,this.name)}}class d{constructor(e,t,i,r){this.table=e,this.columns=t,this.name=r??c(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [r.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function p(e,t,i){for(let r=t;r<e.length;r++){let n=e[r];if("\\"===n){r++;continue}if('"'===n)return[e.slice(t,r).replace(/\\/g,""),r+1];if(!i&&(","===n||"}"===n))return[e.slice(t,r).replace(/\\/g,""),r]}return[e.slice(t).replace(/\\/g,""),e.length]}class m extends n{foreignKeyConfigs=[];static [r.i]="PgColumnBuilder";array(e){return new w(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:r})=>(0,u.i)((i,r)=>{let n=new a(()=>({columns:[e],foreignColumns:[i()]}));return r.onUpdate&&n.onUpdate(r.onUpdate),r.onDelete&&n.onDelete(r.onDelete),n.build(t)},i,r))}buildExtraConfigColumn(e){return new y(e,this.config)}}class g extends s.V{constructor(e,t){t.uniqueName||(t.uniqueName=c(e,[t.name])),super(e,t),this.table=e}static [r.i]="PgColumn"}class y extends g{static [r.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class b{static [r.i]=null;constructor(e,t,i,r){this.name=e,this.keyAsName=t,this.type=i,this.indexConfig=r}name;keyAsName;type;indexConfig}class w extends m{static [r.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new v(e,this.config,t)}}class v extends g{constructor(e,t,i,r){super(e,t),this.baseColumn=i,this.range=r,this.size=t.size}size;static [r.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let r=[],n=i,s=!1;for(;n<t.length;){let l=t[n];if(","===l){(s||n===i)&&r.push(""),s=!0,n++;continue}if(s=!1,"\\"===l){n+=2;continue}if('"'===l){let[e,i]=p(t,n+1,!0);r.push(e),n=i;continue}if("}"===l)return[r,n+1];if("{"===l){let[i,s]=e(t,n+1);r.push(i),n=s;continue}let[a,o]=p(t,n,!1);r.push(a),n=o}return[r,n]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,r.is)(this.baseColumn,v)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},54693:(e,t,i)=>{i.d(t,{zM:()=>a});var r=i(5730),n=i(51024);class s extends n.pe{static [r.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new l(e,this.config)}}class l extends n.Kl{static [r.i]="PgBoolean";getSQLType(){return"boolean"}}function a(e){return new s(e??"")}},63431:(e,t,i)=>{i.d(t,{nd:()=>o});var r=i(5730),n=i(51024),s=i(68449);class l extends s.p{static [r.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function o(e){return new l(e??"")}},65734:(e,t,i)=>{i.d(t,{u:()=>l});var r=i(5730),n=i(96657),s=i(51024);class l extends s.pe{static [r.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,n.ll)`now()`)}}},67083:(e,t,i)=>{i.d(t,{J:()=>s,n:()=>n});var r=i(5730);class n{static [r.i]="Subquery";constructor(e,t,i,r=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:r}}}class s extends n{static [r.i]="WithSubquery"}},68449:(e,t,i)=>{i.d(t,{p:()=>s});var r=i(5730),n=i(51024);class s extends n.pe{static [r.i]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}},69167:(e,t,i)=>{i.d(t,{i:()=>r});function r(e,...t){return e(...t)}},72170:(e,t,i)=>{i.d(t,{Z5:()=>a,sH:()=>o});var r=i(5730),n=i(79608),s=i(51024);class l extends s.pe{static [r.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new a(e,this.config)}}class a extends s.Kl{static [r.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function o(e,t){let{name:i,config:r}=(0,n.Ll)(e,t);return new l(i,r?.precision,r?.scale)}},79608:(e,t,i)=>{i.d(t,{DV:()=>c,He:()=>function e(t,i){return Object.entries(t).reduce((t,[l,o])=>{if("string"!=typeof l)return t;let u=i?[...i,l]:[l];return(0,n.is)(o,r.V)||(0,n.is)(o,s.Xs)||(0,n.is)(o,s.Xs.Aliased)?t.push({path:u,field:o}):(0,n.is)(o,a.XI)?t.push(...e(o[a.XI.Symbol.Columns],u)):t.push(...e(o,u)),t},[])},Ll:()=>m,Lq:()=>g,XJ:()=>f,YD:()=>d,a6:()=>u,q:()=>h,zN:()=>p});var r=i(10007),n=i(5730),s=i(96657),l=i(67083),a=i(24717),o=i(12772);function u(e,t,i){let l={},o=e.reduce((e,{path:o,field:u},c)=>{let h;h=(0,n.is)(u,r.V)?u:(0,n.is)(u,s.Xs)?u.decoder:u.sql.decoder;let f=e;for(let[e,s]of o.entries())if(e<o.length-1)s in f||(f[s]={}),f=f[s];else{let e=t[c],d=f[s]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,n.is)(u,r.V)&&2===o.length){let e=o[0];e in l?"string"==typeof l[e]&&l[e]!==(0,a.Io)(u.table)&&(l[e]=!1):l[e]=null===d&&(0,a.Io)(u.table)}}return e},{});if(i&&Object.keys(l).length>0)for(let[e,t]of Object.entries(l))"string"!=typeof t||i[t]||(o[e]=null);return o}function c(e,t){let i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;for(let[e,t]of i.entries())if(t!==r[e])return!1;return!0}function h(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,n.is)(i,s.Xs)||(0,n.is)(i,r.V)?[t,i]:[t,new s.Iw(i,e[a.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function f(e,t){for(let i of t)for(let t of Object.getOwnPropertyNames(i.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(i.prototype,t)||Object.create(null))}function d(e){return e[a.XI.Symbol.Columns]}function p(e){return(0,n.is)(e,l.n)?e._.alias:(0,n.is)(e,s.Ss)?e[o.n].name:(0,n.is)(e,s.Xs)?void 0:e[a.XI.Symbol.IsAlias]?e[a.XI.Symbol.Name]:e[a.XI.Symbol.BaseName]}function m(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}function g(e){if("object"!=typeof e||null===e||"Object"!==e.constructor.name)return!1;if("logger"in e){let t=typeof e.logger;return"boolean"===t||"object"===t&&"function"==typeof e.logger.logQuery||"undefined"===t}if("schema"in e){let t=typeof e.logger;return"object"===t||"undefined"===t}if("casing"in e){let t=typeof e.logger;return"string"===t||"undefined"===t}if("mode"in e)return"default"===e.mode&&"planetscale"===e.mode&&void 0===e.mode;if("connection"in e){let t=typeof e.connection;return"string"===t||"object"===t||"undefined"===t}if("client"in e){let t=typeof e.client;return"object"===t||"function"===t||"undefined"===t}return 0===Object.keys(e).length}},86214:(e,t,i)=>{i.d(t,{E:()=>r});let r=Symbol.for("drizzle:Name")},92768:(e,t,i)=>{i.d(t,{mu:()=>eX,cJ:()=>eK});var r=i(5730),n=i(24717),s=i(79608),l=i(51024),a=i(68449);class o extends a.p{static [r.i]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new u(e,this.config)}}class u extends l.Kl{static [r.i]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class c extends a.p{static [r.i]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new h(e,this.config)}}class h extends l.Kl{static [r.i]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function f(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new o(i):new c(i)}class d extends l.pe{static [r.i]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new p(e,this.config)}}class p extends l.Kl{static [r.i]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class m extends l.pe{static [r.i]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new g(e,this.config)}}class g extends l.Kl{static [r.i]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function y(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new d(i):new m(i)}var b=i(54693);class w extends l.pe{static [r.i]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new v(e,this.config)}}class v extends l.Kl{static [r.i]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function S(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new w(i,r)}class T extends l.pe{static [r.i]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new P(e,this.config)}}class P extends l.Kl{static [r.i]="PgCidr";getSQLType(){return"cidr"}}function N(e){return new T(e??"")}class $ extends l.pe{static [r.i]="PgCustomColumnBuilder";constructor(e,t,i){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new x(e,this.config)}}class x extends l.Kl{static [r.i]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function A(e){return(t,i)=>{let{name:r,config:n}=(0,s.Ll)(t,i);return new $(r,n,e)}}var C=i(37193);class O extends l.pe{static [r.i]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new q(e,this.config)}}class q extends l.Kl{static [r.i]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function _(e){return new O(e??"")}class j extends l.pe{static [r.i]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new E(e,this.config)}}class E extends l.Kl{static [r.i]="PgInet";getSQLType(){return"inet"}}function I(e){return new j(e??"")}var B=i(63431);class L extends l.pe{static [r.i]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new k(e,this.config)}}class k extends l.Kl{static [r.i]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function D(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new L(i,r)}var z=i(34359),Q=i(34509);class F extends l.pe{static [r.i]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new V(e,this.config)}}class V extends l.Kl{static [r.i]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i),Number.parseFloat(r)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class U extends l.pe{static [r.i]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new X(e,this.config)}}class X extends l.Kl{static [r.i]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(i),c:Number.parseFloat(r)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function K(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new U(i):new F(i)}class M extends l.pe{static [r.i]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new R(e,this.config)}}class R extends l.Kl{static [r.i]="PgMacaddr";getSQLType(){return"macaddr"}}function J(e){return new M(e??"")}class G extends l.pe{static [r.i]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new W(e,this.config)}}class W extends l.Kl{static [r.i]="PgMacaddr8";getSQLType(){return"macaddr8"}}function H(e){return new G(e??"")}var Y=i(72170);class Z extends l.pe{static [r.i]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new ee(e,this.config)}}class ee extends l.Kl{static [r.i]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class et extends l.pe{static [r.i]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new ei(e,this.config)}}class ei extends l.Kl{static [r.i]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(i)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function er(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new et(i):new Z(i)}function en(e,t){let i=new DataView(new ArrayBuffer(8));for(let r=0;r<8;r++)i.setUint8(r,e[t+r]);return i.getFloat64(0,!0)}function es(e){let t=function(e){let t=[];for(let i=0;i<e.length;i+=2)t.push(Number.parseInt(e.slice(i,i+2),16));return new Uint8Array(t)}(e),i=0,r=t[0];i+=1;let n=new DataView(t.buffer),s=n.getUint32(i,1===r);if(i+=4,0x20000000&s&&(n.getUint32(i,1===r),i+=4),(65535&s)==1){let e=en(t,i),r=en(t,i+=8);return i+=8,[e,r]}throw Error("Unsupported geometry type")}class el extends l.pe{static [r.i]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new ea(e,this.config)}}class ea extends l.Kl{static [r.i]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return es(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class eo extends l.pe{static [r.i]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new eu(e,this.config)}}class eu extends l.Kl{static [r.i]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=es(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function ec(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new eo(i):new el(i)}class eh extends l.pe{static [r.i]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new ef(e,this.config)}}class ef extends l.Kl{static [r.i]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function ed(e){return new eh(e??"")}class ep extends l.pe{static [r.i]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new em(e,this.config)}}class em extends l.Kl{static [r.i]="PgSerial";getSQLType(){return"serial"}}function eg(e){return new ep(e??"")}class ey extends a.p{static [r.i]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new eb(e,this.config)}}class eb extends l.Kl{static [r.i]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function ew(e){return new ey(e??"")}class ev extends l.pe{static [r.i]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new eS(e,this.config)}}class eS extends l.Kl{static [r.i]="PgSmallSerial";getSQLType(){return"smallserial"}}function eT(e){return new ev(e??"")}var eP=i(29334),eN=i(9528),e$=i(9253),ex=i(9594);class eA extends l.pe{static [r.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new eC(e,this.config)}}class eC extends l.Kl{static [r.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eO(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new eA(i,r)}class eq extends l.pe{static [r.i]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new e_(e,this.config)}}class e_ extends l.Kl{static [r.i]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function ej(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eq(i,r)}class eE extends l.pe{static [r.i]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new eI(e,this.config)}}class eI extends l.Kl{static [r.i]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eB(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eE(i,r)}class eL extends l.pe{static [r.i]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new ek(e,this.config)}}class ek extends l.Kl{static [r.i]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function eD(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eL(i,r)}class ez extends l.pe{static [r.i]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new eQ(e,this.config)}}class eQ extends l.Kl{static [r.i]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eF(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new ez(i,r)}let eV=Symbol.for("drizzle:PgInlineForeignKeys"),eU=Symbol.for("drizzle:EnableRLS");class eX extends n.XI{static [r.i]="PgTable";static Symbol=Object.assign({},n.XI.Symbol,{InlineForeignKeys:eV,EnableRLS:eU});[eV]=[];[eU]=!1;[n.XI.Symbol.ExtraConfigBuilder]=void 0}let eK=(e,t,i)=>(function(e,t,i,r,s=e){let l=new eX(e,r,s),a="function"==typeof t?t({bigint:f,bigserial:y,boolean:b.zM,char:S,cidr:N,customType:A,date:C.p6,doublePrecision:_,inet:I,integer:B.nd,interval:D,json:z.Pq,jsonb:Q.Fx,line:K,macaddr:J,macaddr8:H,numeric:Y.sH,point:er,geometry:ec,real:ed,serial:eg,smallint:ew,smallserial:eT,text:eP.Qq,time:eN.kB,timestamp:e$.vE,uuid:ex.uR,varchar:eO,bit:ej,halfvec:eB,sparsevec:eD,vector:eF}):t,o=Object.fromEntries(Object.entries(a).map(([e,t])=>{t.setName(e);let i=t.build(l);return l[eV].push(...t.buildForeignKeys(i,l)),[e,i]})),u=Object.fromEntries(Object.entries(a).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(l)]))),c=Object.assign(l,o);return c[n.XI.Symbol.Columns]=o,c[n.XI.Symbol.ExtraConfigColumns]=u,i&&(c[eX.Symbol.ExtraConfigBuilder]=i),Object.assign(c,{enableRLS:()=>(c[eX.Symbol.EnableRLS]=!0,c)})})(e,t,i,void 0)},94634:(e,t,i)=>{i.d(t,{AU:()=>f,B3:()=>A,KJ:()=>T,KL:()=>b,Pe:()=>v,RK:()=>x,RO:()=>p,RV:()=>y,Tq:()=>P,Uo:()=>c,eq:()=>o,gt:()=>d,kZ:()=>w,lt:()=>m,mj:()=>$,ne:()=>u,o8:()=>N,or:()=>h,q1:()=>C,t2:()=>S,wJ:()=>g});var r=i(10007),n=i(5730),s=i(24717),l=i(96657);function a(e,t){return!(0,l.eG)(t)||(0,l.qt)(e)||(0,n.is)(e,l.Iw)||(0,n.is)(e,l.Or)||(0,n.is)(e,r.V)||(0,n.is)(e,s.XI)||(0,n.is)(e,l.Ss)?e:new l.Iw(e,t)}let o=(e,t)=>(0,l.ll)`${e} = ${a(t,e)}`,u=(e,t)=>(0,l.ll)`${e} <> ${a(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" and ")),new l.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" or ")),new l.DJ(")")])}function f(e){return(0,l.ll)`not ${e}`}let d=(e,t)=>(0,l.ll)`${e} > ${a(t,e)}`,p=(e,t)=>(0,l.ll)`${e} >= ${a(t,e)}`,m=(e,t)=>(0,l.ll)`${e} < ${a(t,e)}`,g=(e,t)=>(0,l.ll)`${e} <= ${a(t,e)}`;function y(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`false`:(0,l.ll)`${e} in ${t.map(t=>a(t,e))}`:(0,l.ll)`${e} in ${a(t,e)}`}function b(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`true`:(0,l.ll)`${e} not in ${t.map(t=>a(t,e))}`:(0,l.ll)`${e} not in ${a(t,e)}`}function w(e){return(0,l.ll)`${e} is null`}function v(e){return(0,l.ll)`${e} is not null`}function S(e){return(0,l.ll)`exists ${e}`}function T(e){return(0,l.ll)`not exists ${e}`}function P(e,t,i){return(0,l.ll)`${e} between ${a(t,e)} and ${a(i,e)}`}function N(e,t,i){return(0,l.ll)`${e} not between ${a(t,e)} and ${a(i,e)}`}function $(e,t){return(0,l.ll)`${e} like ${t}`}function x(e,t){return(0,l.ll)`${e} not like ${t}`}function A(e,t){return(0,l.ll)`${e} ilike ${t}`}function C(e,t){return(0,l.ll)`${e} not ilike ${t}`}},96657:(e,t,i)=>{i.d(t,{Iw:()=>S,Or:()=>P,Xs:()=>g,DJ:()=>m,Ss:()=>$,Ct:()=>N,eG:()=>b,qt:()=>p,ll:()=>T});var r=i(5730),n=i(51024);let s=Symbol.for("drizzle:isPgEnum");class l extends n.pe{static [r.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var o=i(67083),u=i(99511),c=i(12772),h=i(10007),f=i(24717);class d{static [r.i]=null}function p(e){return null!=e&&"function"==typeof e.getSQL}class m{static [r.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new g([this])}}class g{constructor(e){this.queryChunks=e}static [r.i]="SQL";decoder=w;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return u.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:n,escapeName:l,escapeParam:a,prepareTyping:u,inlineParams:d,paramStartIndex:b}=i;var w=e.map(e=>{if((0,r.is)(e,m))return{sql:e.value.join(""),params:[]};if((0,r.is)(e,y))return{sql:l(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new m("(")];for(let[i,r]of e.entries())t.push(r),i<e.length-1&&t.push(new m(", "));return t.push(new m(")")),this.buildQueryFromSourceParams(t,i)}if((0,r.is)(e,g))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:d||e.shouldInlineParams});if((0,r.is)(e,f.XI)){let t=e[f.XI.Symbol.Schema],i=e[f.XI.Symbol.Name];return{sql:void 0===t?l(i):l(t)+"."+l(i),params:[]}}if((0,r.is)(e,h.V)){let i=n.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:l(i),params:[]};let r=e.table[f.XI.Symbol.Schema];return{sql:e.table[f.HE]||void 0===r?l(e.table[f.XI.Symbol.Name])+"."+l(i):l(r)+"."+l(e.table[f.XI.Symbol.Name])+"."+l(i),params:[]}}if((0,r.is)(e,$)){let t=e[c.n].schema,i=e[c.n].name;return{sql:void 0===t?l(i):l(t)+"."+l(i),params:[]}}if((0,r.is)(e,S)){if((0,r.is)(e.value,P))return{sql:a(b.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if((0,r.is)(t,g))return this.buildQueryFromSourceParams([t],i);if(d)return{sql:this.mapInlineParam(t,i),params:[]};let n=["none"];return u&&(n=[u(e.encoder)]),{sql:a(b.value++,t),params:[t],typings:n}}return(0,r.is)(e,P)?{sql:a(b.value++,e),params:[e],typings:["none"]}:(0,r.is)(e,g.Aliased)&&void 0!==e.fieldAlias?{sql:l(e.fieldAlias),params:[]}:(0,r.is)(e,o.n)?e._.isWith?{sql:l(e._.alias),params:[]}:this.buildQueryFromSourceParams([new m("("),e._.sql,new m(") "),new y(e._.alias)],i):e&&"function"==typeof e&&s in e&&!0===e[s]?e.schema?{sql:l(e.schema)+"."+l(e.enumName),params:[]}:{sql:l(e.enumName),params:[]}:p(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new m("("),e.getSQL(),new m(")")],i):d?{sql:this.mapInlineParam(e,i),params:[]}:{sql:a(b.value++,e),params:[e],typings:["none"]}});let v={sql:"",params:[]};for(let e of w)v.sql+=e.sql,v.params.push(...e.params),e.typings?.length&&(v.typings||(v.typings=[]),v.typings.push(...e.typings));return v}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new g.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class y{constructor(e){this.value=e}static [r.i]="Name";brand;getSQL(){return new g([this])}}function b(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let w={mapFromDriverValue:e=>e},v={mapToDriverValue:e=>e};({...w,...v});class S{constructor(e,t=v){this.value=e,this.encoder=t}static [r.i]="Param";brand;getSQL(){return new g([this])}}function T(e,...t){let i=[];for(let[r,n]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new m(e[0])),t.entries()))i.push(n,new m(e[r+1]));return new g(i)}(e=>{e.empty=function(){return new g([])},e.fromList=function(e){return new g(e)},e.raw=function(e){return new g([new m(e)])},e.join=function(e,t){let i=[];for(let[r,n]of e.entries())r>0&&void 0!==t&&i.push(t),i.push(n);return new g(i)},e.identifier=function(e){return new y(e)},e.placeholder=function(e){return new P(e)},e.param=function(e,t){return new S(e,t)}})(T||(T={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [r.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(g||(g={}));class P{constructor(e){this.name=e}static [r.i]="Placeholder";getSQL(){return new g([this])}}function N(e,t){return e.map(e=>{if((0,r.is)(e,P)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if((0,r.is)(e,S)&&(0,r.is)(e.value,P)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}class ${static [r.i]="View";[c.n];constructor({name:e,schema:t,selectedFields:i,query:r}){this[c.n]={name:e,originalName:e,schema:t,selectedFields:i,query:r,isExisting:!r,isAlias:!1}}getSQL(){return new g([this])}}h.V.prototype.getSQL=function(){return new g([this])},f.XI.prototype.getSQL=function(){return new g([this])},o.n.prototype.getSQL=function(){return new g([this])}},99511:(e,t,i)=>{let r,n;i.d(t,{k:()=>l});var s=i(69167);let l={startActiveSpan:(e,t)=>r?(n||(n=r.trace.getTracer("drizzle-orm","0.36.4")),(0,s.i)((i,r)=>r.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),r,n)):t()}}};