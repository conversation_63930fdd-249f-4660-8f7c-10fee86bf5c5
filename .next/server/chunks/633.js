exports.id=633,exports.ids=[633],exports.modules={339:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function a(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,a,n,s){if("function"!=typeof a)throw TypeError("The listener must be a function");var o=new i(a,n||e,s),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new a:delete e._events[t]}function o(){this._events=new a,this._eventsCount=0}Object.create&&(a.prototype=Object.create(null),new a().__proto__||(r=!1)),o.prototype.eventNames=function(){var e,a,i=[];if(0===this._eventsCount)return i;for(a in e=this._events)t.call(e,a)&&i.push(r?a.slice(1):a);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,a=this._events[t];if(!a)return[];if(a.fn)return[a.fn];for(var i=0,n=a.length,s=Array(n);i<n;i++)s[i]=a[i].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,a=this._events[t];return a?a.fn?1:a.length:0},o.prototype.emit=function(e,t,a,i,n,s){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,d=this._events[o],c=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),c){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,a),!0;case 4:return d.fn.call(d.context,t,a,i),!0;case 5:return d.fn.call(d.context,t,a,i,n),!0;case 6:return d.fn.call(d.context,t,a,i,n,s),!0}for(u=1,l=Array(c-1);u<c;u++)l[u-1]=arguments[u];d.fn.apply(d.context,l)}else{var h,p=d.length;for(u=0;u<p;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),c){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,a);break;case 4:d[u].fn.call(d[u].context,t,a,i);break;default:if(!l)for(h=1,l=Array(c-1);h<c;h++)l[h-1]=arguments[h];d[u].fn.apply(d[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,a,i){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return s(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||i&&!o.once||a&&o.context!==a||s(this,n);else{for(var l=0,u=[],d=o.length;l<d;l++)(o[l].fn!==t||i&&!o[l].once||a&&o[l].context!==a)&&u.push(o[l]);u.length?this._events[n]=1===u.length?u[0]:u:s(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new a,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},2843:e=>{"use strict";class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},3706:(e,t,r)=>{"use strict";let a=/\s+/g;class i{constructor(e,t){if(t=s(t),e instanceof i)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else return new i(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(a," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&_(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&g))+":"+e,r=n.get(t);if(r)return r;let a=this.options.loose,i=a?d[c.HYPHENRANGELOOSE]:d[c.HYPHENRANGE];l("hyphen replace",e=e.replace(i,C(this.options.includePrerelease))),l("comparator trim",e=e.replace(d[c.COMPARATORTRIM],h)),l("tilde trim",e=e.replace(d[c.TILDETRIM],p)),l("caret trim",e=e.replace(d[c.CARETTRIM],f));let s=e.split(" ").map(e=>v(e,this.options)).join(" ").split(/\s+/).map(e=>I(e,this.options));a&&(s=s.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(d[c.COMPARATORLOOSE])))),l("range list",s);let u=new Map;for(let e of s.map(e=>new o(e,this.options))){if(y(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let _=[...u.values()];return n.set(t,_),_}intersects(e,t){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(r=>b(r,t)&&e.set.some(e=>b(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(P(this.set[t],e,this.options))return!0;return!1}}e.exports=i;let n=new(r(2843)),s=r(98300),o=r(14239),l=r(38267),u=r(64487),{safeRe:d,t:c,comparatorTrimReplace:h,tildeTrimReplace:p,caretTrimReplace:f}=r(26515),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:g}=r(32397),y=e=>"<0.0.0-0"===e.value,_=e=>""===e.value,b=(e,t)=>{let r=!0,a=e.slice(),i=a.pop();for(;r&&a.length;)r=a.every(e=>i.intersects(e,t)),i=a.pop();return r},v=(e,t)=>(l("comp",e,t),l("caret",e=x(e,t)),l("tildes",e=E(e,t)),l("xrange",e=S(e,t)),l("stars",e=A(e,t)),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,E=(e,t)=>e.trim().split(/\s+/).map(e=>O(e,t)).join(" "),O=(e,t)=>{let r=t.loose?d[c.TILDELOOSE]:d[c.TILDE];return e.replace(r,(t,r,a,i,n)=>{let s;return l("tilde",e,t,r,a,i,n),w(r)?s="":w(a)?s=`>=${r}.0.0 <${+r+1}.0.0-0`:w(i)?s=`>=${r}.${a}.0 <${r}.${+a+1}.0-0`:n?(l("replaceTilde pr",n),s=`>=${r}.${a}.${i}-${n} <${r}.${+a+1}.0-0`):s=`>=${r}.${a}.${i} <${r}.${+a+1}.0-0`,l("tilde return",s),s})},x=(e,t)=>e.trim().split(/\s+/).map(e=>T(e,t)).join(" "),T=(e,t)=>{l("caret",e,t);let r=t.loose?d[c.CARETLOOSE]:d[c.CARET],a=t.includePrerelease?"-0":"";return e.replace(r,(t,r,i,n,s)=>{let o;return l("caret",e,t,r,i,n,s),w(r)?o="":w(i)?o=`>=${r}.0.0${a} <${+r+1}.0.0-0`:w(n)?o="0"===r?`>=${r}.${i}.0${a} <${r}.${+i+1}.0-0`:`>=${r}.${i}.0${a} <${+r+1}.0.0-0`:s?(l("replaceCaret pr",s),o="0"===r?"0"===i?`>=${r}.${i}.${n}-${s} <${r}.${i}.${+n+1}-0`:`>=${r}.${i}.${n}-${s} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${n}-${s} <${+r+1}.0.0-0`):(l("no pr"),o="0"===r?"0"===i?`>=${r}.${i}.${n}${a} <${r}.${i}.${+n+1}-0`:`>=${r}.${i}.${n}${a} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${n} <${+r+1}.0.0-0`),l("caret return",o),o})},S=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>k(e,t)).join(" ")),k=(e,t)=>{e=e.trim();let r=t.loose?d[c.XRANGELOOSE]:d[c.XRANGE];return e.replace(r,(r,a,i,n,s,o)=>{l("xRange",e,r,a,i,n,s,o);let u=w(i),d=u||w(n),c=d||w(s);return"="===a&&c&&(a=""),o=t.includePrerelease?"-0":"",u?r=">"===a||"<"===a?"<0.0.0-0":"*":a&&c?(d&&(n=0),s=0,">"===a?(a=">=",d?(i=+i+1,n=0):n=+n+1,s=0):"<="===a&&(a="<",d?i=+i+1:n=+n+1),"<"===a&&(o="-0"),r=`${a+i}.${n}.${s}${o}`):d?r=`>=${i}.0.0${o} <${+i+1}.0.0-0`:c&&(r=`>=${i}.${n}.0${o} <${i}.${+n+1}.0-0`),l("xRange return",r),r})},A=(e,t)=>(l("replaceStars",e,t),e.trim().replace(d[c.STAR],"")),I=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(d[t.includePrerelease?c.GTE0PRE:c.GTE0],"")),C=e=>(t,r,a,i,n,s,o,l,u,d,c,h)=>(r=w(a)?"":w(i)?`>=${a}.0.0${e?"-0":""}`:w(n)?`>=${a}.${i}.0${e?"-0":""}`:s?`>=${r}`:`>=${r}${e?"-0":""}`,l=w(u)?"":w(d)?`<${+u+1}.0.0-0`:w(c)?`<${u}.${+d+1}.0-0`:h?`<=${u}.${d}.${c}-${h}`:e?`<${u}.${d}.${+c+1}-0`:`<=${l}`,`${r} ${l}`.trim()),P=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){let a=e[r].semver;if(a.major===t.major&&a.minor===t.minor&&a.patch===t.patch)return!0}return!1}return!0}},7110:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,t);return r&&r.prerelease.length?r.prerelease:null}},8536:(e,t,r)=>{"use strict";let a=r(24800);e.exports=(e,t)=>e.sort((e,r)=>a(r,e,t))},11337:(e,t,r)=>{"use strict";let a=r(3706),i=r(14239),{ANY:n}=i,s=r(42679),o=r(33877),l=[new i(">=0.0.0-0")],u=[new i(">=0.0.0")],d=(e,t,r)=>{let a,i,d,p,f,m,g;if(e===t)return!0;if(1===e.length&&e[0].semver===n)if(1===t.length&&t[0].semver===n)return!0;else e=r.includePrerelease?l:u;if(1===t.length&&t[0].semver===n)if(r.includePrerelease)return!0;else t=u;let y=new Set;for(let t of e)">"===t.operator||">="===t.operator?a=c(a,t,r):"<"===t.operator||"<="===t.operator?i=h(i,t,r):y.add(t.semver);if(y.size>1)return null;if(a&&i&&((d=o(a.semver,i.semver,r))>0||0===d&&(">="!==a.operator||"<="!==i.operator)))return null;for(let e of y){if(a&&!s(e,String(a),r)||i&&!s(e,String(i),r))return null;for(let a of t)if(!s(e,String(a),r))return!1;return!0}let _=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver,b=!!a&&!r.includePrerelease&&!!a.semver.prerelease.length&&a.semver;for(let e of(_&&1===_.prerelease.length&&"<"===i.operator&&0===_.prerelease[0]&&(_=!1),t)){if(g=g||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,a){if(b&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===b.major&&e.semver.minor===b.minor&&e.semver.patch===b.patch&&(b=!1),">"===e.operator||">="===e.operator){if((p=c(a,e,r))===e&&p!==a)return!1}else if(">="===a.operator&&!s(a.semver,String(e),r))return!1}if(i){if(_&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===_.major&&e.semver.minor===_.minor&&e.semver.patch===_.patch&&(_=!1),"<"===e.operator||"<="===e.operator){if((f=h(i,e,r))===e&&f!==i)return!1}else if("<="===i.operator&&!s(i.semver,String(e),r))return!1}if(!e.operator&&(i||a)&&0!==d)return!1}return(!a||!m||!!i||0===d)&&(!i||!g||!!a||0===d)&&!b&&!_&&!0},c=(e,t,r)=>{if(!e)return t;let a=o(e.semver,t.semver,r);return a>0?e:a<0||">"===t.operator&&">="===e.operator?t:e},h=(e,t,r)=>{if(!e)return t;let a=o(e.semver,t.semver,r);return a<0?e:a>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new a(e,r),t=new a(t,r);let i=!1;e:for(let a of e.set){for(let e of t.set){let t=d(a,e,r);if(i=i||null!==t,t)continue e}if(i)return!1}return!0}},12441:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let a=r(71611);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=a.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},14239:(e,t,r)=>{"use strict";let a=Symbol("SemVer ANY");class i{static get ANY(){return a}constructor(e,t){if(t=n(t),e instanceof i)if(!!t.loose===e.loose)return e;else e=e.value;u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===a?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?s[o.COMPARATORLOOSE]:s[o.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new d(r[2],this.options.loose):this.semver=a}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===a||e===a)return!0;if("string"==typeof e)try{e=new d(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof i))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new c(e.value,t).test(this.value):""===e.operator?""===e.value||new c(this.value,t).test(e.semver):!((t=n(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=i;let n=r(98300),{safeRe:s,t:o}=r(26515),l=r(84450),u=r(38267),d=r(64487),c=r(3706)},17950:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t)=>a(e,t,!0)},18929:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],a=t[1];return(r+a)*3/4-a},t.toByteArray=function(e){var t,r,n=l(e),s=n[0],o=n[1],u=new i((s+o)*3/4-o),d=0,c=o>0?s-4:s;for(r=0;r<c;r+=4)t=a[e.charCodeAt(r)]<<18|a[e.charCodeAt(r+1)]<<12|a[e.charCodeAt(r+2)]<<6|a[e.charCodeAt(r+3)],u[d++]=t>>16&255,u[d++]=t>>8&255,u[d++]=255&t;return 2===o&&(t=a[e.charCodeAt(r)]<<2|a[e.charCodeAt(r+1)]>>4,u[d++]=255&t),1===o&&(t=a[e.charCodeAt(r)]<<10|a[e.charCodeAt(r+1)]<<4|a[e.charCodeAt(r+2)]>>2,u[d++]=t>>8&255,u[d++]=255&t),u},t.fromByteArray=function(e){for(var t,a=e.length,i=a%3,n=[],s=0,o=a-i;s<o;s+=16383)n.push(function(e,t,a){for(var i,n=[],s=t;s<a;s+=3)i=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]),n.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return n.join("")}(e,s,s+16383>o?o:s+16383));return 1===i?n.push(r[(t=e[a-1])>>2]+r[t<<4&63]+"=="):2===i&&n.push(r[(t=(e[a-2]<<8)+e[a-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),n.join("")};for(var r=[],a=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,o=n.length;s<o;++s)r[s]=n[s],a[n.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var a=r===t?0:4-r%4;return[r,a]}a[45]=62,a[95]=63},20938:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t)=>new a(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},22893:(e,t,r)=>{"use strict";let a=r(43528);e.exports=(e,t,r)=>a(e,t,"<",r)},23518:(e,t,r)=>{e.exports=r(55332)},23870:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(55511);let i={randomUUID:a.randomUUID},n=new Uint8Array(256),s=n.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let l=(e=e||{}).random??e.rng?.()??(s>n.length-16&&((0,a.randomFillSync)(n),s=0),n.slice(s,s+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=l[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(l)}},24303:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new i(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||1===s.compare(e))&&(s=new a(n=e,r))}),n}},24800:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r)=>{let i=new a(e,r),n=new a(t,r);return i.compare(n)||i.compareBuild(n)}},25706:e=>{"use strict";e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},26515:(e,t,r)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:a,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:n}=r(32397),s=r(38267),o=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],d=t.safeSrc=[],c=t.t={},h=0,p="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",n],[p,i]],m=e=>{for(let[t,r]of f)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},g=(e,t,r)=>{let a=m(t),i=h++;s(e,i,t),c[e]=i,u[i]=t,d[i]=a,o[i]=new RegExp(t,r?"g":void 0),l[i]=new RegExp(a,r?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),g("MAINVERSION",`(${u[c.NUMERICIDENTIFIER]})\\.(${u[c.NUMERICIDENTIFIER]})\\.(${u[c.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${u[c.NUMERICIDENTIFIERLOOSE]})\\.(${u[c.NUMERICIDENTIFIERLOOSE]})\\.(${u[c.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${u[c.NONNUMERICIDENTIFIER]}|${u[c.NUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${u[c.NONNUMERICIDENTIFIER]}|${u[c.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASE",`(?:-(${u[c.PRERELEASEIDENTIFIER]}(?:\\.${u[c.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${u[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[c.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${p}+`),g("BUILD",`(?:\\+(${u[c.BUILDIDENTIFIER]}(?:\\.${u[c.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${u[c.MAINVERSION]}${u[c.PRERELEASE]}?${u[c.BUILD]}?`),g("FULL",`^${u[c.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${u[c.MAINVERSIONLOOSE]}${u[c.PRERELEASELOOSE]}?${u[c.BUILD]}?`),g("LOOSE",`^${u[c.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${u[c.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${u[c.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${u[c.XRANGEIDENTIFIER]})(?:\\.(${u[c.XRANGEIDENTIFIER]})(?:\\.(${u[c.XRANGEIDENTIFIER]})(?:${u[c.PRERELEASE]})?${u[c.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${u[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[c.XRANGEIDENTIFIERLOOSE]})(?:${u[c.PRERELEASELOOSE]})?${u[c.BUILD]}?)?)?`),g("XRANGE",`^${u[c.GTLT]}\\s*${u[c.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${u[c.GTLT]}\\s*${u[c.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${a}})(?:\\.(\\d{1,${a}}))?(?:\\.(\\d{1,${a}}))?`),g("COERCE",`${u[c.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",u[c.COERCEPLAIN]+`(?:${u[c.PRERELEASE]})?`+`(?:${u[c.BUILD]})?`+"(?:$|[^\\d])"),g("COERCERTL",u[c.COERCE],!0),g("COERCERTLFULL",u[c.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${u[c.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${u[c.LONETILDE]}${u[c.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${u[c.LONETILDE]}${u[c.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${u[c.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${u[c.LONECARET]}${u[c.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${u[c.LONECARET]}${u[c.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${u[c.GTLT]}\\s*(${u[c.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${u[c.GTLT]}\\s*(${u[c.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${u[c.GTLT]}\\s*(${u[c.LOOSEPLAIN]}|${u[c.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${u[c.XRANGEPLAIN]})\\s+-\\s+(${u[c.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${u[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[c.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},27290:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0!==a(e,t,r)},28584:(e,t,r)=>{"use strict";let a=r(26515),i=r(32397),n=r(64487),s=r(78668),o=r(58361),l=r(35444),u=r(73051),d=r(90726),c=r(93419),h=r(42467),p=r(40999),f=r(78172),m=r(7110),g=r(33877),y=r(86605),_=r(17950),b=r(24800),v=r(31904),w=r(8536),E=r(42699),O=r(40720),x=r(73438),T=r(27290),S=r(44156),k=r(60301),A=r(84450),I=r(44449),C=r(14239),P=r(3706),j=r(42679),R=r(20938),$=r(43441),N=r(24303),L=r(36686),M=r(31385),U=r(43528),D=r(43900),F=r(22893),z=r(71505);e.exports={parse:o,valid:l,clean:u,inc:d,diff:c,major:h,minor:p,patch:f,prerelease:m,compare:g,rcompare:y,compareLoose:_,compareBuild:b,sort:v,rsort:w,gt:E,lt:O,eq:x,neq:T,gte:S,lte:k,cmp:A,coerce:I,Comparator:C,Range:P,satisfies:j,toComparators:R,maxSatisfying:$,minSatisfying:N,minVersion:L,validRange:M,outside:U,gtr:D,ltr:F,intersects:z,simplifyRange:r(77860),subset:r(11337),SemVer:n,re:a.re,src:a.src,tokens:a.t,SEMVER_SPEC_VERSION:i.SEMVER_SPEC_VERSION,RELEASE_TYPES:i.RELEASE_TYPES,compareIdentifiers:s.compareIdentifiers,rcompareIdentifiers:s.rcompareIdentifiers}},31385:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t)=>{try{return new a(e,t).range||"*"}catch(e){return null}}},31904:(e,t,r)=>{"use strict";let a=r(24800);e.exports=(e,t)=>e.sort((e,r)=>a(e,r,t))},32397:e=>{"use strict";e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},33877:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r)=>new a(e,r).compare(new a(t,r))},35444:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,t);return r?r.version:null}},36686:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706),n=r(42699);e.exports=(e,t)=>{e=new i(e,t);let r=new a("0.0.0");if(e.test(r)||(r=new a("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let i=e.set[t],s=null;i.forEach(e=>{let t=new a(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!s||n(t,s))&&(s=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),s&&(!r||n(r,s))&&(r=s)}return r&&e.test(r)?r:null}},38267:e=>{"use strict";e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},40720:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0>a(e,t,r)},40999:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).minor},42467:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).major},42679:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t,r)=>{try{t=new a(t,r)}catch(e){return!1}return t.test(e)}},42699:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(e,t,r)>0},43441:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new i(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||-1===s.compare(e))&&(s=new a(n=e,r))}),n}},43528:(e,t,r)=>{"use strict";let a=r(64487),i=r(14239),{ANY:n}=i,s=r(3706),o=r(42679),l=r(42699),u=r(40720),d=r(60301),c=r(44156);e.exports=(e,t,r,h)=>{let p,f,m,g,y;switch(e=new a(e,h),t=new s(t,h),r){case">":p=l,f=d,m=u,g=">",y=">=";break;case"<":p=u,f=c,m=l,g="<",y="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(o(e,t,h))return!1;for(let r=0;r<t.set.length;++r){let a=t.set[r],s=null,o=null;if(a.forEach(e=>{e.semver===n&&(e=new i(">=0.0.0")),s=s||e,o=o||e,p(e.semver,s.semver,h)?s=e:m(e.semver,o.semver,h)&&(o=e)}),s.operator===g||s.operator===y||(!o.operator||o.operator===g)&&f(e,o.semver)||o.operator===y&&m(e,o.semver))return!1}return!0}},43900:(e,t,r)=>{"use strict";let a=r(43528);e.exports=(e,t,r)=>a(e,t,">",r)},44156:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(e,t,r)>=0},44449:(e,t,r)=>{"use strict";let a=r(64487),i=r(58361),{safeRe:n,t:s}=r(26515);e.exports=(e,t)=>{if(e instanceof a)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let a,i=t.includePrerelease?n[s.COERCERTLFULL]:n[s.COERCERTL];for(;(a=i.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&a.index+a[0].length===r.index+r[0].length||(r=a),i.lastIndex=a.index+a[1].length+a[2].length;i.lastIndex=-1}else r=e.match(t.includePrerelease?n[s.COERCEFULL]:n[s.COERCE]);if(null===r)return null;let o=r[2],l=r[3]||"0",u=r[4]||"0",d=t.includePrerelease&&r[5]?`-${r[5]}`:"",c=t.includePrerelease&&r[6]?`+${r[6]}`:"";return i(`${o}.${l}.${u}${d}${c}`,t)}},44633:(e,t,r)=>{"use strict";let a,i,n,s,o,l,u,d;r.d(t,{y:()=>oO});var c,h,p,f,m,g,y,_,b,v,w,E,O,x,T,S,k,A={};r.r(A),r.d(A,{JsonPatchError:()=>aF,_areEquals:()=>aK,applyOperation:()=>aH,applyPatch:()=>aq,applyReducer:()=>aJ,deepClone:()=>az,getValueByPointer:()=>aG,validate:()=>aV,validator:()=>aW}),function(e){e.STRING="string",e.NUMBER="number",e.INTEGER="integer",e.BOOLEAN="boolean",e.ARRAY="array",e.OBJECT="object"}(c||(c={})),function(e){e.LANGUAGE_UNSPECIFIED="language_unspecified",e.PYTHON="python"}(h||(h={})),function(e){e.OUTCOME_UNSPECIFIED="outcome_unspecified",e.OUTCOME_OK="outcome_ok",e.OUTCOME_FAILED="outcome_failed",e.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(p||(p={}));let I=["user","model","function","system"];!function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(f||(f={})),function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"}(m||(m={})),function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"}(g||(g={})),function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"}(y||(y={})),function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.OTHER="OTHER"}(_||(_={})),function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"}(b||(b={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"}(v||(v={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(w||(w={}));class C extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class P extends C{constructor(e,t){super(e),this.response=t}}class j extends C{constructor(e,t,r,a){super(e),this.status=t,this.statusText=r,this.errorDetails=a}}class R extends C{}class $ extends C{}!function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"}(E||(E={}));class N{constructor(e,t,r,a,i){this.model=e,this.task=t,this.apiKey=r,this.stream=a,this.requestOptions=i}toString(){var e,t;let r=(null==(e=this.requestOptions)?void 0:e.apiVersion)||"v1beta",a=(null==(t=this.requestOptions)?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com",i=`${a}/${r}/${this.model}:${this.task}`;return this.stream&&(i+="?alt=sse"),i}}async function L(e){var t;let r=new Headers;r.append("Content-Type","application/json"),r.append("x-goog-api-client",function(e){let t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.1"),t.join(" ")}(e.requestOptions)),r.append("x-goog-api-key",e.apiKey);let a=null==(t=e.requestOptions)?void 0:t.customHeaders;if(a){if(!(a instanceof Headers))try{a=new Headers(a)}catch(e){throw new R(`unable to convert customHeaders value ${JSON.stringify(a)} to Headers: ${e.message}`)}for(let[e,t]of a.entries()){if("x-goog-api-key"===e)throw new R(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new R(`Header name ${e} can only be set using the apiClient field`);r.append(e,t)}}return r}async function M(e,t,r,a,i,n){let s=new N(e,t,r,a,n);return{url:s.toString(),fetchOptions:Object.assign(Object.assign({},function(e){let t={};if((null==e?void 0:e.signal)!==void 0||(null==e?void 0:e.timeout)>=0){let r=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout(()=>r.abort(),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",()=>{r.abort()}),t.signal=r.signal}return t}(n)),{method:"POST",headers:await L(s),body:i})}}async function U(e,t,r,a,i,n={},s=fetch){let{url:o,fetchOptions:l}=await M(e,t,r,a,i,n);return D(o,l,s)}async function D(e,t,r=fetch){let a;try{a=await r(e,t)}catch(r){var i=r,n=e;let t=i;throw"AbortError"===t.name?(t=new $(`Request aborted when fetching ${n.toString()}: ${i.message}`)).stack=i.stack:i instanceof j||i instanceof R||((t=new C(`Error fetching from ${n.toString()}: ${i.message}`)).stack=i.stack),t}return a.ok||await F(a,e),a}async function F(e,t){let r,a="";try{let t=await e.json();a=t.error.message,t.error.details&&(a+=` ${JSON.stringify(t.error.details)}`,r=t.error.details)}catch(e){}throw new j(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${a}`,e.status,e.statusText,r)}function z(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),G(e.candidates[0]))throw new P(`${H(e)}`,e);return function(e){var t,r,a,i;let n=[];if(null==(r=null==(t=e.candidates)?void 0:t[0].content)?void 0:r.parts)for(let t of null==(i=null==(a=e.candidates)?void 0:a[0].content)?void 0:i.parts)t.text&&n.push(t.text),t.executableCode&&n.push("\n```"+t.executableCode.language+"\n"+t.executableCode.code+"\n```\n"),t.codeExecutionResult&&n.push("\n```\n"+t.codeExecutionResult.output+"\n```\n");return n.length>0?n.join(""):""}(e)}if(e.promptFeedback)throw new P(`Text not available. ${H(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),G(e.candidates[0]))throw new P(`${H(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),B(e)[0]}if(e.promptFeedback)throw new P(`Function call not available. ${H(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),G(e.candidates[0]))throw new P(`${H(e)}`,e);return B(e)}if(e.promptFeedback)throw new P(`Function call not available. ${H(e)}`,e)},e}function B(e){var t,r,a,i;let n=[];if(null==(r=null==(t=e.candidates)?void 0:t[0].content)?void 0:r.parts)for(let t of null==(i=null==(a=e.candidates)?void 0:a[0].content)?void 0:i.parts)t.functionCall&&n.push(t.functionCall);return n.length>0?n:void 0}let Z=[_.RECITATION,_.SAFETY,_.LANGUAGE];function G(e){return!!e.finishReason&&Z.includes(e.finishReason)}function H(e){var t,r,a;let i="";if((!e.candidates||0===e.candidates.length)&&e.promptFeedback)i+="Response was blocked",(null==(t=e.promptFeedback)?void 0:t.blockReason)&&(i+=` due to ${e.promptFeedback.blockReason}`),(null==(r=e.promptFeedback)?void 0:r.blockReasonMessage)&&(i+=`: ${e.promptFeedback.blockReasonMessage}`);else if(null==(a=e.candidates)?void 0:a[0]){let t=e.candidates[0];G(t)&&(i+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(i+=`: ${t.finishMessage}`))}return i}function q(e){return this instanceof q?(this.v=e,this):new q(e)}"function"==typeof SuppressedError&&SuppressedError;let J=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function W(e){let t=[],r=e.getReader();for(;;){let{done:e,value:a}=await r.read();if(e)return z(function(e){let t=e[e.length-1],r={promptFeedback:null==t?void 0:t.promptFeedback};for(let t of e){if(t.candidates){let e=0;for(let a of t.candidates)if(r.candidates||(r.candidates=[]),r.candidates[e]||(r.candidates[e]={index:e}),r.candidates[e].citationMetadata=a.citationMetadata,r.candidates[e].groundingMetadata=a.groundingMetadata,r.candidates[e].finishReason=a.finishReason,r.candidates[e].finishMessage=a.finishMessage,r.candidates[e].safetyRatings=a.safetyRatings,a.content&&a.content.parts){r.candidates[e].content||(r.candidates[e].content={role:a.content.role||"user",parts:[]});let t={};for(let i of a.content.parts)i.text&&(t.text=i.text),i.functionCall&&(t.functionCall=i.functionCall),i.executableCode&&(t.executableCode=i.executableCode),i.codeExecutionResult&&(t.codeExecutionResult=i.codeExecutionResult),0===Object.keys(t).length&&(t.text=""),r.candidates[e].content.parts.push(t)}e++}t.usageMetadata&&(r.usageMetadata=t.usageMetadata)}return r}(t));t.push(a)}}async function V(e,t,r,a){let[i,n]=(function(e){let t=e.getReader();return new ReadableStream({start(e){let r="";return function a(){return t.read().then(({value:t,done:i})=>{let n;if(i)return r.trim()?void e.error(new C("Failed to parse stream")):void e.close();let s=(r+=t).match(J);for(;s;){try{n=JSON.parse(s[1])}catch(t){e.error(new C(`Error parsing JSON response: "${s[1]}"`));return}e.enqueue(n),s=(r=r.substring(s[0].length)).match(J)}return a()}).catch(e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new $("Request aborted when reading from the stream"):new C("Error reading from the stream")})}()}})})((await U(t,E.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(r),a)).body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))).tee();return{stream:function(e){return function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var a,i=r.apply(e,t||[]),n=[];return a={},s("next"),s("throw"),s("return"),a[Symbol.asyncIterator]=function(){return this},a;function s(e){i[e]&&(a[e]=function(t){return new Promise(function(r,a){n.push([e,t,r,a])>1||o(e,t)})})}function o(e,t){try{var r;(r=i[e](t)).value instanceof q?Promise.resolve(r.value.v).then(l,u):d(n[0][2],r)}catch(e){d(n[0][3],e)}}function l(e){o("next",e)}function u(e){o("throw",e)}function d(e,t){e(t),n.shift(),n.length&&o(n[0][0],n[0][1])}}(this,arguments,function*(){let t=e.getReader();for(;;){let{value:e,done:r}=yield q(t.read());if(r)break;yield yield q(z(e))}})}(i),response:W(n)}}async function K(e,t,r,a){let i=await U(t,E.GENERATE_CONTENT,e,!1,JSON.stringify(r),a);return{response:z(await i.json())}}function Y(e){if(null!=e){if("string"==typeof e)return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)if(!e.role)return{role:"system",parts:e.parts};else return e}}function X(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(let r of e)"string"==typeof r?t.push({text:r}):t.push(r);var r=t;let a={role:"user",parts:[]},i={role:"function",parts:[]},n=!1,s=!1;for(let e of r)"functionResponse"in e?(i.parts.push(e),s=!0):(a.parts.push(e),n=!0);if(n&&s)throw new C("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!n&&!s)throw new C("No content is provided for sending chat message.");return n?a:i}function Q(e){let t;return t=e.contents?e:{contents:[X(e)]},e.systemInstruction&&(t.systemInstruction=Y(e.systemInstruction)),t}let ee=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],et={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function er(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;let r=null==(t=e.candidates[0])?void 0:t.content;if(void 0===r||void 0===r.parts||0===r.parts.length)return!1;for(let e of r.parts)if(void 0===e||0===Object.keys(e).length||void 0!==e.text&&""===e.text)return!1;return!0}let ea="SILENT_ERROR";class ei{constructor(e,t,r,a={}){this.model=t,this.params=r,this._requestOptions=a,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==r?void 0:r.history)&&(!function(e){let t=!1;for(let r of e){let{role:e,parts:a}=r;if(!t&&"user"!==e)throw new C(`First content should be with role 'user', got ${e}`);if(!I.includes(e))throw new C(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(I)}`);if(!Array.isArray(a))throw new C("Content should have 'parts' property with an array of Parts");if(0===a.length)throw new C("Each Content should have at least one part");let i={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let e of a)for(let t of ee)t in e&&(i[t]+=1);let n=et[e];for(let t of ee)if(!n.includes(t)&&i[t]>0)throw new C(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(r.history),this._history=r.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var r,a,i,n,s,o;let l;await this._sendPromise;let u=X(e),d={safetySettings:null==(r=this.params)?void 0:r.safetySettings,generationConfig:null==(a=this.params)?void 0:a.generationConfig,tools:null==(i=this.params)?void 0:i.tools,toolConfig:null==(n=this.params)?void 0:n.toolConfig,systemInstruction:null==(s=this.params)?void 0:s.systemInstruction,cachedContent:null==(o=this.params)?void 0:o.cachedContent,contents:[...this._history,u]},c=Object.assign(Object.assign({},this._requestOptions),t);return this._sendPromise=this._sendPromise.then(()=>K(this._apiKey,this.model,d,c)).then(e=>{var t;if(er(e.response)){this._history.push(u);let r=Object.assign({parts:[],role:"model"},null==(t=e.response.candidates)?void 0:t[0].content);this._history.push(r)}else{let t=H(e.response);t&&console.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}l=e}).catch(e=>{throw this._sendPromise=Promise.resolve(),e}),await this._sendPromise,l}async sendMessageStream(e,t={}){var r,a,i,n,s,o;await this._sendPromise;let l=X(e),u={safetySettings:null==(r=this.params)?void 0:r.safetySettings,generationConfig:null==(a=this.params)?void 0:a.generationConfig,tools:null==(i=this.params)?void 0:i.tools,toolConfig:null==(n=this.params)?void 0:n.toolConfig,systemInstruction:null==(s=this.params)?void 0:s.systemInstruction,cachedContent:null==(o=this.params)?void 0:o.cachedContent,contents:[...this._history,l]},d=Object.assign(Object.assign({},this._requestOptions),t),c=V(this._apiKey,this.model,u,d);return this._sendPromise=this._sendPromise.then(()=>c).catch(e=>{throw Error(ea)}).then(e=>e.response).then(e=>{if(er(e)){this._history.push(l);let t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{let t=H(e);t&&console.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}}).catch(e=>{e.message!==ea&&console.error(e)}),c}}async function en(e,t,r,a){return(await U(t,E.COUNT_TOKENS,e,!1,JSON.stringify(r),a)).json()}async function es(e,t,r,a){return(await U(t,E.EMBED_CONTENT,e,!1,JSON.stringify(r),a)).json()}async function eo(e,t,r,a){let i=r.requests.map(e=>Object.assign(Object.assign({},e),{model:t}));return(await U(t,E.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:i}),a)).json()}class el{constructor(e,t,r={}){this.apiKey=e,this._requestOptions=r,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=Y(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var r;let a=Q(e),i=Object.assign(Object.assign({},this._requestOptions),t);return K(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(r=this.cachedContent)?void 0:r.name},a),i)}async generateContentStream(e,t={}){var r;let a=Q(e),i=Object.assign(Object.assign({},this._requestOptions),t);return V(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(r=this.cachedContent)?void 0:r.name},a),i)}startChat(e){var t;return new ei(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(t=this.cachedContent)?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){let r=function(e,t){var r;let a={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null==(r=null==t?void 0:t.cachedContent)?void 0:r.name,contents:[]},i=null!=e.generateContentRequest;if(e.contents){if(i)throw new R("CountTokensRequest must have one of contents or generateContentRequest, not both.");a.contents=e.contents}else if(i)a=Object.assign(Object.assign({},a),e.generateContentRequest);else{let t=X(e);a.contents=[t]}return{generateContentRequest:a}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),a=Object.assign(Object.assign({},this._requestOptions),t);return en(this.apiKey,this.model,r,a)}async embedContent(e,t={}){let r="string"==typeof e||Array.isArray(e)?{content:X(e)}:e,a=Object.assign(Object.assign({},this._requestOptions),t);return es(this.apiKey,this.model,r,a)}async batchEmbedContents(e,t={}){let r=Object.assign(Object.assign({},this._requestOptions),t);return eo(this.apiKey,this.model,e,r)}}class eu{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new C("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new el(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,r){if(!e.name)throw new R("Cached content must contain a `name` field.");if(!e.model)throw new R("Cached content must contain a `model` field.");for(let r of["model","systemInstruction"])if((null==t?void 0:t[r])&&e[r]&&(null==t?void 0:t[r])!==e[r]){if("model"===r&&(t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue;throw new R(`Different value for "${r}" specified in modelParams (${t[r]}) and cachedContent (${e[r]})`)}let a=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new el(this.apiKey,a,r)}}let ed=()=>"undefined"!=typeof window&&void 0!==window.document,ec=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,eh=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&navigator.userAgent.includes("jsdom"),ep=()=>"undefined"!=typeof Deno,ef=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!ep(),em=()=>{let e;return ed()?"browser":ef()?"node":ec()?"webworker":eh()?"jsdom":ep()?"deno":"other"};function eg(e){try{if("undefined"!=typeof process)return process.env?.[e];if(ep())return Deno?.env.get(e);return}catch(e){return}}function ey(e,t=e_){let r=(e=e.trim()).indexOf("```");if(-1===r)return t(e);let a=e.substring(r+3);a.startsWith("json\n")?a=a.substring(5):a.startsWith("json")?a=a.substring(4):a.startsWith("\n")&&(a=a.substring(1));let i=a.indexOf("```"),n=a;return -1!==i&&(n=a.substring(0,i)),t(n.trim())}function e_(e){if(void 0===e)return null;try{return JSON.parse(e)}catch(e){}let t="",r=[],a=!1,i=!1;for(let n of e){if(a)'"'!==n||i?"\n"!==n||i?i="\\"===n&&!i:n="\\n":a=!1;else if('"'===n)a=!0,i=!1;else if("{"===n)r.push("}");else if("["===n)r.push("]");else if("}"===n||"]"===n)if(!r||r[r.length-1]!==n)return null;else r.pop();t+=n}a&&(t+='"');for(let e=r.length-1;e>=0;e-=1)t+=r[e];try{return JSON.parse(t)}catch(e){return null}}var eb=r(57543);function ev(e,t){return t?.[e]||eb(e)}function ew(e){return Array.isArray(e)?[...e]:{...e}}function eE(e){let t=Object.getPrototypeOf(e);return"function"==typeof e.lc_name&&("function"!=typeof t.lc_name||e.lc_name()!==t.lc_name())?e.lc_name():e.name}r(51862);class eO{static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eE(this.constructor)]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}get lc_serializable_keys(){}constructor(e,...t){Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),void 0!==this.lc_serializable_keys?this.lc_kwargs=Object.fromEntries(Object.entries(e||{}).filter(([e])=>this.lc_serializable_keys?.includes(e))):this.lc_kwargs=e??{}}toJSON(){if(!this.lc_serializable||this.lc_kwargs instanceof eO||"object"!=typeof this.lc_kwargs||Array.isArray(this.lc_kwargs))return this.toJSONNotImplemented();let e={},t={},r=Object.keys(this.lc_kwargs).reduce((e,t)=>(e[t]=t in this?this[t]:this.lc_kwargs[t],e),{});for(let a=Object.getPrototypeOf(this);a;a=Object.getPrototypeOf(a))Object.assign(e,Reflect.get(a,"lc_aliases",this)),Object.assign(t,Reflect.get(a,"lc_secrets",this)),Object.assign(r,Reflect.get(a,"lc_attributes",this));return Object.keys(t).forEach(e=>{let t=this,a=r,[i,...n]=e.split(".").reverse();for(let e of n.reverse()){if(!(e in t)||void 0===t[e])return;e in a&&void 0!==a[e]||("object"==typeof t[e]&&null!=t[e]?a[e]={}:Array.isArray(t[e])&&(a[e]=[])),t=t[e],a=a[e]}i in t&&void 0!==t[i]&&(a[i]=a[i]||t[i])}),{lc:1,type:"constructor",id:this.lc_id,kwargs:function(e,t,r){let a={};for(let i in e)Object.hasOwn(e,i)&&(a[t(i,r)]=e[i]);return a}(Object.keys(t).length?function(e,t){let r=ew(e);for(let[e,a]of Object.entries(t)){let[t,...i]=e.split(".").reverse(),n=r;for(let e of i.reverse()){if(void 0===n[e])break;n[e]=ew(n[e]),n=n[e]}void 0!==n[t]&&(n[t]={lc:1,type:"secret",id:[a]})}return r}(r,t):r,ev,e)}}toJSONNotImplemented(){return{lc:1,type:"not_implemented",id:this.lc_id}}}function ex(e){return"object"==typeof e&&null!==e&&"type"in e&&"string"==typeof e.type&&"source_type"in e&&("url"===e.source_type||"base64"===e.source_type||"text"===e.source_type||"id"===e.source_type)}function eT({dataUrl:e,asTypedArray:t=!1}){let r=e.match(/^data:(\w+\/\w+);base64,([A-Za-z0-9+/]+=*)$/);if(r)return{mime_type:r[1].toLowerCase(),data:t?Uint8Array.from(atob(r[2]),e=>e.charCodeAt(0)):r[2]}}function eS(e,t){return"string"==typeof e?""===e?t:"string"==typeof t?e+t:Array.isArray(t)&&t.some(e=>ex(e))?[{type:"text",source_type:"text",text:e},...t]:[{type:"text",text:e},...t]:Array.isArray(t)?eI(e,t)??[...e,...t]:""===t?e:Array.isArray(e)&&e.some(e=>ex(e))?[...e,{type:"file",source_type:"text",text:t}]:[...e,{type:"text",text:t}]}class ek extends eO{get lc_aliases(){return{additional_kwargs:"additional_kwargs",response_metadata:"response_metadata"}}get text(){return"string"==typeof this.content?this.content:Array.isArray(this.content)?this.content.map(e=>"string"==typeof e?e:"text"===e.type?e.text:"").join(""):""}getType(){return this._getType()}constructor(e,t){"string"==typeof e&&(e={content:e,additional_kwargs:t,response_metadata:{}}),e.additional_kwargs||(e.additional_kwargs={}),e.response_metadata||(e.response_metadata={}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","messages"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"additional_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.content=e.content,this.additional_kwargs=e.additional_kwargs,this.response_metadata=e.response_metadata,this.id=e.id}toDict(){return{type:this._getType(),data:this.toJSON().kwargs}}static lc_name(){return"BaseMessage"}get _printableFields(){return{id:this.id,content:this.content,name:this.name,additional_kwargs:this.additional_kwargs,response_metadata:this.response_metadata}}_updateId(e){this.id=e,this.lc_kwargs.id=e}get[Symbol.toStringTag](){return this.constructor.lc_name()}[Symbol.for("nodejs.util.inspect.custom")](e){var t,r;if(null===e)return this;let a=(t=this._printableFields,r=Math.max(4,e),JSON.stringify(function e(t,a){if("object"!=typeof t||null==t)return t;if(a>=r)return Array.isArray(t)?"[Array]":"[Object]";if(Array.isArray(t))return t.map(t=>e(t,a+1));let i={};for(let r of Object.keys(t))i[r]=e(t[r],a+1);return i}(t,0),null,2));return`${this.constructor.lc_name()} ${a}`}}function eA(e,t){let r={...e};for(let[e,a]of Object.entries(t))if(null==r[e])r[e]=a;else if(null==a)continue;else if(typeof r[e]!=typeof a||Array.isArray(r[e])!==Array.isArray(a))throw Error(`field[${e}] already exists in the message chunk, but with a different type.`);else if("string"==typeof r[e]){if("type"===e)continue;r[e]+=a}else if("object"!=typeof r[e]||Array.isArray(r[e]))if(Array.isArray(r[e]))r[e]=eI(r[e],a);else{if(r[e]===a)continue;console.warn(`field[${e}] already exists in this message chunk and value has unsupported type.`)}else r[e]=eA(r[e],a);return r}function eI(e,t){if(void 0!==e||void 0!==t){if(void 0===e||void 0===t)return e||t;let r=[...e];for(let e of t)if("object"==typeof e&&"index"in e&&"number"==typeof e.index){let t=r.findIndex(t=>t.index===e.index);-1!==t?r[t]=eA(r[t],e):r.push(e)}else{if("object"==typeof e&&"text"in e&&""===e.text)continue;r.push(e)}return r}}class eC extends ek{}function eP(e){return"function"==typeof e?._getType}class ej extends ek{static lc_name(){return"ToolMessage"}get lc_aliases(){return{tool_call_id:"tool_call_id"}}constructor(e,t,r){"string"==typeof e&&(e={content:e,name:r,tool_call_id:t}),super(e),Object.defineProperty(this,"lc_direct_tool_output",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}_getType(){return"tool"}static isInstance(e){return"tool"===e._getType()}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}class eR extends eC{constructor(e){super(e),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}static lc_name(){return"ToolMessageChunk"}_getType(){return"tool"}concat(e){var t,r;return new eR({content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),artifact:function(e,t){if(!e&&!t)throw Error("Cannot merge two undefined objects.");if(!e||!t)return e||t;if(typeof e!=typeof t)throw Error(`Cannot merge objects of different types.
Left ${typeof e}
Right ${typeof t}`);if("string"==typeof e&&"string"==typeof t)return e+t;if(Array.isArray(e)&&Array.isArray(t))return eI(e,t);if("object"==typeof e&&"object"==typeof t)return eA(e,t);else if(e===t)return e;else throw Error(`Can not merge objects of different types.
Left ${e}
Right ${t}`)}(this.artifact,e.artifact),tool_call_id:this.tool_call_id,id:this.id??e.id,status:(t=this.status,r=e.status,"error"===t||"error"===r?"error":"success")})}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}class e$ extends ek{get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls"}}constructor(e,t){let r;if("string"==typeof e)r={content:e,tool_calls:[],invalid_tool_calls:[],additional_kwargs:t??{}};else{r=e;let t=r.additional_kwargs?.tool_calls,a=r.tool_calls;null!=t&&t.length>0&&(void 0===a||0===a.length)&&console.warn("New LangChain packages are available that more efficiently handle tool calling.\n\nPlease upgrade your packages to versions that set message tool calls. e.g., `yarn add @langchain/anthropic`, yarn add @langchain/openai`, etc.");try{if(null!=t&&void 0===a){let[e,a]=function(e){let t=[],r=[];for(let a of e)if(!a.function)continue;else{let e=a.function.name;try{let r=JSON.parse(a.function.arguments),i={name:e||"",args:r||{},id:a.id};t.push(i)}catch(t){r.push({name:e,args:a.function.arguments,id:a.id,error:"Malformed args."})}}return[t,r]}(t);r.tool_calls=e??[],r.invalid_tool_calls=a??[]}else r.tool_calls=r.tool_calls??[],r.invalid_tool_calls=r.invalid_tool_calls??[]}catch(e){r.tool_calls=[],r.invalid_tool_calls=[]}}super(r),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"string"!=typeof r&&(this.tool_calls=r.tool_calls??this.tool_calls,this.invalid_tool_calls=r.invalid_tool_calls??this.invalid_tool_calls),this.usage_metadata=r.usage_metadata}static lc_name(){return"AIMessage"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}}function eN(e){return"ai"===e._getType()}function eL(e){return"ai"===e._getType()}class eM extends eC{constructor(e){let t;if("string"==typeof e)t={content:e,tool_calls:[],invalid_tool_calls:[],tool_call_chunks:[]};else if(void 0===e.tool_call_chunks)t={...e,tool_calls:e.tool_calls??[],invalid_tool_calls:[],tool_call_chunks:[],usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0};else{let r=[],a=[];for(let t of e.tool_call_chunks){let e={};try{if(e=e_(t.args||"{}"),null===e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed tool call chunk args.");r.push({name:t.name??"",args:e,id:t.id,type:"tool_call"})}catch(e){a.push({name:t.name,args:t.args,id:t.id,error:"Malformed args.",type:"invalid_tool_call"})}}t={...e,tool_calls:r,invalid_tool_calls:a,usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0}}super(t),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tool_call_chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_chunks=t.tool_call_chunks??this.tool_call_chunks,this.tool_calls=t.tool_calls??this.tool_calls,this.invalid_tool_calls=t.invalid_tool_calls??this.invalid_tool_calls,this.usage_metadata=t.usage_metadata}get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls",tool_call_chunks:"tool_call_chunks"}}static lc_name(){return"AIMessageChunk"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,tool_call_chunks:this.tool_call_chunks,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}concat(e){let t={content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),tool_call_chunks:[],id:this.id??e.id};if(void 0!==this.tool_call_chunks||void 0!==e.tool_call_chunks){let r=eI(this.tool_call_chunks,e.tool_call_chunks);void 0!==r&&r.length>0&&(t.tool_call_chunks=r)}if(void 0!==this.usage_metadata||void 0!==e.usage_metadata){let r={...(this.usage_metadata?.input_token_details?.audio!==void 0||e.usage_metadata?.input_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.input_token_details?.audio??0)+(e.usage_metadata?.input_token_details?.audio??0)},...(this.usage_metadata?.input_token_details?.cache_read!==void 0||e.usage_metadata?.input_token_details?.cache_read!==void 0)&&{cache_read:(this.usage_metadata?.input_token_details?.cache_read??0)+(e.usage_metadata?.input_token_details?.cache_read??0)},...(this.usage_metadata?.input_token_details?.cache_creation!==void 0||e.usage_metadata?.input_token_details?.cache_creation!==void 0)&&{cache_creation:(this.usage_metadata?.input_token_details?.cache_creation??0)+(e.usage_metadata?.input_token_details?.cache_creation??0)}},a={...(this.usage_metadata?.output_token_details?.audio!==void 0||e.usage_metadata?.output_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.output_token_details?.audio??0)+(e.usage_metadata?.output_token_details?.audio??0)},...(this.usage_metadata?.output_token_details?.reasoning!==void 0||e.usage_metadata?.output_token_details?.reasoning!==void 0)&&{reasoning:(this.usage_metadata?.output_token_details?.reasoning??0)+(e.usage_metadata?.output_token_details?.reasoning??0)}},i=this.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0},n=e.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0};t.usage_metadata={input_tokens:i.input_tokens+n.input_tokens,output_tokens:i.output_tokens+n.output_tokens,total_tokens:i.total_tokens+n.total_tokens,...Object.keys(r).length>0&&{input_token_details:r},...Object.keys(a).length>0&&{output_token_details:a}}}return new eM(t)}}class eU extends ek{static lc_name(){return"ChatMessage"}static _chatMessageClass(){return eU}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}static isInstance(e){return"generic"===e._getType()}get _printableFields(){return{...super._printableFields,role:this.role}}}class eD extends eC{static lc_name(){return"ChatMessageChunk"}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}concat(e){return new eD({content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),role:this.role,id:this.id??e.id})}get _printableFields(){return{...super._printableFields,role:this.role}}}class eF extends ek{static lc_name(){return"FunctionMessage"}constructor(e,t){"string"==typeof e&&(e={content:e,name:t}),super(e)}_getType(){return"function"}}class ez extends eC{static lc_name(){return"FunctionMessageChunk"}_getType(){return"function"}concat(e){return new ez({content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),name:this.name??"",id:this.id??e.id})}}class eB extends ek{static lc_name(){return"HumanMessage"}_getType(){return"human"}constructor(e,t){super(e,t)}}class eZ extends eC{static lc_name(){return"HumanMessageChunk"}_getType(){return"human"}constructor(e,t){super(e,t)}concat(e){return new eZ({content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),id:this.id??e.id})}}class eG extends ek{static lc_name(){return"SystemMessage"}_getType(){return"system"}constructor(e,t){super(e,t)}}class eH extends eC{static lc_name(){return"SystemMessageChunk"}_getType(){return"system"}constructor(e,t){super(e,t)}concat(e){return new eH({content:eS(this.content,e.content),additional_kwargs:eA(this.additional_kwargs,e.additional_kwargs),response_metadata:eA(this.response_metadata,e.response_metadata),id:this.id??e.id})}}function eq(e,t){return e.lc_error_code=t,e.message=`${e.message}

Troubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${t}/
`,e}function eJ(e){return!!(e&&"object"==typeof e&&"type"in e&&"tool_call"===e.type)}class eW extends Error{constructor(e,t){super(e),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.output=t}}function eV(e){return eJ(e)?e:"string"==typeof e.id&&"function"===e.type&&"object"==typeof e.function&&null!==e.function&&"arguments"in e.function&&"string"==typeof e.function.arguments&&"name"in e.function&&"string"==typeof e.function.name?{id:e.id,args:JSON.parse(e.function.arguments),name:e.function.name,type:"tool_call"}:e}function eK(e){let t,r;if("object"==typeof e&&null!=e&&1===e.lc&&Array.isArray(e.id)&&null!=e.kwargs&&"object"==typeof e.kwargs){let a=e.id.at(-1);t="HumanMessage"===a||"HumanMessageChunk"===a?"user":"AIMessage"===a||"AIMessageChunk"===a?"assistant":"SystemMessage"===a||"SystemMessageChunk"===a?"system":"FunctionMessage"===a||"FunctionMessageChunk"===a?"function":"ToolMessage"===a||"ToolMessageChunk"===a?"tool":"unknown",r=e.kwargs}else{let{type:a,...i}=e;t=a,r=i}if("human"===t||"user"===t)return new eB(r);if("ai"===t||"assistant"===t){let{tool_calls:e,...t}=r;if(!Array.isArray(e))return new e$(r);let a=e.map(eV);return new e$({...t,tool_calls:a})}if("system"===t)return new eG(r);if("developer"===t)return new eG({...r,additional_kwargs:{...r.additional_kwargs,__openai_role__:"developer"}});if("tool"===t&&"tool_call_id"in r)return new ej({...r,content:r.content,tool_call_id:r.tool_call_id,name:r.name});throw eq(Error(`Unable to coerce message from array: only human, AI, system, developer, or tool message coercion is currently supported.

Received: ${JSON.stringify(e,null,2)}`),"MESSAGE_COERCION_FAILURE")}function eY(e){if("string"==typeof e)return new eB(e);if(eP(e))return e;if(Array.isArray(e)){let[t,r]=e;return eK({type:t,content:r})}if("string"!=typeof e.role)return eK(e);{let{role:t,...r}=e;return eK({...r,type:t})}}function eX(e,t="Human",r="AI"){let a=[];for(let i of e){let e;if("human"===i._getType())e=t;else if("ai"===i._getType())e=r;else if("system"===i._getType())e="System";else if("function"===i._getType())e="Function";else if("tool"===i._getType())e="Tool";else if("generic"===i._getType())e=i.role;else throw Error(`Got unsupported message type: ${i._getType()}`);let n=i.name?`${i.name}, `:"",s="string"==typeof i.content?i.content:JSON.stringify(i.content,null,2);a.push(`${e}: ${n}${s}`)}return a.join("\n")}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(let e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(O||(O={})),(x||(x={})).mergeShapes=(e,t)=>({...e,...t});let eQ=O.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),e0=e=>{switch(typeof e){case"undefined":return eQ.undefined;case"string":return eQ.string;case"number":return Number.isNaN(e)?eQ.nan:eQ.number;case"boolean":return eQ.boolean;case"function":return eQ.function;case"bigint":return eQ.bigint;case"symbol":return eQ.symbol;case"object":if(Array.isArray(e))return eQ.array;if(null===e)return eQ.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return eQ.promise;if("undefined"!=typeof Map&&e instanceof Map)return eQ.map;if("undefined"!=typeof Set&&e instanceof Set)return eQ.set;if("undefined"!=typeof Date&&e instanceof Date)return eQ.date;return eQ.object;default:return eQ.unknown}},e1=O.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class e2 extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},a=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(a);else if("invalid_return_type"===i.code)a(i.returnTypeError);else if("invalid_arguments"===i.code)a(i.argumentsError);else if(0===i.path.length)r._errors.push(t(i));else{let e=r,a=0;for(;a<i.path.length;){let r=i.path[a];a===i.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(i))):e[r]=e[r]||{_errors:[]},e=e[r],a++}}};return a(this),r}static assert(e){if(!(e instanceof e2))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,O.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}e2.create=e=>new e2(e);let e4=(e,t)=>{let r;switch(e.code){case e1.invalid_type:r=e.received===eQ.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case e1.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,O.jsonStringifyReplacer)}`;break;case e1.unrecognized_keys:r=`Unrecognized key(s) in object: ${O.joinValues(e.keys,", ")}`;break;case e1.invalid_union:r="Invalid input";break;case e1.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${O.joinValues(e.options)}`;break;case e1.invalid_enum_value:r=`Invalid enum value. Expected ${O.joinValues(e.options)}, received '${e.received}'`;break;case e1.invalid_arguments:r="Invalid function arguments";break;case e1.invalid_return_type:r="Invalid function return type";break;case e1.invalid_date:r="Invalid date";break;case e1.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:O.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case e1.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case e1.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case e1.custom:r="Invalid input";break;case e1.invalid_intersection_types:r="Intersection results could not be merged";break;case e1.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case e1.not_finite:r="Number must be finite";break;default:r=t.defaultError,O.assertNever(e)}return{message:r}},e5=e=>{let{data:t,path:r,errorMaps:a,issueData:i}=e,n=[...r,...i.path||[]],s={...i,path:n};if(void 0!==i.message)return{...i,path:n,message:i.message};let o="";for(let e of a.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return{...i,path:n,message:o}};function e3(e,t){let r=e5({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,e4,e4==e4?void 0:e4].filter(e=>!!e)});e.common.issues.push(r)}class e9{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let a of t){if("aborted"===a.status)return e6;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,a=await e.value;r.push({key:t,value:a})}return e9.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let a of t){let{key:t,value:i}=a;if("aborted"===t.status||"aborted"===i.status)return e6;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||a.alwaysSet)&&(r[t.value]=i.value)}return{status:e.value,value:r}}}let e6=Object.freeze({status:"aborted"}),e8=e=>({status:"dirty",value:e}),e7=e=>({status:"valid",value:e}),te=e=>"aborted"===e.status,tt=e=>"dirty"===e.status,tr=e=>"valid"===e.status,ta=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(T||(T={}));class ti{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let tn=(e,t)=>{if(tr(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new e2(e.common.issues);return this._error=t,this._error}}};function ts(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:a,description:i}=e;if(t&&(r||a))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:n}=e;return"invalid_enum_value"===t.code?{message:n??i.defaultError}:void 0===i.data?{message:n??a??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:n??r??i.defaultError}},description:i}}class to{get description(){return this._def.description}_getType(e){return e0(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:e0(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new e9,ctx:{common:e.parent.common,data:e.data,parsedType:e0(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(ta(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:e0(e)},a=this._parseSync({data:e,path:r.path,parent:r});return tn(r,a)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:e0(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return tr(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>tr(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:e0(e)},a=this._parse({data:e,path:r.path,parent:r});return tn(r,await (ta(a)?a:Promise.resolve(a)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,a)=>{let i=e(t),n=()=>a.addIssue({code:e1.custom,...r(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(n(),!1)):!!i||(n(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new t0({schema:this,typeName:S.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return t1.create(this,this._def)}nullable(){return t2.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return tM.create(this)}promise(){return tQ.create(this,this._def)}or(e){return tD.create([this,e],this._def)}and(e){return tB.create(this,e,this._def)}transform(e){return new t0({...ts(this._def),schema:this,typeName:S.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new t4({...ts(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:S.ZodDefault})}brand(){return new t9({typeName:S.ZodBranded,type:this,...ts(this._def)})}catch(e){return new t5({...ts(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:S.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return t6.create(this,e)}readonly(){return t8.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let tl=/^c[^\s-]{8,}$/i,tu=/^[0-9a-z]+$/,td=/^[0-9A-HJKMNP-TV-Z]{26}$/i,tc=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,th=/^[a-z0-9_-]{21}$/i,tp=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,tf=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,tm=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,tg=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ty=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,t_=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,tb=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,tv=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,tw=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,tE="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",tO=RegExp(`^${tE}$`);function tx(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class tT extends to{_parse(e){var t,r,a,n;let s;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==eQ.string){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.string,received:t.parsedType}),e6}let o=new e9;for(let l of this._def.checks)if("min"===l.kind)e.data.length<l.value&&(e3(s=this._getOrReturnCtx(e,s),{code:e1.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("max"===l.kind)e.data.length>l.value&&(e3(s=this._getOrReturnCtx(e,s),{code:e1.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("length"===l.kind){let t=e.data.length>l.value,r=e.data.length<l.value;(t||r)&&(s=this._getOrReturnCtx(e,s),t?e3(s,{code:e1.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):r&&e3(s,{code:e1.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),o.dirty())}else if("email"===l.kind)tm.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"email",code:e1.invalid_string,message:l.message}),o.dirty());else if("emoji"===l.kind)i||(i=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),i.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"emoji",code:e1.invalid_string,message:l.message}),o.dirty());else if("uuid"===l.kind)tc.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"uuid",code:e1.invalid_string,message:l.message}),o.dirty());else if("nanoid"===l.kind)th.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"nanoid",code:e1.invalid_string,message:l.message}),o.dirty());else if("cuid"===l.kind)tl.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"cuid",code:e1.invalid_string,message:l.message}),o.dirty());else if("cuid2"===l.kind)tu.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"cuid2",code:e1.invalid_string,message:l.message}),o.dirty());else if("ulid"===l.kind)td.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"ulid",code:e1.invalid_string,message:l.message}),o.dirty());else if("url"===l.kind)try{new URL(e.data)}catch{e3(s=this._getOrReturnCtx(e,s),{validation:"url",code:e1.invalid_string,message:l.message}),o.dirty()}else"regex"===l.kind?(l.regex.lastIndex=0,l.regex.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"regex",code:e1.invalid_string,message:l.message}),o.dirty())):"trim"===l.kind?e.data=e.data.trim():"includes"===l.kind?e.data.includes(l.value,l.position)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),o.dirty()):"toLowerCase"===l.kind?e.data=e.data.toLowerCase():"toUpperCase"===l.kind?e.data=e.data.toUpperCase():"startsWith"===l.kind?e.data.startsWith(l.value)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:{startsWith:l.value},message:l.message}),o.dirty()):"endsWith"===l.kind?e.data.endsWith(l.value)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:{endsWith:l.value},message:l.message}),o.dirty()):"datetime"===l.kind?(function(e){let t=`${tE}T${tx(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(l).test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:"datetime",message:l.message}),o.dirty()):"date"===l.kind?tO.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:"date",message:l.message}),o.dirty()):"time"===l.kind?RegExp(`^${tx(l)}$`).test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{code:e1.invalid_string,validation:"time",message:l.message}),o.dirty()):"duration"===l.kind?tf.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"duration",code:e1.invalid_string,message:l.message}),o.dirty()):"ip"===l.kind?(t=e.data,!(("v4"===(r=l.version)||!r)&&tg.test(t)||("v6"===r||!r)&&t_.test(t))&&1&&(e3(s=this._getOrReturnCtx(e,s),{validation:"ip",code:e1.invalid_string,message:l.message}),o.dirty())):"jwt"===l.kind?!function(e,t){if(!tp.test(e))return!1;try{let[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),i=JSON.parse(atob(a));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,l.alg)&&(e3(s=this._getOrReturnCtx(e,s),{validation:"jwt",code:e1.invalid_string,message:l.message}),o.dirty()):"cidr"===l.kind?(a=e.data,!(("v4"===(n=l.version)||!n)&&ty.test(a)||("v6"===n||!n)&&tb.test(a))&&1&&(e3(s=this._getOrReturnCtx(e,s),{validation:"cidr",code:e1.invalid_string,message:l.message}),o.dirty())):"base64"===l.kind?tv.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"base64",code:e1.invalid_string,message:l.message}),o.dirty()):"base64url"===l.kind?tw.test(e.data)||(e3(s=this._getOrReturnCtx(e,s),{validation:"base64url",code:e1.invalid_string,message:l.message}),o.dirty()):O.assertNever(l);return{status:o.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:e1.invalid_string,...T.errToObj(r)})}_addCheck(e){return new tT({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...T.errToObj(e)})}url(e){return this._addCheck({kind:"url",...T.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...T.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...T.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...T.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...T.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...T.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...T.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...T.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...T.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...T.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...T.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...T.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...T.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...T.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...T.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...T.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...T.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...T.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...T.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...T.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...T.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...T.errToObj(t)})}nonempty(e){return this.min(1,T.errToObj(e))}trim(){return new tT({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new tT({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new tT({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}tT.create=e=>new tT({checks:[],typeName:S.ZodString,coerce:e?.coerce??!1,...ts(e)});class tS extends to{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==eQ.number){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.number,received:t.parsedType}),e6}let r=new e9;for(let a of this._def.checks)"int"===a.kind?O.isInteger(e.data)||(e3(t=this._getOrReturnCtx(e,t),{code:e1.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"multipleOf"===a.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,i=r>a?r:a;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,a.value)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(e3(t=this._getOrReturnCtx(e,t),{code:e1.not_finite,message:a.message}),r.dirty()):O.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,T.toString(t))}gt(e,t){return this.setLimit("min",e,!1,T.toString(t))}lte(e,t){return this.setLimit("max",e,!0,T.toString(t))}lt(e,t){return this.setLimit("max",e,!1,T.toString(t))}setLimit(e,t,r,a){return new tS({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:T.toString(a)}]})}_addCheck(e){return new tS({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:T.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:T.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:T.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:T.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:T.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:T.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:T.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:T.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:T.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&O.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}tS.create=e=>new tS({checks:[],typeName:S.ZodNumber,coerce:e?.coerce||!1,...ts(e)});class tk extends to{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==eQ.bigint)return this._getInvalidInput(e);let r=new e9;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):O.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.bigint,received:t.parsedType}),e6}gte(e,t){return this.setLimit("min",e,!0,T.toString(t))}gt(e,t){return this.setLimit("min",e,!1,T.toString(t))}lte(e,t){return this.setLimit("max",e,!0,T.toString(t))}lt(e,t){return this.setLimit("max",e,!1,T.toString(t))}setLimit(e,t,r,a){return new tk({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:T.toString(a)}]})}_addCheck(e){return new tk({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:T.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:T.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:T.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:T.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:T.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}tk.create=e=>new tk({checks:[],typeName:S.ZodBigInt,coerce:e?.coerce??!1,...ts(e)});class tA extends to{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==eQ.boolean){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.boolean,received:t.parsedType}),e6}return e7(e.data)}}tA.create=e=>new tA({typeName:S.ZodBoolean,coerce:e?.coerce||!1,...ts(e)});class tI extends to{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==eQ.date){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.date,received:t.parsedType}),e6}if(Number.isNaN(e.data.getTime()))return e3(this._getOrReturnCtx(e),{code:e1.invalid_date}),e6;let r=new e9;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(e3(t=this._getOrReturnCtx(e,t),{code:e1.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):O.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new tI({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:T.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:T.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}tI.create=e=>new tI({checks:[],coerce:e?.coerce||!1,typeName:S.ZodDate,...ts(e)});class tC extends to{_parse(e){if(this._getType(e)!==eQ.symbol){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.symbol,received:t.parsedType}),e6}return e7(e.data)}}tC.create=e=>new tC({typeName:S.ZodSymbol,...ts(e)});class tP extends to{_parse(e){if(this._getType(e)!==eQ.undefined){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.undefined,received:t.parsedType}),e6}return e7(e.data)}}tP.create=e=>new tP({typeName:S.ZodUndefined,...ts(e)});class tj extends to{_parse(e){if(this._getType(e)!==eQ.null){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.null,received:t.parsedType}),e6}return e7(e.data)}}tj.create=e=>new tj({typeName:S.ZodNull,...ts(e)});class tR extends to{constructor(){super(...arguments),this._any=!0}_parse(e){return e7(e.data)}}tR.create=e=>new tR({typeName:S.ZodAny,...ts(e)});class t$ extends to{constructor(){super(...arguments),this._unknown=!0}_parse(e){return e7(e.data)}}t$.create=e=>new t$({typeName:S.ZodUnknown,...ts(e)});class tN extends to{_parse(e){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.never,received:t.parsedType}),e6}}tN.create=e=>new tN({typeName:S.ZodNever,...ts(e)});class tL extends to{_parse(e){if(this._getType(e)!==eQ.undefined){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.void,received:t.parsedType}),e6}return e7(e.data)}}tL.create=e=>new tL({typeName:S.ZodVoid,...ts(e)});class tM extends to{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==eQ.array)return e3(t,{code:e1.invalid_type,expected:eQ.array,received:t.parsedType}),e6;if(null!==a.exactLength){let e=t.data.length>a.exactLength.value,i=t.data.length<a.exactLength.value;(e||i)&&(e3(t,{code:e?e1.too_big:e1.too_small,minimum:i?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(e3(t,{code:e1.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(e3(t,{code:e1.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new ti(t,e,t.path,r)))).then(e=>e9.mergeArray(r,e));let i=[...t.data].map((e,r)=>a.type._parseSync(new ti(t,e,t.path,r)));return e9.mergeArray(r,i)}get element(){return this._def.type}min(e,t){return new tM({...this._def,minLength:{value:e,message:T.toString(t)}})}max(e,t){return new tM({...this._def,maxLength:{value:e,message:T.toString(t)}})}length(e,t){return new tM({...this._def,exactLength:{value:e,message:T.toString(t)}})}nonempty(e){return this.min(1,e)}}tM.create=(e,t)=>new tM({type:e,minLength:null,maxLength:null,exactLength:null,typeName:S.ZodArray,...ts(t)});class tU extends to{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=O.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==eQ.object){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.object,received:t.parsedType}),e6}let{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),n=[];if(!(this._def.catchall instanceof tN&&"strip"===this._def.unknownKeys))for(let e in r.data)i.includes(e)||n.push(e);let s=[];for(let e of i){let t=a[e],i=r.data[e];s.push({key:{status:"valid",value:e},value:t._parse(new ti(r,i,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof tN){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of n)s.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)n.length>0&&(e3(r,{code:e1.unrecognized_keys,keys:n}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of n){let a=r.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new ti(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>e9.mergeObjectSync(t,e)):e9.mergeObjectSync(t,s)}get shape(){return this._def.shape()}strict(e){return T.errToObj,new tU({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:T.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new tU({...this._def,unknownKeys:"strip"})}passthrough(){return new tU({...this._def,unknownKeys:"passthrough"})}extend(e){return new tU({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new tU({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:S.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new tU({...this._def,catchall:e})}pick(e){let t={};for(let r of O.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new tU({...this._def,shape:()=>t})}omit(e){let t={};for(let r of O.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new tU({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof tU){let r={};for(let a in t.shape){let i=t.shape[a];r[a]=t1.create(e(i))}return new tU({...t._def,shape:()=>r})}if(t instanceof tM)return new tM({...t._def,type:e(t.element)});if(t instanceof t1)return t1.create(e(t.unwrap()));if(t instanceof t2)return t2.create(e(t.unwrap()));if(t instanceof tZ)return tZ.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of O.objectKeys(this.shape)){let a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new tU({...this._def,shape:()=>t})}required(e){let t={};for(let r of O.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof t1;)e=e._def.innerType;t[r]=e}return new tU({...this._def,shape:()=>t})}keyof(){return tK(O.objectKeys(this.shape))}}tU.create=(e,t)=>new tU({shape:()=>e,unknownKeys:"strip",catchall:tN.create(),typeName:S.ZodObject,...ts(t)}),tU.strictCreate=(e,t)=>new tU({shape:()=>e,unknownKeys:"strict",catchall:tN.create(),typeName:S.ZodObject,...ts(t)}),tU.lazycreate=(e,t)=>new tU({shape:e,unknownKeys:"strip",catchall:tN.create(),typeName:S.ZodObject,...ts(t)});class tD extends to{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new e2(e.ctx.common.issues));return e3(t,{code:e1.invalid_union,unionErrors:r}),e6});{let e,a=[];for(let i of r){let r={...t,common:{...t.common,issues:[]},parent:null},n=i._parseSync({data:t.data,path:t.path,parent:r});if("valid"===n.status)return n;"dirty"!==n.status||e||(e={result:n,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=a.map(e=>new e2(e));return e3(t,{code:e1.invalid_union,unionErrors:i}),e6}}get options(){return this._def.options}}tD.create=(e,t)=>new tD({options:e,typeName:S.ZodUnion,...ts(t)});let tF=e=>{if(e instanceof tW)return tF(e.schema);if(e instanceof t0)return tF(e.innerType());if(e instanceof tV)return[e.value];if(e instanceof tY)return e.options;if(e instanceof tX)return O.objectValues(e.enum);else if(e instanceof t4)return tF(e._def.innerType);else if(e instanceof tP)return[void 0];else if(e instanceof tj)return[null];else if(e instanceof t1)return[void 0,...tF(e.unwrap())];else if(e instanceof t2)return[null,...tF(e.unwrap())];else if(e instanceof t9)return tF(e.unwrap());else if(e instanceof t8)return tF(e.unwrap());else if(e instanceof t5)return tF(e._def.innerType);else return[]};class tz extends to{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eQ.object)return e3(t,{code:e1.invalid_type,expected:eQ.object,received:t.parsedType}),e6;let r=this.discriminator,a=t.data[r],i=this.optionsMap.get(a);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(e3(t,{code:e1.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),e6)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=tF(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(a.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);a.set(i,r)}}return new tz({typeName:S.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...ts(r)})}}class tB extends to{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=(e,a)=>{if(te(e)||te(a))return e6;let i=function e(t,r){let a=e0(t),i=e0(r);if(t===r)return{valid:!0,data:t};if(a===eQ.object&&i===eQ.object){let a=O.objectKeys(r),i=O.objectKeys(t).filter(e=>-1!==a.indexOf(e)),n={...t,...r};for(let a of i){let i=e(t[a],r[a]);if(!i.valid)return{valid:!1};n[a]=i.data}return{valid:!0,data:n}}if(a===eQ.array&&i===eQ.array){if(t.length!==r.length)return{valid:!1};let a=[];for(let i=0;i<t.length;i++){let n=e(t[i],r[i]);if(!n.valid)return{valid:!1};a.push(n.data)}return{valid:!0,data:a}}if(a===eQ.date&&i===eQ.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,a.value);return i.valid?((tt(e)||tt(a))&&t.dirty(),{status:t.value,value:i.data}):(e3(r,{code:e1.invalid_intersection_types}),e6)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}tB.create=(e,t,r)=>new tB({left:e,right:t,typeName:S.ZodIntersection,...ts(r)});class tZ extends to{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eQ.array)return e3(r,{code:e1.invalid_type,expected:eQ.array,received:r.parsedType}),e6;if(r.data.length<this._def.items.length)return e3(r,{code:e1.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),e6;!this._def.rest&&r.data.length>this._def.items.length&&(e3(r,{code:e1.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...r.data].map((e,t)=>{let a=this._def.items[t]||this._def.rest;return a?a._parse(new ti(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>e9.mergeArray(t,e)):e9.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new tZ({...this._def,rest:e})}}tZ.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new tZ({items:e,typeName:S.ZodTuple,rest:null,...ts(t)})};class tG extends to{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eQ.object)return e3(r,{code:e1.invalid_type,expected:eQ.object,received:r.parsedType}),e6;let a=[],i=this._def.keyType,n=this._def.valueType;for(let e in r.data)a.push({key:i._parse(new ti(r,e,r.path,e)),value:n._parse(new ti(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?e9.mergeObjectAsync(t,a):e9.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new tG(t instanceof to?{keyType:e,valueType:t,typeName:S.ZodRecord,...ts(r)}:{keyType:tT.create(),valueType:e,typeName:S.ZodRecord,...ts(t)})}}class tH extends to{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eQ.map)return e3(r,{code:e1.invalid_type,expected:eQ.map,received:r.parsedType}),e6;let a=this._def.keyType,i=this._def.valueType,n=[...r.data.entries()].map(([e,t],n)=>({key:a._parse(new ti(r,e,r.path,[n,"key"])),value:i._parse(new ti(r,t,r.path,[n,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of n){let a=await r.key,i=await r.value;if("aborted"===a.status||"aborted"===i.status)return e6;("dirty"===a.status||"dirty"===i.status)&&t.dirty(),e.set(a.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of n){let a=r.key,i=r.value;if("aborted"===a.status||"aborted"===i.status)return e6;("dirty"===a.status||"dirty"===i.status)&&t.dirty(),e.set(a.value,i.value)}return{status:t.value,value:e}}}}tH.create=(e,t,r)=>new tH({valueType:t,keyType:e,typeName:S.ZodMap,...ts(r)});class tq extends to{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eQ.set)return e3(r,{code:e1.invalid_type,expected:eQ.set,received:r.parsedType}),e6;let a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(e3(r,{code:e1.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(e3(r,{code:e1.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());let i=this._def.valueType;function n(e){let r=new Set;for(let a of e){if("aborted"===a.status)return e6;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}let s=[...r.data.values()].map((e,t)=>i._parse(new ti(r,e,r.path,t)));return r.common.async?Promise.all(s).then(e=>n(e)):n(s)}min(e,t){return new tq({...this._def,minSize:{value:e,message:T.toString(t)}})}max(e,t){return new tq({...this._def,maxSize:{value:e,message:T.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}tq.create=(e,t)=>new tq({valueType:e,minSize:null,maxSize:null,typeName:S.ZodSet,...ts(t)});class tJ extends to{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eQ.function)return e3(t,{code:e1.invalid_type,expected:eQ.function,received:t.parsedType}),e6;function r(e,r){return e5({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,e4,e4].filter(e=>!!e),issueData:{code:e1.invalid_arguments,argumentsError:r}})}function a(e,r){return e5({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,e4,e4].filter(e=>!!e),issueData:{code:e1.invalid_return_type,returnTypeError:r}})}let i={errorMap:t.common.contextualErrorMap},n=t.data;if(this._def.returns instanceof tQ){let e=this;return e7(async function(...t){let s=new e2([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw s.addIssue(r(t,e)),s}),l=await Reflect.apply(n,this,o);return await e._def.returns._def.type.parseAsync(l,i).catch(e=>{throw s.addIssue(a(l,e)),s})})}{let e=this;return e7(function(...t){let s=e._def.args.safeParse(t,i);if(!s.success)throw new e2([r(t,s.error)]);let o=Reflect.apply(n,this,s.data),l=e._def.returns.safeParse(o,i);if(!l.success)throw new e2([a(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new tJ({...this._def,args:tZ.create(e).rest(t$.create())})}returns(e){return new tJ({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new tJ({args:e||tZ.create([]).rest(t$.create()),returns:t||t$.create(),typeName:S.ZodFunction,...ts(r)})}}class tW extends to{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}tW.create=(e,t)=>new tW({getter:e,typeName:S.ZodLazy,...ts(t)});class tV extends to{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return e3(t,{received:t.data,code:e1.invalid_literal,expected:this._def.value}),e6}return{status:"valid",value:e.data}}get value(){return this._def.value}}function tK(e,t){return new tY({values:e,typeName:S.ZodEnum,...ts(t)})}tV.create=(e,t)=>new tV({value:e,typeName:S.ZodLiteral,...ts(t)});class tY extends to{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return e3(t,{expected:O.joinValues(r),received:t.parsedType,code:e1.invalid_type}),e6}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return e3(t,{received:t.data,code:e1.invalid_enum_value,options:r}),e6}return e7(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return tY.create(e,{...this._def,...t})}exclude(e,t=this._def){return tY.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}tY.create=tK;class tX extends to{_parse(e){let t=O.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==eQ.string&&r.parsedType!==eQ.number){let e=O.objectValues(t);return e3(r,{expected:O.joinValues(e),received:r.parsedType,code:e1.invalid_type}),e6}if(this._cache||(this._cache=new Set(O.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=O.objectValues(t);return e3(r,{received:r.data,code:e1.invalid_enum_value,options:e}),e6}return e7(e.data)}get enum(){return this._def.values}}tX.create=(e,t)=>new tX({values:e,typeName:S.ZodNativeEnum,...ts(t)});class tQ extends to{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==eQ.promise&&!1===t.common.async?(e3(t,{code:e1.invalid_type,expected:eQ.promise,received:t.parsedType}),e6):e7((t.parsedType===eQ.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}tQ.create=(e,t)=>new tQ({type:e,typeName:S.ZodPromise,...ts(t)});class t0 extends to{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===S.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,i={addIssue:e=>{e3(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===a.type){let e=a.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return e6;let a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?e6:"dirty"===a.status||"dirty"===t.value?e8(a.value):a});{if("aborted"===t.value)return e6;let a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?e6:"dirty"===a.status||"dirty"===t.value?e8(a.value):a}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?e6:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===a.status?e6:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}}if("transform"===a.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>tr(e)?Promise.resolve(a.transform(e.value,i)).then(e=>({status:t.value,value:e})):e6);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!tr(e))return e6;let n=a.transform(e.value,i);if(n instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}O.assertNever(a)}}t0.create=(e,t,r)=>new t0({schema:e,typeName:S.ZodEffects,effect:t,...ts(r)}),t0.createWithPreprocess=(e,t,r)=>new t0({schema:t,effect:{type:"preprocess",transform:e},typeName:S.ZodEffects,...ts(r)});class t1 extends to{_parse(e){return this._getType(e)===eQ.undefined?e7(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t1.create=(e,t)=>new t1({innerType:e,typeName:S.ZodOptional,...ts(t)});class t2 extends to{_parse(e){return this._getType(e)===eQ.null?e7(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t2.create=(e,t)=>new t2({innerType:e,typeName:S.ZodNullable,...ts(t)});class t4 extends to{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===eQ.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}t4.create=(e,t)=>new t4({innerType:e,typeName:S.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...ts(t)});class t5 extends to{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return ta(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new e2(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new e2(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}t5.create=(e,t)=>new t5({innerType:e,typeName:S.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...ts(t)});class t3 extends to{_parse(e){if(this._getType(e)!==eQ.nan){let t=this._getOrReturnCtx(e);return e3(t,{code:e1.invalid_type,expected:eQ.nan,received:t.parsedType}),e6}return{status:"valid",value:e.data}}}t3.create=e=>new t3({typeName:S.ZodNaN,...ts(e)}),Symbol("zod_brand");class t9 extends to{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class t6 extends to{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?e6:"dirty"===e.status?(t.dirty(),e8(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?e6:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new t6({in:e,out:t,typeName:S.ZodPipeline})}}class t8 extends to{_parse(e){let t=this._def.innerType._parse(e),r=e=>(tr(e)&&(e.value=Object.freeze(e.value)),e);return ta(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}t8.create=(e,t)=>new t8({innerType:e,typeName:S.ZodReadonly,...ts(t)}),tU.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(S||(S={}));let t7=tT.create;tS.create,t3.create,tk.create,tA.create,tI.create,tC.create,tP.create,tj.create;let re=tR.create;t$.create,tN.create,tL.create,tM.create;let rt=tU.create;tU.strictCreate,tD.create,tz.create,tB.create,tZ.create,tG.create,tH.create,tq.create,tJ.create,tW.create,tV.create,tY.create,tX.create,tQ.create,t0.create,t1.create,t2.create,t0.createWithPreprocess,t6.create;var rr=r(63611),ra=r(77598),ri=r.n(ra);let rn={randomUUID:ri().randomUUID},rs=new Uint8Array(256),ro=rs.length,rl=[];for(let e=0;e<256;++e)rl.push((e+256).toString(16).slice(1));let ru=function(e,t,r){if(rn.randomUUID&&!t&&!e)return rn.randomUUID();let a=(e=e||{}).random||(e.rng||function(){return ro>rs.length-16&&(ri().randomFillSync(rs),ro=0),rs.slice(ro,ro+=16)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=a[e];return t}return function(e,t=0){return(rl[e[t+0]]+rl[e[t+1]]+rl[e[t+2]]+rl[e[t+3]]+"-"+rl[e[t+4]]+rl[e[t+5]]+"-"+rl[e[t+6]]+rl[e[t+7]]+"-"+rl[e[t+8]]+rl[e[t+9]]+"-"+rl[e[t+10]]+rl[e[t+11]]+rl[e[t+12]]+rl[e[t+13]]+rl[e[t+14]]+rl[e[t+15]]).toLowerCase()}(a)},rd={randomUUID:ri().randomUUID},rc=new Uint8Array(256),rh=rc.length,rp=[];for(let e=0;e<256;++e)rp.push((e+256).toString(16).slice(1));function rf(e,t=0){return(rp[e[t+0]]+rp[e[t+1]]+rp[e[t+2]]+rp[e[t+3]]+"-"+rp[e[t+4]]+rp[e[t+5]]+"-"+rp[e[t+6]]+rp[e[t+7]]+"-"+rp[e[t+8]]+rp[e[t+9]]+"-"+rp[e[t+10]]+rp[e[t+11]]+rp[e[t+12]]+rp[e[t+13]]+rp[e[t+14]]+rp[e[t+15]]).toLowerCase()}let rm=function(e,t,r){if(rd.randomUUID&&!t&&!e)return rd.randomUUID();let a=(e=e||{}).random||(e.rng||function(){return rh>rc.length-16&&(ri().randomFillSync(rc),rh=0),rc.slice(rh,rh+=16)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=a[e];return t}return rf(a)},rg=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,ry=function(e){let t;if(!("string"==typeof e&&rg.test(e)))throw TypeError("Invalid UUID");let r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r},r_=function(e,t,r){function a(e,t,a,i){var n;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));let t=[];for(let r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof t&&(t=ry(t)),(null==(n=t)?void 0:n.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let s=new Uint8Array(16+e.length);if(s.set(t),s.set(e,t.length),(s=r(s))[6]=15&s[6]|80,s[8]=63&s[8]|128,a){i=i||0;for(let e=0;e<16;++e)a[i+e]=s[e];return a}return rf(s)}try{a.name="v5"}catch(e){}return a.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",a.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",a}(0,0,function(e){return Array.isArray(e)?e=Buffer.from(e):"string"==typeof e&&(e=Buffer.from(e,"utf8")),ri().createHash("sha1").update(e).digest()}),rb="gen_ai.request.model",rv="gen_ai.usage.input_tokens",rw="gen_ai.usage.output_tokens",rE="gen_ai.usage.total_tokens",rO="langsmith.span.tags",rx=(...e)=>fetch(...e),rT=Symbol.for("ls:fetch_implementation"),rS=()=>{let e=globalThis[rT];return!!e&&"function"==typeof e&&"Headers"in e&&"Request"in e&&"Response"in e},rk=e=>async(...t)=>{if(e||"true"===rU("DEBUG")){let[e,r]=t;console.log(`→ ${r?.method||"GET"} ${e}`)}let r=await (globalThis[rT]??rx)(...t);return(e||"true"===rU("DEBUG"))&&console.log(`← ${r.status} ${r.statusText} ${r.url}`),r},rA=()=>rU("PROJECT")??rM("LANGCHAIN_SESSION")??"default",rI="0.3.36",rC=()=>"undefined"!=typeof window&&void 0!==window.document,rP=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,rj=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&navigator.userAgent.includes("jsdom"),rR=()=>"undefined"!=typeof Deno,r$=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!rR(),rN=()=>n||(n=rC()?"browser":r$()?"node":rP()?"webworker":rj()?"jsdom":rR()?"deno":"other");function rL(){return void 0===s&&(s={library:"langsmith",runtime:rN(),sdk:"langsmith-js",sdk_version:rI,...function(){if(void 0!==o)return o;let e={};for(let t of["VERCEL_GIT_COMMIT_SHA","NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA","COMMIT_REF","RENDER_GIT_COMMIT","CI_COMMIT_SHA","CIRCLE_SHA1","CF_PAGES_COMMIT_SHA","REACT_APP_GIT_SHA","SOURCE_VERSION","GITHUB_SHA","TRAVIS_COMMIT","GIT_COMMIT","BUILD_VCS_NUMBER","bamboo_planRepository_revision","Build.SourceVersion","BITBUCKET_COMMIT","DRONE_COMMIT_SHA","SEMAPHORE_GIT_SHA","BUILDKITE_COMMIT"]){let r=rM(t);void 0!==r&&(e[t]=r)}return o=e,e}()}),s}function rM(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}function rU(e){return rM(`LANGSMITH_${e}`)||rM(`LANGCHAIN_${e}`)}class rD{constructor(){Object.defineProperty(this,"hasWarned",{enumerable:!0,configurable:!0,writable:!0,value:!1})}startActiveSpan(e,...t){let r;if(this.hasWarned||"true"!==rM("OTEL_ENABLED")||(console.warn('You have enabled OTEL export via the `OTEL_ENABLED` environment variable, but have not initialized the required OTEL instances. Please add:\n```\nimport { initializeOTEL } from "langsmith/experimental/otel/setup";\ninitializeOTEL();\n```\nat the beginning of your code.'),this.hasWarned=!0),1===t.length&&"function"==typeof t[0]?r=t[0]:2===t.length&&"function"==typeof t[1]?r=t[1]:3===t.length&&"function"==typeof t[2]&&(r=t[2]),"function"==typeof r)return r()}}class rF{constructor(){Object.defineProperty(this,"mockTracer",{enumerable:!0,configurable:!0,writable:!0,value:new rD})}getTracer(e,t){return this.mockTracer}getActiveSpan(){}setSpan(e,t){return e}getSpan(e){}setSpanContext(e,t){return e}getTracerProvider(){}setGlobalTracerProvider(e){return!1}}class rz{active(){return{}}with(e,t){return t()}}let rB=Symbol.for("ls:otel_trace"),rZ=Symbol.for("ls:otel_context"),rG=Symbol.for("ls:otel_get_default_otlp_tracer_provider"),rH=new rF,rq=new rz;class rJ{getTraceInstance(){return globalThis[rB]??rH}getContextInstance(){return globalThis[rZ]??rq}initializeGlobalInstances(e){void 0===globalThis[rB]&&(globalThis[rB]=e.trace),void 0===globalThis[rZ]&&(globalThis[rZ]=e.context)}setDefaultOTLPTracerComponents(e){globalThis[rG]=e}getDefaultOTLPTracerComponents(){return globalThis[rG]??void 0}}let rW=new rJ;function rV(){return rW.getTraceInstance()}let rK={llm:"chat",tool:"execute_tool",retriever:"embeddings",embedding:"embeddings",prompt:"chat"};class rY{constructor(){Object.defineProperty(this,"spans",{enumerable:!0,configurable:!0,writable:!0,value:new Map})}exportBatch(e,t){for(let r of e)try{if(!r.run)continue;if("post"===r.operation){let e=this.createSpanForRun(r,r.run,t.get(r.id));e&&!r.run.end_time&&this.spans.set(r.id,e)}else this.updateSpanForRun(r,r.run)}catch(e){console.error(`Error processing operation ${r.id}:`,e)}}createSpanForRun(e,t,r){let a=r&&rV().getSpan(r);if(a)try{return this.finishSpanSetup(a,t,e)}catch(t){console.error(`Failed to create span for run ${e.id}:`,t);return}}finishSpanSetup(e,t,r){return this.setSpanAttributes(e,t,r),t.error?(e.setStatus({code:2}),e.recordException(Error(t.error))):e.setStatus({code:1}),t.end_time&&e.end(t.end_time),e}updateSpanForRun(e,t){try{let r=this.spans.get(e.id);if(!r)return void console.debug(`No span found for run ${e.id} during update`);this.setSpanAttributes(r,t,e),t.error?(r.setStatus({code:2}),r.recordException(Error(t.error))):r.setStatus({code:1});let a=t.end_time;a&&(r.end(a),this.spans.delete(e.id))}catch(t){console.error(`Failed to update span for run ${e.id}:`,t)}}extractModelName(e){if(e.extra?.metadata){let t=e.extra.metadata;if(t.ls_model_name)return t.ls_model_name;if(t.invocation_params){let e=t.invocation_params;if(e.model)return e.model;if(e.model_name)return e.model_name}}}setSpanAttributes(e,t,r){if("run_type"in t&&t.run_type){var a;e.setAttribute("langsmith.span.kind",t.run_type);let r=rK[a=t.run_type||"chain"]||a;e.setAttribute("gen_ai.operation.name",r)}"name"in t&&t.name&&e.setAttribute("langsmith.trace.name",t.name),"session_id"in t&&t.session_id&&e.setAttribute("langsmith.trace.session_id",t.session_id),"session_name"in t&&t.session_name&&e.setAttribute("langsmith.trace.session_name",t.session_name),this.setGenAiSystem(e,t);let i=this.extractModelName(t);for(let[r,a]of(i&&e.setAttribute(rb,i),"prompt_tokens"in t&&"number"==typeof t.prompt_tokens&&e.setAttribute(rv,t.prompt_tokens),"completion_tokens"in t&&"number"==typeof t.completion_tokens&&e.setAttribute(rw,t.completion_tokens),"total_tokens"in t&&"number"==typeof t.total_tokens&&e.setAttribute(rE,t.total_tokens),this.setInvocationParameters(e,t),Object.entries(t.extra?.metadata||{})))null!=a&&e.setAttribute(`langsmith.metadata.${r}`,String(a));let n=t.tags;if(n&&Array.isArray(n)?e.setAttribute(rO,n.join(", ")):n&&e.setAttribute(rO,String(n)),"serialized"in t&&"object"==typeof t.serialized){let r=t.serialized;r.name&&e.setAttribute("gen_ai.serialized.name",String(r.name)),r.signature&&e.setAttribute("gen_ai.serialized.signature",String(r.signature)),r.doc&&e.setAttribute("gen_ai.serialized.doc",String(r.doc))}this.setIOAttributes(e,r)}setGenAiSystem(e,t){let r="langchain",a=this.extractModelName(t);if(a){let e=a.toLowerCase();e.includes("anthropic")||e.startsWith("claude")?r="anthropic":e.includes("bedrock")?r="aws.bedrock":e.includes("azure")&&e.includes("openai")?r="az.ai.openai":e.includes("azure")&&e.includes("inference")?r="az.ai.inference":e.includes("cohere")?r="cohere":e.includes("deepseek")?r="deepseek":e.includes("gemini")?r="gemini":e.includes("groq")?r="groq":e.includes("watson")||e.includes("ibm")?r="ibm.watsonx.ai":e.includes("mistral")?r="mistral_ai":e.includes("gpt")||e.includes("openai")?r="openai":e.includes("perplexity")||e.includes("sonar")?r="perplexity":e.includes("vertex")?r="vertex_ai":(e.includes("xai")||e.includes("grok"))&&(r="xai")}e.setAttribute("gen_ai.system",r)}setInvocationParameters(e,t){if(!t.extra?.metadata?.invocation_params)return;let r=t.extra.metadata.invocation_params;void 0!==r.max_tokens&&e.setAttribute("gen_ai.request.max_tokens",r.max_tokens),void 0!==r.temperature&&e.setAttribute("gen_ai.request.temperature",r.temperature),void 0!==r.top_p&&e.setAttribute("gen_ai.request.top_p",r.top_p),void 0!==r.frequency_penalty&&e.setAttribute("gen_ai.request.frequency_penalty",r.frequency_penalty),void 0!==r.presence_penalty&&e.setAttribute("gen_ai.request.presence_penalty",r.presence_penalty)}setIOAttributes(e,t){if(t.run.inputs)try{let r=t.run.inputs;"object"==typeof r&&null!==r&&(r.model&&Array.isArray(r.messages)&&e.setAttribute(rb,r.model),void 0!==r.stream&&e.setAttribute("langsmith.request.streaming",r.stream),r.extra_headers&&e.setAttribute("langsmith.request.headers",JSON.stringify(r.extra_headers)),r.extra_query&&e.setAttribute("gen_ai.request.extra_query",JSON.stringify(r.extra_query)),r.extra_body&&e.setAttribute("gen_ai.request.extra_body",JSON.stringify(r.extra_body))),e.setAttribute("gen_ai.prompt",JSON.stringify(r))}catch(e){console.debug(`Failed to process inputs for run ${t.id}`,e)}if(t.run.outputs)try{let r=t.run.outputs,a=this.getUnifiedRunTokens(r);if(a&&(e.setAttribute(rv,a[0]),e.setAttribute(rw,a[1]),e.setAttribute(rE,a[0]+a[1])),r&&"object"==typeof r){if(r.model&&e.setAttribute("gen_ai.response.model",String(r.model)),r.id&&e.setAttribute("gen_ai.response.id",r.id),r.choices&&Array.isArray(r.choices)){let t=r.choices.map(e=>e.finish_reason).filter(e=>e).map(String);t.length>0&&e.setAttribute("gen_ai.response.finish_reasons",t.join(", "))}if(r.service_tier&&e.setAttribute("gen_ai.response.service_tier",r.service_tier),r.system_fingerprint&&e.setAttribute("gen_ai.response.system_fingerprint",r.system_fingerprint),r.usage_metadata&&"object"==typeof r.usage_metadata){let t=r.usage_metadata;t.input_token_details&&e.setAttribute("gen_ai.usage.input_token_details",JSON.stringify(t.input_token_details)),t.output_token_details&&e.setAttribute("gen_ai.usage.output_token_details",JSON.stringify(t.output_token_details))}}e.setAttribute("gen_ai.completion",JSON.stringify(r))}catch(e){console.debug(`Failed to process outputs for run ${t.id}`,e)}}getUnifiedRunTokens(e){if(!e)return null;let t=this.extractUnifiedRunTokens(e.usage_metadata);if(t)return t;for(let r of Object.keys(e)){let a=e[r];if(a&&"object"==typeof a&&((t=this.extractUnifiedRunTokens(a.usage_metadata))||1===a.lc&&a.kwargs&&"object"==typeof a.kwargs&&(t=this.extractUnifiedRunTokens(a.kwargs.usage_metadata))))return t}let r=e.generations||[];if(!Array.isArray(r))return null;for(let e of Array.isArray(r[0])?r.flat():r)if("object"==typeof e&&e.message&&"object"==typeof e.message&&e.message.kwargs&&"object"==typeof e.message.kwargs&&(t=this.extractUnifiedRunTokens(e.message.kwargs.usage_metadata)))return t;return null}extractUnifiedRunTokens(e){return e&&"object"==typeof e&&"number"==typeof e.input_tokens&&"number"==typeof e.output_tokens?[e.input_tokens,e.output_tokens]:null}}var rX=r(71719);let rQ=[400,401,403,404,405,406,407,408],r0=[409];class r1{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedResponseHook",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.debug=e.debug,this.queue=new rX.default({concurrency:this.maxConcurrency}),this.onFailedResponseHook=e?.onFailedResponseHook}call(e,...t){let r=this.onFailedResponseHook;return this.queue.add(()=>rr(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{async onFailedAttempt(e){if(e.message.startsWith("Cancel")||e.message.startsWith("TimeoutError")||e.message.startsWith("AbortError")||e?.code==="ECONNABORTED")throw e;let t=e?.response,a=t?.status;if(a){if(rQ.includes(+a))throw e;if(r0.includes(+a))return;r&&await r(t)}},retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>rk(this.debug)(...e).then(e=>e.ok?e:Promise.reject(e)))}}function r2(e){return"function"==typeof e?._getType}function r4(e){let t={type:e._getType(),data:{content:e.content}};return e?.additional_kwargs&&Object.keys(e.additional_kwargs).length>0&&(t.data.additional_kwargs={...e.additional_kwargs}),t}let r5=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function r3(e,t){if(!r5.test(e))throw Error(void 0!==t?`Invalid UUID for ${t}: ${e}`:`Invalid UUID: ${e}`);return e}let r9={};function r6(e){r9[e]||(console.warn(e),r9[e]=!0)}function r8(e){if(!e||e.split("/").length>2||e.startsWith("/")||e.endsWith("/")||e.split(":").length>2)throw Error(`Invalid identifier format: ${e}`);let[t,r]=e.split(":"),a=r||"latest";if(t.includes("/")){let[r,i]=t.split("/",2);if(!r||!i)throw Error(`Invalid identifier format: ${e}`);return[r,i,a]}if(!t)throw Error(`Invalid identifier format: ${e}`);return["-",t,a]}r(28584);class r7 extends Error{constructor(e){super(e),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="LangSmithConflictError",this.status=409}}async function ae(e,t,r){let a;if(e.ok){r&&(a=await e.text());return}a=await e.text();let i=`Failed to ${t}. Received status [${e.status}]: ${e.statusText}. Server response: ${a}`;if(409===e.status)throw new r7(i);let n=Error(i);throw n.status=e.status,n}var at={result:"[Circular]"},ar=[],aa=[];let ai=new TextEncoder;function an(e){return ai.encode(e)}function as(e,t,r,a,i){try{let t=JSON.stringify(e,r,a);return an(t)}catch(s){let n;if(!s.message?.includes("Converting circular structure to JSON"))return console.warn(`[WARNING]: LangSmith received unserializable value.${t?`
Context: ${t}`:""}`),an("[Unserializable]");"true"!==rU("SUPPRESS_CIRCULAR_JSON_WARNINGS")&&console.warn(`[WARNING]: LangSmith received circular JSON. This will decrease tracer performance. ${t?`
Context: ${t}`:""}`),void 0===i&&(i={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,r,a,i,n,s,o){if(s+=1,"object"==typeof t&&null!==t){for(l=0;l<i.length;l++)if(i[l]===t)return void ao(at,t,r,n);if(void 0!==o.depthLimit&&s>o.depthLimit||void 0!==o.edgesLimit&&a+1>o.edgesLimit)return void ao("[...]",t,r,n);if(i.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,i,t,s,o);else{var l,u=Object.keys(t);for(l=0;l<u.length;l++){var d=u[l];e(t[d],d,l,i,t,s,o)}}i.pop()}}(e,"",0,[],void 0,0,i);try{n=0===aa.length?JSON.stringify(e,r,a):JSON.stringify(e,function(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(aa.length>0)for(var a=0;a<aa.length;a++){var i=aa[a];if(i[1]===t&&i[0]===r){r=i[2],aa.splice(a,1);break}}return e.call(this,t,r)}}(r),a)}catch(e){return an("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==ar.length;){let e=ar.pop();4===e.length?Object.defineProperty(e[0],e[1],e[3]):e[0][e[1]]=e[2]}}return an(n)}}function ao(e,t,r,a){var i=Object.getOwnPropertyDescriptor(a,r);void 0!==i.get?i.configurable?(Object.defineProperty(a,r,{value:e}),ar.push([a,r,t,i])):aa.push([t,r,e]):(a[r]=e,ar.push([a,r,t]))}function al(e){let t=rL(),r=function(){let e=function(){try{if("undefined"!=typeof process&&process.env)return Object.entries(process.env).reduce((e,[t,r])=>(e[t]=String(r),e),{});return}catch(e){return}}()||{},t={},r=["LANGCHAIN_API_KEY","LANGCHAIN_ENDPOINT","LANGCHAIN_TRACING_V2","LANGCHAIN_PROJECT","LANGCHAIN_SESSION","LANGSMITH_API_KEY","LANGSMITH_ENDPOINT","LANGSMITH_TRACING_V2","LANGSMITH_PROJECT","LANGSMITH_SESSION"];for(let[a,i]of Object.entries(e))(a.startsWith("LANGCHAIN_")||a.startsWith("LANGSMITH_"))&&"string"==typeof i&&!r.includes(a)&&!a.toLowerCase().includes("key")&&!a.toLowerCase().includes("secret")&&!a.toLowerCase().includes("token")&&("LANGCHAIN_REVISION_ID"===a?t.revision_id=i:t[a]=i);return t}(),a=e.extra??{},i=a.metadata;return e.extra={...a,runtime:{...t,...a?.runtime},metadata:{...r,...r.revision_id||e.revision_id?{revision_id:e.revision_id??r.revision_id}:{},...i}},e}let au=e=>{let t=e?.toString()??rU("TRACING_SAMPLING_RATE");if(void 0===t)return;let r=parseFloat(t);if(r<0||r>1)throw Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${r}`);return r},ad=e=>{let t=e.replace("http://","").replace("https://","").split("/")[0].split(":")[0];return"localhost"===t||"127.0.0.1"===t||"::1"===t};async function ac(e){let t=[];for await(let r of e)t.push(r);return t}function ah(e){if(void 0!==e)return e.trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}let ap=async e=>{if(e?.status===429){let t=1e3*parseInt(e.headers.get("retry-after")??"30",10);if(t>0)return await new Promise(e=>setTimeout(e,t)),!0}return!1};function af(e){return"number"==typeof e?Number(e.toFixed(4)):e}class am{constructor(){Object.defineProperty(this,"items",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"sizeBytes",{enumerable:!0,configurable:!0,writable:!0,value:0})}peek(){return this.items[0]}push(e){let t,r=new Promise(e=>{t=e}),a=as(e.item,`Serializing run with id: ${e.item.id}`).length;return this.items.push({action:e.action,payload:e.item,otelContext:e.otelContext,itemPromiseResolve:t,itemPromise:r,size:a}),this.sizeBytes+=a,r}pop(e){if(e<1)throw Error("Number of bytes to pop off may not be less than 1.");let t=[],r=0;for(;r+(this.peek()?.size??0)<e&&this.items.length>0;){let e=this.items.shift();e&&(t.push(e),r+=e.size,this.sizeBytes-=e.size)}if(0===t.length&&this.items.length>0){let e=this.items.shift();t.push(e),r+=e.size,this.sizeBytes-=e.size}return[t.map(e=>({action:e.action,item:e.payload,otelContext:e.otelContext})),()=>t.forEach(e=>e.itemPromiseResolve())]}}class ag{constructor(e={}){Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"webUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"batchIngestCaller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timeout_ms",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tenantId",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hideInputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"hideOutputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingSampleRate",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filteredPostUuids",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"autoBatchTracing",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"autoBatchQueue",{enumerable:!0,configurable:!0,writable:!0,value:new am}),Object.defineProperty(this,"autoBatchTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoBatchAggregationDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:250}),Object.defineProperty(this,"batchSizeBytesLimit",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fetchOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"settings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"blockOnRootRunFinalization",{enumerable:!0,configurable:!0,writable:!0,value:"false"===rM("LANGSMITH_TRACING_BACKGROUND")}),Object.defineProperty(this,"traceBatchConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:5}),Object.defineProperty(this,"_serverInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_getServerInfoPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"manualFlushMode",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"langSmithToOTELTranslator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:"true"===rM("LANGSMITH_DEBUG")});let t=ag.getDefaultClientConfig();if(this.tracingSampleRate=au(e.tracingSamplingRate),this.apiUrl=ah(e.apiUrl??t.apiUrl)??"",this.apiUrl.endsWith("/")&&(this.apiUrl=this.apiUrl.slice(0,-1)),this.apiKey=ah(e.apiKey??t.apiKey),this.webUrl=ah(e.webUrl??t.webUrl),this.webUrl?.endsWith("/")&&(this.webUrl=this.webUrl.slice(0,-1)),this.timeout_ms=e.timeout_ms??9e4,this.caller=new r1({...e.callerOptions??{},debug:e.debug??this.debug}),this.traceBatchConcurrency=e.traceBatchConcurrency??this.traceBatchConcurrency,this.traceBatchConcurrency<1)throw Error("Trace batch concurrency must be positive.");this.debug=e.debug??this.debug,this.batchIngestCaller=new r1({maxRetries:2,maxConcurrency:this.traceBatchConcurrency,...e.callerOptions??{},onFailedResponseHook:ap,debug:e.debug??this.debug}),this.hideInputs=e.hideInputs??e.anonymizer??t.hideInputs,this.hideOutputs=e.hideOutputs??e.anonymizer??t.hideOutputs,this.autoBatchTracing=e.autoBatchTracing??this.autoBatchTracing,this.blockOnRootRunFinalization=e.blockOnRootRunFinalization??this.blockOnRootRunFinalization,this.batchSizeBytesLimit=e.batchSizeBytesLimit,this.fetchOptions=e.fetchOptions||{},this.manualFlushMode=e.manualFlushMode??this.manualFlushMode,"true"===rM("OTEL_ENABLED")&&(this.langSmithToOTELTranslator=new rY)}static getDefaultClientConfig(){let e=rU("API_KEY"),t=rU("ENDPOINT")??"https://api.smith.langchain.com";return{apiUrl:t,apiKey:e,webUrl:void 0,hideInputs:"true"===rU("HIDE_INPUTS"),hideOutputs:"true"===rU("HIDE_OUTPUTS")}}getHostUrl(){if(this.webUrl)return this.webUrl;if(ad(this.apiUrl))return this.webUrl="http://localhost:3000",this.webUrl;if(this.apiUrl.endsWith("/api/v1"))return this.webUrl=this.apiUrl.replace("/api/v1",""),this.webUrl;if(this.apiUrl.includes("/api")&&!this.apiUrl.split(".",1)[0].endsWith("api"))return this.webUrl=this.apiUrl.replace("/api",""),this.webUrl;if(this.apiUrl.split(".",1)[0].includes("dev"))return this.webUrl="https://dev.smith.langchain.com",this.webUrl;else if(this.apiUrl.split(".",1)[0].includes("eu"))return this.webUrl="https://eu.smith.langchain.com",this.webUrl;else if(this.apiUrl.split(".",1)[0].includes("beta"))return this.webUrl="https://beta.smith.langchain.com",this.webUrl;else return this.webUrl="https://smith.langchain.com",this.webUrl}get headers(){let e={"User-Agent":`langsmith-js/${rI}`};return this.apiKey&&(e["x-api-key"]=`${this.apiKey}`),e}async processInputs(e){return!1===this.hideInputs?e:!0===this.hideInputs?{}:"function"==typeof this.hideInputs?this.hideInputs(e):e}async processOutputs(e){return!1===this.hideOutputs?e:!0===this.hideOutputs?{}:"function"==typeof this.hideOutputs?this.hideOutputs(e):e}async prepareRunCreateOrUpdateInputs(e){let t={...e};return void 0!==t.inputs&&(t.inputs=await this.processInputs(t.inputs)),void 0!==t.outputs&&(t.outputs=await this.processOutputs(t.outputs)),t}async _getResponse(e,t){let r=t?.toString()??"",a=`${this.apiUrl}${e}?${r}`,i=await this.caller.call(rk(this.debug),a,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(i,`Failed to fetch ${e}`),i}async _get(e,t){return(await this._getResponse(e,t)).json()}async *_getPaginated(e,t=new URLSearchParams,r){let a=Number(t.get("offset"))||0,i=Number(t.get("limit"))||100;for(;;){t.set("offset",String(a)),t.set("limit",String(i));let n=`${this.apiUrl}${e}?${t}`,s=await this.caller.call(rk(this.debug),n,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(s,`Failed to fetch ${e}`);let o=r?r(await s.json()):await s.json();if(0===o.length||(yield o,o.length<i))break;a+=o.length}}async *_getCursorPaginatedList(e,t=null,r="POST",a="runs"){let i=t?{...t}:{};for(;;){let t=await this.caller.call(rk(this.debug),`${this.apiUrl}${e}`,{method:r,headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions,body:JSON.stringify(i)}),n=await t.json();if(!n||!n[a])break;yield n[a];let s=n.cursors;if(!s||!s.next)break;i.cursor=s.next}}_shouldSample(){return void 0===this.tracingSampleRate||Math.random()<this.tracingSampleRate}_filterForSampling(e,t=!1){if(void 0===this.tracingSampleRate)return e;if(t){let t=[];for(let r of e)this.filteredPostUuids.has(r.id)?this.filteredPostUuids.delete(r.id):t.push(r);return t}{let t=[];for(let r of e){let e=r.trace_id??r.id;this.filteredPostUuids.has(e)||(r.id===e?this._shouldSample()?t.push(r):this.filteredPostUuids.add(e):t.push(r))}return t}}async _getBatchSizeLimitBytes(){let e=await this._ensureServerInfo();return this.batchSizeBytesLimit??e.batch_ingest_config?.size_limit_bytes??0x1400000}async _getMultiPartSupport(){let e=await this._ensureServerInfo();return e.instance_flags?.dataset_examples_multipart_enabled??!1}drainAutoBatchQueue(e){let t=[];for(;this.autoBatchQueue.items.length>0;){let[r,a]=this.autoBatchQueue.pop(e);if(!r.length){a();break}let i=this._processBatch(r,a).catch(console.error);t.push(i)}return Promise.all(t)}async _processBatch(e,t){if(!e.length)return void t();try{if(void 0!==this.langSmithToOTELTranslator)this._sendBatchToOTELTranslator(e);else{let t={runCreates:e.filter(e=>"create"===e.action).map(e=>e.item),runUpdates:e.filter(e=>"update"===e.action).map(e=>e.item)},r=await this._ensureServerInfo();r?.batch_ingest_config?.use_multipart_endpoint?await this.multipartIngestRuns(t):await this.batchIngestRuns(t)}}catch(e){console.error("Error exporting batch:",e)}finally{t()}}_sendBatchToOTELTranslator(e){if(void 0!==this.langSmithToOTELTranslator){let t=new Map,r=[];for(let a of e)a.item.id&&a.otelContext&&(t.set(a.item.id,a.otelContext),"create"===a.action?r.push({operation:"post",id:a.item.id,trace_id:a.item.trace_id??a.item.id,run:a.item}):r.push({operation:"patch",id:a.item.id,trace_id:a.item.trace_id??a.item.id,run:a.item}));this.langSmithToOTELTranslator.exportBatch(r,t)}}async processRunOperation(e){clearTimeout(this.autoBatchTimeout),this.autoBatchTimeout=void 0,"create"===e.action&&(e.item=al(e.item));let t=this.autoBatchQueue.push(e);if(this.manualFlushMode)return t;let r=await this._getBatchSizeLimitBytes();return this.autoBatchQueue.sizeBytes>r&&this.drainAutoBatchQueue(r),this.autoBatchQueue.items.length>0&&(this.autoBatchTimeout=setTimeout(()=>{this.autoBatchTimeout=void 0,this.drainAutoBatchQueue(r)},this.autoBatchAggregationDelayMs)),t}async _getServerInfo(){let e=await this.caller.call(rk(this.debug),`${this.apiUrl}/info`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(2500),...this.fetchOptions});await ae(e,"get server info");let t=await e.json();return this.debug&&console.log("\n=== LangSmith Server Configuration ===\n"+JSON.stringify(t,null,2)+"\n"),t}async _ensureServerInfo(){return void 0===this._getServerInfoPromise&&(this._getServerInfoPromise=(async()=>{if(void 0===this._serverInfo)try{this._serverInfo=await this._getServerInfo()}catch(e){console.warn(`[WARNING]: LangSmith failed to fetch info on supported operations with status code ${e.status}. Falling back to batch operations and default limits.`)}return this._serverInfo??{}})()),this._getServerInfoPromise.then(e=>(void 0===this._serverInfo&&(this._getServerInfoPromise=void 0),e))}async _getSettings(){return this.settings||(this.settings=this._get("/settings")),await this.settings}async flush(){let e=await this._getBatchSizeLimitBytes();await this.drainAutoBatchQueue(e)}_cloneCurrentOTELContext(){let e=rV(),t=rW.getContextInstance();if(void 0!==this.langSmithToOTELTranslator){let r=e.getActiveSpan();if(r)return e.setSpan(t.active(),r)}}async createRun(e){if(!this._filterForSampling([e]).length)return;let t={...this.headers,"Content-Type":"application/json"},r=e.project_name;delete e.project_name;let a=await this.prepareRunCreateOrUpdateInputs({session_name:r,...e,start_time:e.start_time??Date.now()});if(this.autoBatchTracing&&void 0!==a.trace_id&&void 0!==a.dotted_order){let e=this._cloneCurrentOTELContext();this.processRunOperation({action:"create",item:a,otelContext:e}).catch(console.error);return}let i=al(a),n=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs`,{method:"POST",headers:t,body:as(i,`Creating run with id: ${i.id}`),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(n,"create run",!0)}async batchIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r=await Promise.all(e?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[]),a=await Promise.all(t?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[]);if(r.length>0&&a.length>0){let e=r.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of a)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);r=Object.values(e),a=t}let i={post:r,patch:a};if(!i.post.length&&!i.patch.length)return;let n={post:[],patch:[]};for(let e of["post","patch"]){let t=i[e].reverse(),r=t.pop();for(;void 0!==r;)n[e].push(r),r=t.pop()}if(n.post.length>0||n.patch.length>0){let e=n.post.map(e=>e.id).concat(n.patch.map(e=>e.id)).join(",");await this._postBatchIngestRuns(as(n,`Ingesting runs with ids: ${e}`))}}async _postBatchIngestRuns(e){let t={...this.headers,"Content-Type":"application/json",Accept:"application/json"},r=await this.batchIngestCaller.call(rk(this.debug),`${this.apiUrl}/runs/batch`,{method:"POST",headers:t,body:e,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(r,"batch create run",!0)}async multipartIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r={},a=[];for(let t of e??[]){let e=await this.prepareRunCreateOrUpdateInputs(t);void 0!==e.id&&void 0!==e.attachments&&(r[e.id]=e.attachments),delete e.attachments,a.push(e)}let i=[];for(let e of t??[])i.push(await this.prepareRunCreateOrUpdateInputs(e));if(void 0!==a.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run');if(void 0!==i.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run');if(a.length>0&&i.length>0){let e=a.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of i)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);a=Object.values(e),i=t}if(0===a.length&&0===i.length)return;let n=[],s=[];for(let[e,t]of[["post",a],["patch",i]])for(let a of t){let{inputs:t,outputs:i,events:o,attachments:l,...u}=a,d={inputs:t,outputs:i,events:o},c=as(u,`Serializing for multipart ingestion of run with id: ${u.id}`);for(let[t,r]of(s.push({name:`${e}.${u.id}`,payload:new Blob([c],{type:`application/json; length=${c.length}`})}),Object.entries(d))){if(void 0===r)continue;let a=as(r,`Serializing ${t} for multipart ingestion of run with id: ${u.id}`);s.push({name:`${e}.${u.id}.${t}`,payload:new Blob([a],{type:`application/json; length=${a.length}`})})}if(void 0!==u.id){let e=r[u.id];if(e)for(let[t,a]of(delete r[u.id],Object.entries(e))){let e,r;if(Array.isArray(a)?[e,r]=a:(e=a.mimeType,r=a.data),t.includes(".")){console.warn(`Skipping attachment '${t}' for run ${u.id}: Invalid attachment name. Attachment names must not contain periods ('.'). Please rename the attachment and try again.`);continue}s.push({name:`attachment.${u.id}.${t}`,payload:new Blob([r],{type:`${e}; length=${r.byteLength}`})})}}n.push(`trace=${u.trace_id},id=${u.id}`)}await this._sendMultipartRequest(s,n.join("; "))}async _createNodeFetchBody(e,t){let r=[];for(let a of e)r.push(new Blob([`--${t}\r
`])),r.push(new Blob([`Content-Disposition: form-data; name="${a.name}"\r
`,`Content-Type: ${a.payload.type}\r
\r
`])),r.push(a.payload),r.push(new Blob(["\r\n"]));r.push(new Blob([`--${t}--\r
`]));let a=new Blob(r);return await a.arrayBuffer()}async _createMultipartStream(e,t){let r=new TextEncoder;return new ReadableStream({async start(a){let i=async e=>{"string"==typeof e?a.enqueue(r.encode(e)):a.enqueue(e)};for(let r of e){await i(`--${t}\r
`),await i(`Content-Disposition: form-data; name="${r.name}"\r
`),await i(`Content-Type: ${r.payload.type}\r
\r
`);let e=r.payload.stream().getReader();try{let t;for(;!(t=await e.read()).done;)a.enqueue(t.value)}finally{e.releaseLock()}await i("\r\n")}await i(`--${t}--\r
`),a.close()}})}async _sendMultipartRequest(e,t){try{let t="----LangSmithFormBoundary"+Math.random().toString(36).slice(2),r=await (rS()?this._createNodeFetchBody(e,t):this._createMultipartStream(e,t)),a=await this.batchIngestCaller.call(rk(this.debug),`${this.apiUrl}/runs/multipart`,{method:"POST",headers:{...this.headers,"Content-Type":`multipart/form-data; boundary=${t}`},body:r,duplex:"half",signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(a,"ingest multipart runs",!0)}catch(e){console.warn(`${e.message.trim()}

Context: ${t}`)}}async updateRun(e,t){r3(e),t.inputs&&(t.inputs=await this.processInputs(t.inputs)),t.outputs&&(t.outputs=await this.processOutputs(t.outputs));let r={...t,id:e};if(!this._filterForSampling([r],!0).length)return;if(this.autoBatchTracing&&void 0!==r.trace_id&&void 0!==r.dotted_order){let e=this._cloneCurrentOTELContext();return void 0!==t.end_time&&void 0===r.parent_run_id&&this.blockOnRootRunFinalization&&!this.manualFlushMode?void await this.processRunOperation({action:"update",item:r,otelContext:e}).catch(console.error):void this.processRunOperation({action:"update",item:r,otelContext:e}).catch(console.error)}let a={...this.headers,"Content-Type":"application/json"},i=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs/${e}`,{method:"PATCH",headers:a,body:as(t,`Serializing payload to update run with id: ${e}`),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(i,"update run",!0)}async readRun(e,{loadChildRuns:t}={loadChildRuns:!1}){r3(e);let r=await this._get(`/runs/${e}`);return t&&(r=await this._loadChildRuns(r)),r}async getRunUrl({runId:e,run:t,projectOpts:r}){if(void 0!==t){let e;e=t.session_id?t.session_id:r?.projectName?(await this.readProject({projectName:r?.projectName})).id:r?.projectId?r?.projectId:(await this.readProject({projectName:rU("PROJECT")||"default"})).id;let a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${e}/r/${t.id}?poll=true`}if(void 0!==e){let t=await this.readRun(e);if(!t.app_path)throw Error(`Run ${e} has no app_path`);let r=this.getHostUrl();return`${r}${t.app_path}`}throw Error("Must provide either runId or run")}async _loadChildRuns(e){let t=await ac(this.listRuns({isRoot:!1,projectId:e.session_id,traceId:e.trace_id})),r={},a={};for(let i of(t.sort((e,t)=>(e?.dotted_order??"").localeCompare(t?.dotted_order??"")),t)){if(null===i.parent_run_id||void 0===i.parent_run_id)throw Error(`Child run ${i.id} has no parent`);i.dotted_order?.startsWith(e.dotted_order??"")&&i.id!==e.id&&(i.parent_run_id in r||(r[i.parent_run_id]=[]),r[i.parent_run_id].push(i),a[i.id]=i)}for(let t in e.child_runs=r[e.id]||[],r)t!==e.id&&(a[t].child_runs=r[t]);return e}async *listRuns(e){let{projectId:t,projectName:r,parentRunId:a,traceId:i,referenceExampleId:n,startTime:s,executionOrder:o,isRoot:l,runType:u,error:d,id:c,query:h,filter:p,traceFilter:f,treeFilter:m,limit:g,select:y,order:_}=e,b=[];if(t&&(b=Array.isArray(t)?t:[t]),r){let e=Array.isArray(r)?r:[r],t=await Promise.all(e.map(e=>this.readProject({projectName:e}).then(e=>e.id)));b.push(...t)}let v={session:b.length?b:null,run_type:u,reference_example:n,query:h,filter:p,trace_filter:f,tree_filter:m,execution_order:o,parent_run:a,start_time:s?s.toISOString():null,error:d,id:c,limit:g,trace:i,select:y||["app_path","completion_cost","completion_tokens","dotted_order","end_time","error","events","extra","feedback_stats","first_token_time","id","inputs","name","outputs","parent_run_id","parent_run_ids","prompt_cost","prompt_tokens","reference_example_id","run_type","session_id","start_time","status","tags","total_cost","total_tokens","trace_id"],is_root:l,order:_},w=0;for await(let e of this._getCursorPaginatedList("/runs/query",v))if(g){if(w>=g)break;if(e.length+w>g){let t=e.slice(0,g-w);yield*t;break}w+=e.length,yield*e}else yield*e}async *listGroupRuns(e){let{projectId:t,projectName:r,groupBy:a,filter:i,startTime:n,endTime:s,limit:o,offset:l}=e,u={session_id:t||(await this.readProject({projectName:r})).id,group_by:a,filter:i,start_time:n?n.toISOString():null,end_time:s?s.toISOString():null,limit:Number(o)||100},d=Number(l)||0,c="/runs/group",h=`${this.apiUrl}${c}`;for(;;){let e=Object.fromEntries(Object.entries({...u,offset:d}).filter(([e,t])=>void 0!==t)),t=await this.caller.call(rk(),h,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(e),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(t,`Failed to fetch ${c}`);let{groups:r,total:a}=await t.json();if(0===r.length)break;for(let e of r)yield e;if((d+=r.length)>=a)break}}async getRunStats({id:e,trace:t,parentRun:r,runType:a,projectNames:i,projectIds:n,referenceExampleIds:s,startTime:o,endTime:l,error:u,query:d,filter:c,traceFilter:h,treeFilter:p,isRoot:f,dataSourceType:m}){let g=n||[];i&&(g=[...n||[],...await Promise.all(i.map(e=>this.readProject({projectName:e}).then(e=>e.id)))]);let y=Object.fromEntries(Object.entries({id:e,trace:t,parent_run:r,run_type:a,session:g,reference_example:s,start_time:o,end_time:l,error:u,query:d,filter:c,trace_filter:h,tree_filter:p,is_root:f,data_source_type:m}).filter(([e,t])=>void 0!==t)),_=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs/stats`,{method:"POST",headers:this.headers,body:JSON.stringify(y),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _.json()}async shareRun(e,{shareId:t}={}){let r={run_id:e,share_token:t||rm()};r3(e);let a=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await a.json();if(null===i||!("share_token"in i))throw Error("Invalid response from server");return`${this.getHostUrl()}/public/${i.share_token}/r`}async unshareRun(e){r3(e);let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(t,"unshare run",!0)}async readRunSharedLink(e){r3(e);let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(null!==r&&"share_token"in r)return`${this.getHostUrl()}/public/${r.share_token}/r`}async listSharedRuns(e,{runIds:t}={}){let r=new URLSearchParams({share_token:e});if(void 0!==t)for(let e of t)r.append("id",e);r3(e);let a=await this.caller.call(rk(this.debug),`${this.apiUrl}/public/${e}/runs${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await a.json()}async readDatasetSharedSchema(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id),r3(e);let r=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await r.json();return a.url=`${this.getHostUrl()}/public/${a.share_token}/d`,a}async shareDataset(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id);let r={dataset_id:e};r3(e);let a=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await a.json();return i.url=`${this.getHostUrl()}/public/${i.share_token}/d`,i}async unshareDataset(e){r3(e);let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(t,"unshare dataset",!0)}async readSharedDataset(e){r3(e);let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/public/${e}/datasets`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await t.json()}async listSharedExamples(e,t){let r={};t?.exampleIds&&(r.id=t.exampleIds);let a=new URLSearchParams;Object.entries(r).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>a.append(e,t)):a.append(e,t)});let i=await this.caller.call(rk(this.debug),`${this.apiUrl}/public/${e}/examples?${a.toString()}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await i.json();if(!i.ok){if("detail"in n)throw Error(`Failed to list shared examples.
Status: ${i.status}
Message: ${Array.isArray(n.detail)?n.detail.join("\n"):"Unspecified error"}`);throw Error(`Failed to list shared examples: ${i.status} ${i.statusText}`)}return n.map(e=>({...e,_hostUrl:this.getHostUrl()}))}async createProject({projectName:e,description:t=null,metadata:r=null,upsert:a=!1,projectExtra:i=null,referenceDatasetId:n=null}){let s=`${this.apiUrl}/sessions${a?"?upsert=true":""}`,o=i||{};r&&(o.metadata=r);let l={name:e,extra:o,description:t};null!==n&&(l.reference_dataset_id=n);let u=await this.caller.call(rk(this.debug),s,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(u,"create project"),await u.json()}async updateProject(e,{name:t=null,description:r=null,metadata:a=null,projectExtra:i=null,endTime:n=null}){let s=`${this.apiUrl}/sessions/${e}`,o=i;a&&(o={...o||{},metadata:a});let l={name:t,extra:o,description:r,end_time:n?new Date(n).toISOString():null},u=await this.caller.call(rk(this.debug),s,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(u,"update project"),await u.json()}async hasProject({projectId:e,projectName:t}){let r="/sessions",a=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)r3(e),r+=`/${e}`;else if(void 0!==t)a.append("name",t);else throw Error("Must provide projectName or projectId");let i=await this.caller.call(rk(this.debug),`${this.apiUrl}${r}?${a}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});try{let e=await i.json();if(!i.ok)return!1;if(Array.isArray(e))return e.length>0;return!0}catch(e){return!1}}async readProject({projectId:e,projectName:t,includeStats:r}){let a,i="/sessions",n=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)r3(e),i+=`/${e}`;else if(void 0!==t)n.append("name",t);else throw Error("Must provide projectName or projectId");void 0!==r&&n.append("include_stats",r.toString());let s=await this._get(i,n);if(Array.isArray(s)){if(0===s.length)throw Error(`Project[id=${e}, name=${t}] not found`);a=s[0]}else a=s;return a}async getProjectUrl({projectId:e,projectName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either projectName or projectId");let r=await this.readProject({projectId:e,projectName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${r.id}`}async getDatasetUrl({datasetId:e,datasetName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either datasetName or datasetId");let r=await this.readDataset({datasetId:e,datasetName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/datasets/${r.id}`}async _getTenantId(){if(null!==this._tenantId)return this._tenantId;let e=new URLSearchParams({limit:"1"});for await(let t of this._getPaginated("/sessions",e))return this._tenantId=t[0].tenant_id,t[0].tenant_id;throw Error("No projects found to resolve tenant.")}async *listProjects({projectIds:e,name:t,nameContains:r,referenceDatasetId:a,referenceDatasetName:i,referenceFree:n,metadata:s}={}){let o=new URLSearchParams;if(void 0!==e)for(let t of e)o.append("id",t);if(void 0!==t&&o.append("name",t),void 0!==r&&o.append("name_contains",r),void 0!==a)o.append("reference_dataset",a);else if(void 0!==i){let e=await this.readDataset({datasetName:i});o.append("reference_dataset",e.id)}for await(let e of(void 0!==n&&o.append("reference_free",n.toString()),void 0!==s&&o.append("metadata",JSON.stringify(s)),this._getPaginated("/sessions",o)))yield*e}async deleteProject({projectId:e,projectName:t}){let r;if(void 0===e&&void 0===t)throw Error("Must provide projectName or projectId");if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");r3(r=void 0===e?(await this.readProject({projectName:t})).id:e);let a=await this.caller.call(rk(this.debug),`${this.apiUrl}/sessions/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(a,`delete session ${r} (${t})`,!0)}async uploadCsv({csvFile:e,fileName:t,inputKeys:r,outputKeys:a,description:i,dataType:n,name:s}){let o=`${this.apiUrl}/datasets/upload`,l=new FormData;l.append("file",e,t),r.forEach(e=>{l.append("input_keys",e)}),a.forEach(e=>{l.append("output_keys",e)}),i&&l.append("description",i),n&&l.append("data_type",n),s&&l.append("name",s);let u=await this.caller.call(rk(this.debug),o,{method:"POST",headers:this.headers,body:l,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(u,"upload CSV"),await u.json()}async createDataset(e,{description:t,dataType:r,inputsSchema:a,outputsSchema:i,metadata:n}={}){let s={name:e,description:t,extra:n?{metadata:n}:void 0};r&&(s.data_type=r),a&&(s.inputs_schema_definition=a),i&&(s.outputs_schema_definition=i);let o=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(o,"create dataset"),await o.json()}async readDataset({datasetId:e,datasetName:t}){let r,a="/datasets",i=new URLSearchParams({limit:"1"});if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)r3(e),a+=`/${e}`;else if(void 0!==t)i.append("name",t);else throw Error("Must provide datasetName or datasetId");let n=await this._get(a,i);if(Array.isArray(n)){if(0===n.length)throw Error(`Dataset[id=${e}, name=${t}] not found`);r=n[0]}else r=n;return r}async hasDataset({datasetId:e,datasetName:t}){try{return await this.readDataset({datasetId:e,datasetName:t}),!0}catch(e){if(e instanceof Error&&e.message.toLocaleLowerCase().includes("not found"))return!1;throw e}}async diffDatasetVersions({datasetId:e,datasetName:t,fromVersion:r,toVersion:a}){let i=e;if(void 0===i&&void 0===t)throw Error("Must provide either datasetName or datasetId");if(void 0!==i&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");void 0===i&&(i=(await this.readDataset({datasetName:t})).id);let n=new URLSearchParams({from_version:"string"==typeof r?r:r.toISOString(),to_version:"string"==typeof a?a:a.toISOString()});return await this._get(`/datasets/${i}/versions/diff`,n)}async readDatasetOpenaiFinetuning({datasetId:e,datasetName:t}){if(void 0!==e);else if(void 0!==t)e=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide either datasetName or datasetId");let r=await this._getResponse(`/datasets/${e}/openai_ft`);return(await r.text()).trim().split("\n").map(e=>JSON.parse(e))}async *listDatasets({limit:e=100,offset:t=0,datasetIds:r,datasetName:a,datasetNameContains:i,metadata:n}={}){let s=new URLSearchParams({limit:e.toString(),offset:t.toString()});if(void 0!==r)for(let e of r)s.append("id",e);for await(let e of(void 0!==a&&s.append("name",a),void 0!==i&&s.append("name_contains",i),void 0!==n&&s.append("metadata",JSON.stringify(n)),this._getPaginated("/datasets",s)))yield*e}async updateDataset(e){let{datasetId:t,datasetName:r,...a}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let i=t??(await this.readDataset({datasetName:r})).id;r3(i);let n=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${i}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(n,"update dataset"),await n.json()}async updateDatasetTag(e){let{datasetId:t,datasetName:r,asOf:a,tag:i}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let n=t??(await this.readDataset({datasetName:r})).id;r3(n);let s=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${n}/tags`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({as_of:"string"==typeof a?a:a.toISOString(),tag:i}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(s,"update dataset tags")}async deleteDataset({datasetId:e,datasetName:t}){let r="/datasets",a=e;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==t&&(a=(await this.readDataset({datasetName:t})).id),void 0!==a)r3(a),r+=`/${a}`;else throw Error("Must provide datasetName or datasetId");let i=await this.caller.call(rk(this.debug),this.apiUrl+r,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(i,`delete ${r}`),await i.json()}async indexDataset({datasetId:e,datasetName:t,tag:r}){let a=e;if(a||t)if(a&&t)throw Error("Must provide either datasetName or datasetId, not both");else a||(a=(await this.readDataset({datasetName:t})).id);else throw Error("Must provide either datasetName or datasetId");r3(a);let i=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${a}/index`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({tag:r}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(i,"index dataset"),await i.json()}async similarExamples(e,t,r,{filter:a}={}){let i={limit:r,inputs:e};void 0!==a&&(i.filter=a),r3(t);let n=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${t}/search`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(n,"fetch similar examples"),(await n.json()).examples}async createExample(e,t,r){let a;if(ay(e)&&(void 0!==t||void 0!==r))throw Error("Cannot provide outputs or options when using ExampleCreate object");let i=t?r?.datasetId:e.dataset_id,n=t?r?.datasetName:e.dataset_name;if(void 0===i&&void 0===n)throw Error("Must provide either datasetName or datasetId");if(void 0!==i&&void 0!==n)throw Error("Must provide either datasetName or datasetId, not both");void 0===i&&(i=(await this.readDataset({datasetName:n})).id);let s=(t?r?.createdAt:e.created_at)||new Date;a=ay(e)?e:{inputs:e,outputs:t,created_at:s?.toISOString(),id:r?.exampleId,metadata:r?.metadata,split:r?.split,source_run_id:r?.sourceRunId,use_source_run_io:r?.useSourceRunIO,use_source_run_attachments:r?.useSourceRunAttachments,attachments:r?.attachments};let o=await this._uploadExamplesMultipart(i,[a]);return await this.readExample(o.example_ids?.[0]??rm())}async createExamples(e){if(Array.isArray(e)){if(0===e.length)return[];let t=e[0].dataset_id,r=e[0].dataset_name;if(void 0===t&&void 0===r)throw Error("Must provide either datasetName or datasetId");if(void 0!==t&&void 0!==r)throw Error("Must provide either datasetName or datasetId, not both");void 0===t&&(t=(await this.readDataset({datasetName:r})).id);let a=await this._uploadExamplesMultipart(t,e);return await Promise.all(a.example_ids.map(e=>this.readExample(e)))}let{inputs:t,outputs:r,metadata:a,splits:i,sourceRunIds:n,useSourceRunIOs:s,useSourceRunAttachments:o,attachments:l,exampleIds:u,datasetId:d,datasetName:c}=e;if(void 0===t)throw Error("Must provide inputs when using legacy parameters");let h=d;if(void 0===h&&void 0===c)throw Error("Must provide either datasetName or datasetId");if(void 0!==h&&void 0!==c)throw Error("Must provide either datasetName or datasetId, not both");void 0===h&&(h=(await this.readDataset({datasetName:c})).id);let p=t.map((e,t)=>({dataset_id:h,inputs:e,outputs:r?.[t],metadata:a?.[t],split:i?.[t],id:u?.[t],attachments:l?.[t],source_run_id:n?.[t],use_source_run_io:s?.[t],use_source_run_attachments:o?.[t]})),f=await this._uploadExamplesMultipart(h,p);return await Promise.all(f.example_ids.map(e=>this.readExample(e)))}async createLLMExample(e,t,r){return this.createExample({input:e},{output:t},r)}async createChatExample(e,t,r){let a=e.map(e=>r2(e)?r4(e):e),i=r2(t)?r4(t):t;return this.createExample({input:a},{output:i},r)}async readExample(e){r3(e);let t=`/examples/${e}`,{attachment_urls:r,...a}=await this._get(t);return r&&(a.attachments=Object.entries(r).reduce((e,[t,r])=>(e[t.slice(11)]={presigned_url:r.presigned_url,mime_type:r.mime_type},e),{})),a}async *listExamples({datasetId:e,datasetName:t,exampleIds:r,asOf:a,splits:i,inlineS3Urls:n,metadata:s,limit:o,offset:l,filter:u,includeAttachments:d}={}){let c;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)c=e;else if(void 0!==t)c=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide a datasetName or datasetId");let h=new URLSearchParams({dataset:c}),p=a?"string"==typeof a?a:a?.toISOString():void 0;if(p&&h.append("as_of",p),h.append("inline_s3_urls",(n??!0).toString()),void 0!==r)for(let e of r)h.append("id",e);if(void 0!==i)for(let e of i)h.append("splits",e);if(void 0!==s){let e=JSON.stringify(s);h.append("metadata",e)}void 0!==o&&h.append("limit",o.toString()),void 0!==l&&h.append("offset",l.toString()),void 0!==u&&h.append("filter",u),!0===d&&["attachment_urls","outputs","metadata"].forEach(e=>h.append("select",e));let f=0;for await(let e of this._getPaginated("/examples",h)){for(let t of e){let{attachment_urls:e,...r}=t;e&&(r.attachments=Object.entries(e).reduce((e,[t,r])=>(e[t.slice(11)]={presigned_url:r.presigned_url,mime_type:r.mime_type||void 0},e),{})),yield r,f++}if(void 0!==o&&f>=o)break}}async deleteExample(e){r3(e);let t=`/examples/${e}`,r=await this.caller.call(rk(this.debug),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(r,`delete ${t}`),await r.json()}async updateExample(e,t){let r,a,i;return r3(r=t?e:e.id),i=void 0!==(a=t?{id:r,...t}:e).dataset_id?a.dataset_id:(await this.readExample(r)).dataset_id,this._updateExamplesMultipart(i,[a])}async updateExamples(e){let t;return t=void 0===e[0].dataset_id?(await this.readExample(e[0].id)).dataset_id:e[0].dataset_id,this._updateExamplesMultipart(t,e)}async readDatasetVersion({datasetId:e,datasetName:t,asOf:r,tag:a}){let i;if(r3(i=e||(await this.readDataset({datasetName:t})).id),r&&a||!r&&!a)throw Error("Exactly one of asOf and tag must be specified.");let n=new URLSearchParams;void 0!==r&&n.append("as_of","string"==typeof r?r:r.toISOString()),void 0!==a&&n.append("tag",a);let s=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${i}/version?${n.toString()}`,{method:"GET",headers:{...this.headers},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(s,"read dataset version"),await s.json()}async listDatasetSplits({datasetId:e,datasetName:t,asOf:r}){let a;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");r3(a=void 0===e?(await this.readDataset({datasetName:t})).id:e);let i=new URLSearchParams,n=r?"string"==typeof r?r:r?.toISOString():void 0;return n&&i.append("as_of",n),await this._get(`/datasets/${a}/splits`,i)}async updateDatasetSplits({datasetId:e,datasetName:t,splitName:r,exampleIds:a,remove:i=!1}){let n;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");r3(n=void 0===e?(await this.readDataset({datasetName:t})).id:e);let s={split_name:r,examples:a.map(e=>(r3(e),e)),remove:i},o=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/${n}/splits`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(o,"update dataset splits",!0)}async evaluateRun(e,t,{sourceInfo:r,loadChildRuns:a,referenceExample:i}={loadChildRuns:!1}){let n;if(r6("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead."),"string"==typeof e)n=await this.readRun(e,{loadChildRuns:a});else if("object"==typeof e&&"id"in e)n=e;else throw Error(`Invalid run type: ${typeof e}`);null!==n.reference_example_id&&void 0!==n.reference_example_id&&(i=await this.readExample(n.reference_example_id));let s=await t.evaluateRun(n,i),[o,l]=await this._logEvaluationFeedback(s,n,r);return l[0]}async createFeedback(e,t,{score:r,value:a,correction:i,comment:n,sourceInfo:s,feedbackSourceType:o="api",sourceRunId:l,feedbackId:u,feedbackConfig:d,projectId:c,comparativeExperimentId:h}){if(!e&&!c)throw Error("One of runId or projectId must be provided");if(e&&c)throw Error("Only one of runId or projectId can be provided");let p={type:o??"api",metadata:s??{}};void 0===l||p?.metadata===void 0||p.metadata.__run||(p.metadata.__run={run_id:l}),p?.metadata!==void 0&&p.metadata.__run?.run_id!==void 0&&r3(p.metadata.__run.run_id);let f={id:u??rm(),run_id:e,key:t,score:af(r),value:a,correction:i,comment:n,feedback_source:p,comparative_experiment_id:h,feedbackConfig:d,session_id:c},m=`${this.apiUrl}/feedback`,g=await this.caller.call(rk(this.debug),m,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(f),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(g,"create feedback",!0),f}async updateFeedback(e,{score:t,value:r,correction:a,comment:i}){let n={};null!=t&&(n.score=af(t)),null!=r&&(n.value=r),null!=a&&(n.correction=a),null!=i&&(n.comment=i),r3(e);let s=await this.caller.call(rk(this.debug),`${this.apiUrl}/feedback/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(n),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(s,"update feedback",!0)}async readFeedback(e){r3(e);let t=`/feedback/${e}`;return await this._get(t)}async deleteFeedback(e){r3(e);let t=`/feedback/${e}`,r=await this.caller.call(rk(this.debug),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(r,`delete ${t}`),await r.json()}async *listFeedback({runIds:e,feedbackKeys:t,feedbackSourceTypes:r}={}){let a=new URLSearchParams;if(e&&a.append("run",e.join(",")),t)for(let e of t)a.append("key",e);if(r)for(let e of r)a.append("source",e);for await(let e of this._getPaginated("/feedback",a))yield*e}async createPresignedFeedbackToken(e,t,{expiration:r,feedbackConfig:a}={}){let i={run_id:e,feedback_key:t,feedback_config:a};r?"string"==typeof r?i.expires_at=r:(r?.hours||r?.minutes||r?.days)&&(i.expires_in=r):i.expires_in={hours:3};let n=await this.caller.call(rk(this.debug),`${this.apiUrl}/feedback/tokens`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await n.json()}async createComparativeExperiment({name:e,experimentIds:t,referenceDatasetId:r,createdAt:a,description:i,metadata:n,id:s}){if(0===t.length)throw Error("At least one experiment is required");if(r||(r=(await this.readProject({projectId:t[0]})).reference_dataset_id),null==!r)throw Error("A reference dataset is required");let o={id:s,name:e,experiment_ids:t,reference_dataset_id:r,description:i,created_at:(a??new Date)?.toISOString(),extra:{}};n&&(o.extra.metadata=n);let l=await this.caller.call(rk(this.debug),`${this.apiUrl}/datasets/comparative`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await l.json()}async *listPresignedFeedbackTokens(e){r3(e);let t=new URLSearchParams({run_id:e});for await(let e of this._getPaginated("/feedback/tokens",t))yield*e}_selectEvalResults(e){let t;return"results"in e?e.results:Array.isArray(e)?e:[e]}async _logEvaluationFeedback(e,t,r){let a=this._selectEvalResults(e),i=[];for(let e of a){let a=r||{};e.evaluatorInfo&&(a={...e.evaluatorInfo,...a});let n=null;e.targetRunId?n=e.targetRunId:t&&(n=t.id),i.push(await this.createFeedback(n,e.key,{score:e.score,value:e.value,comment:e.comment,correction:e.correction,sourceInfo:a,sourceRunId:e.sourceRunId,feedbackConfig:e.feedbackConfig,feedbackSourceType:"model"}))}return[a,i]}async logEvaluationFeedback(e,t,r){let[a]=await this._logEvaluationFeedback(e,t,r);return a}async *listAnnotationQueues(e={}){let{queueIds:t,name:r,nameContains:a,limit:i}=e,n=new URLSearchParams;t&&t.forEach((e,t)=>{r3(e,`queueIds[${t}]`),n.append("ids",e)}),r&&n.append("name",r),a&&n.append("name_contains",a),n.append("limit",(void 0!==i?Math.min(i,100):100).toString());let s=0;for await(let e of this._getPaginated("/annotation-queues",n))if(yield*e,s++,void 0!==i&&s>=i)break}async createAnnotationQueue(e){let{name:t,description:r,queueId:a,rubricInstructions:i}=e,n={name:t,description:r,id:a||rm(),rubric_instructions:i},s=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(Object.entries(n).filter(([e,t])=>void 0!==t))),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(s,"create annotation queue"),await s.json()}async readAnnotationQueue(e){let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(t,"read annotation queue"),await t.json()}async updateAnnotationQueue(e,t){let{name:r,description:a,rubricInstructions:i}=t,n=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({name:r,description:a,rubric_instructions:i}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(n,"update annotation queue")}async deleteAnnotationQueue(e){let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(t,"delete annotation queue")}async addRunsToAnnotationQueue(e,t){let r=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}/runs`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t.map((e,t)=>r3(e,`runIds[${t}]`).toString())),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(r,"add runs to annotation queue")}async getRunFromAnnotationQueue(e,t){let r=`/annotation-queues/${r3(e,"queueId")}/run`,a=await this.caller.call(rk(this.debug),`${this.apiUrl}${r}/${t}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(a,"get run from annotation queue"),await a.json()}async deleteRunFromAnnotationQueue(e,t){let r=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}/runs/${r3(t,"queueRunId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(r,"delete run from annotation queue")}async getSizeFromAnnotationQueue(e){let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/annotation-queues/${r3(e,"queueId")}/size`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(t,"get size from annotation queue"),await t.json()}async _currentTenantIsOwner(e){let t=await this._getSettings();return"-"==e||t.tenant_handle===e}async _ownerConflictError(e,t){let r=await this._getSettings();return Error(`Cannot ${e} for another tenant.

      Current tenant: ${r.tenant_handle}

      Requested tenant: ${t}`)}async _getLatestCommitHash(e){let t=await this.caller.call(rk(this.debug),`${this.apiUrl}/commits/${e}/?limit=1&offset=0`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(!t.ok){let e="string"==typeof r.detail?r.detail:JSON.stringify(r.detail),a=Error(`Error ${t.status}: ${t.statusText}
${e}`);throw a.statusCode=t.status,a}if(0!==r.commits.length)return r.commits[0].commit_hash}async _likeOrUnlikePrompt(e,t){let[r,a,i]=r8(e),n=await this.caller.call(rk(this.debug),`${this.apiUrl}/likes/${r}/${a}`,{method:"POST",body:JSON.stringify({like:t}),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(n,`${t?"like":"unlike"} prompt`),await n.json()}async _getPromptUrl(e){let[t,r,a]=r8(e);if(await this._currentTenantIsOwner(t)){let e=await this._getSettings();return"latest"!==a?`${this.getHostUrl()}/prompts/${r}/${a.substring(0,8)}?organizationId=${e.id}`:`${this.getHostUrl()}/prompts/${r}?organizationId=${e.id}`}return"latest"!==a?`${this.getHostUrl()}/hub/${t}/${r}/${a.substring(0,8)}`:`${this.getHostUrl()}/hub/${t}/${r}`}async promptExists(e){return!!await this.getPrompt(e)}async likePrompt(e){return this._likeOrUnlikePrompt(e,!0)}async unlikePrompt(e){return this._likeOrUnlikePrompt(e,!1)}async *listCommits(e){for await(let t of this._getPaginated(`/commits/${e}/`,new URLSearchParams,e=>e.commits))yield*t}async *listPrompts(e){let t=new URLSearchParams;for await(let r of(t.append("sort_field",e?.sortField??"updated_at"),t.append("sort_direction","desc"),t.append("is_archived",(!!e?.isArchived).toString()),e?.isPublic!==void 0&&t.append("is_public",e.isPublic.toString()),e?.query&&t.append("query",e.query),this._getPaginated("/repos",t,e=>e.repos)))yield*r}async getPrompt(e){let[t,r,a]=r8(e),i=await this.caller.call(rk(this.debug),`${this.apiUrl}/repos/${t}/${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});if(404===i.status)return null;await ae(i,"get prompt");let n=await i.json();return n.repo?n.repo:null}async createPrompt(e,t){let r=await this._getSettings();if(t?.isPublic&&!r.tenant_handle)throw Error(`Cannot create a public prompt without first

        creating a LangChain Hub handle. 
        You can add a handle by creating a public prompt at:

        https://smith.langchain.com/prompts`);let[a,i,n]=r8(e);if(!await this._currentTenantIsOwner(a))throw await this._ownerConflictError("create a prompt",a);let s={repo_handle:i,...t?.description&&{description:t.description},...t?.readme&&{readme:t.readme},...t?.tags&&{tags:t.tags},is_public:!!t?.isPublic},o=await this.caller.call(rk(this.debug),`${this.apiUrl}/repos/`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(o,"create prompt");let{repo:l}=await o.json();return l}async createCommit(e,t,r){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[a,i,n]=r8(e),s=r?.parentCommitHash!=="latest"&&r?.parentCommitHash?r?.parentCommitHash:await this._getLatestCommitHash(`${a}/${i}`),o={manifest:JSON.parse(JSON.stringify(t)),parent_commit:s},l=await this.caller.call(rk(this.debug),`${this.apiUrl}/commits/${a}/${i}`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(l,"create commit");let u=await l.json();return this._getPromptUrl(`${a}/${i}${u.commit_hash?`:${u.commit_hash}`:""}`)}async updateExamplesMultipart(e,t=[]){return this._updateExamplesMultipart(e,t)}async _updateExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");let r=new FormData;for(let e of t){let t=e.id,a=new Blob([as({...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split}},`Serializing body for example with id: ${t}`)],{type:"application/json"});if(r.append(t,a),e.inputs){let a=new Blob([as(e.inputs,`Serializing inputs for example with id: ${t}`)],{type:"application/json"});r.append(`${t}.inputs`,a)}if(e.outputs){let a=new Blob([as(e.outputs,`Serializing outputs whle updating example with id: ${t}`)],{type:"application/json"});r.append(`${t}.outputs`,a)}if(e.attachments)for(let[a,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});r.append(`${t}.attachment.${a}`,s)}if(e.attachments_operations){let a=new Blob([as(e.attachments_operations,`Serializing attachments while updating example with id: ${t}`)],{type:"application/json"});r.append(`${t}.attachments_operations`,a)}}let a=e??t[0]?.dataset_id,i=await this.caller.call(rk(this.debug),`${this.apiUrl}/v1/platform/datasets/${a}/examples`,{method:"PATCH",headers:this.headers,body:r});return await i.json()}async uploadExamplesMultipart(e,t=[]){return this._uploadExamplesMultipart(e,t)}async _uploadExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");let r=new FormData;for(let e of t){let t=(e.id??rm()).toString(),a=new Blob([as({created_at:e.created_at,...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split},...e.source_run_id&&{source_run_id:e.source_run_id},...e.use_source_run_io&&{use_source_run_io:e.use_source_run_io},...e.use_source_run_attachments&&{use_source_run_attachments:e.use_source_run_attachments}},`Serializing body for uploaded example with id: ${t}`)],{type:"application/json"});if(r.append(t,a),e.inputs){let a=new Blob([as(e.inputs,`Serializing inputs for uploaded example with id: ${t}`)],{type:"application/json"});r.append(`${t}.inputs`,a)}if(e.outputs){let a=new Blob([as(e.outputs,`Serializing outputs for uploaded example with id: ${t}`)],{type:"application/json"});r.append(`${t}.outputs`,a)}if(e.attachments)for(let[a,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});r.append(`${t}.attachment.${a}`,s)}}let a=await this.caller.call(rk(this.debug),`${this.apiUrl}/v1/platform/datasets/${e}/examples`,{method:"POST",headers:this.headers,body:r});return await ae(a,"upload examples"),await a.json()}async updatePrompt(e,t){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[r,a]=r8(e);if(!await this._currentTenantIsOwner(r))throw await this._ownerConflictError("update a prompt",r);let i={};if(t?.description!==void 0&&(i.description=t.description),t?.readme!==void 0&&(i.readme=t.readme),t?.tags!==void 0&&(i.tags=t.tags),t?.isPublic!==void 0&&(i.is_public=t.isPublic),t?.isArchived!==void 0&&(i.is_archived=t.isArchived),0===Object.keys(i).length)throw Error("No valid update options provided");let n=await this.caller.call(rk(this.debug),`${this.apiUrl}/repos/${r}/${a}`,{method:"PATCH",body:JSON.stringify(i),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await ae(n,"update prompt"),n.json()}async deletePrompt(e){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[t,r,a]=r8(e);if(!await this._currentTenantIsOwner(t))throw await this._ownerConflictError("delete a prompt",t);let i=await this.caller.call(rk(this.debug),`${this.apiUrl}/repos/${t}/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await i.json()}async pullPromptCommit(e,t){let[r,a,i]=r8(e),n=await this.caller.call(rk(this.debug),`${this.apiUrl}/commits/${r}/${a}/${i}${t?.includeModel?"?include_model=true":""}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await ae(n,"pull prompt commit");let s=await n.json();return{owner:r,repo:a,commit_hash:s.commit_hash,manifest:s.manifest,examples:s.examples}}async _pullPrompt(e,t){return JSON.stringify((await this.pullPromptCommit(e,{includeModel:t?.includeModel})).manifest)}async pushPrompt(e,t){return(await this.promptExists(e)?t&&Object.keys(t).some(e=>"object"!==e)&&await this.updatePrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}):await this.createPrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}),t?.object)?await this.createCommit(e,t?.object,{parentCommitHash:t?.parentCommitHash}):await this._getPromptUrl(e)}async clonePublicDataset(e,t={}){let{sourceApiUrl:r=this.apiUrl,datasetName:a}=t,[i,n]=this.parseTokenOrUrl(e,r),s=new ag({apiUrl:i,apiKey:"placeholder"}),o=await s.readSharedDataset(n),l=a||o.name;try{if(await this.hasDataset({datasetId:l}))return void console.log(`Dataset ${l} already exists in your tenant. Skipping.`)}catch(e){}let u=await s.listSharedExamples(n),d=await this.createDataset(l,{description:o.description,dataType:o.data_type||"kv",inputsSchema:o.inputs_schema_definition??void 0,outputsSchema:o.outputs_schema_definition??void 0});try{await this.createExamples({inputs:u.map(e=>e.inputs),outputs:u.flatMap(e=>e.outputs?[e.outputs]:[]),datasetId:d.id})}catch(e){throw console.error(`An error occurred while creating dataset ${l}. You should delete it manually.`),e}}parseTokenOrUrl(e,t,r=2,a="dataset"){try{return r3(e),[t,e]}catch(e){}try{let i=new URL(e).pathname.split("/").filter(e=>""!==e);if(i.length>=r){let e=i[i.length-r];return[t,e]}throw Error(`Invalid public ${a} URL: ${e}`)}catch(t){throw Error(`Invalid public ${a} URL or token: ${e}`)}}async awaitPendingTraceBatches(){if(this.manualFlushMode)return console.warn("[WARNING]: When tracing in manual flush mode, you must call `await client.flush()` manually to submit trace batches."),Promise.resolve();await Promise.all([...this.autoBatchQueue.items.map(({itemPromise:e})=>e),this.batchIngestCaller.queue.onIdle()]),void 0!==this.langSmithToOTELTranslator&&await rW.getDefaultOTLPTracerComponents()?.DEFAULT_LANGSMITH_SPAN_PROCESSOR?.forceFlush()}}function ay(e){return"dataset_id"in e||"dataset_name"in e}let a_=e=>void 0!==e?e:!!["TRACING_V2","TRACING"].find(e=>"true"===rU(e)),ab=Symbol.for("lc:context_variables");class av{constructor(e,t,r,a){Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.metadata=e,this.tags=t,this.project_name=r,this.replicas=a}static fromHeader(e){let t,r,a=e.split(","),i={},n=[];for(let e of a){let[a,s]=e.split("="),o=decodeURIComponent(s);"langsmith-metadata"===a?i=JSON.parse(o):"langsmith-tags"===a?n=o.split(","):"langsmith-project"===a?t=o:"langsmith-replicas"===a&&(r=JSON.parse(o))}return new av(i,n,t,r)}toHeader(){let e=[];return this.metadata&&Object.keys(this.metadata).length>0&&e.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`),this.tags&&this.tags.length>0&&e.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`),this.project_name&&e.push(`langsmith-project=${encodeURIComponent(this.project_name)}`),e.join(",")}}class aw{constructor(e){if(Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"run_type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"parent_run",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_runs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"start_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"end_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"extra",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"error",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"serialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reference_example_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"dotted_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingEnabled",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"attachments",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),aE(e))return void Object.assign(this,{...e});let t=aw.getDefaultConfig(),{metadata:r,...a}=e,i=a.client??aw.getSharedClient(),n={...r,...a?.extra?.metadata};if(a.extra={...a.extra,metadata:n},Object.assign(this,{...t,...a,client:i}),this.trace_id||(this.parent_run?this.trace_id=this.parent_run.trace_id??this.id:this.trace_id=this.id),this.execution_order??=1,this.child_execution_order??=1,!this.dotted_order){let e=function(e,t,r=1){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(this.start_time,this.id,this.execution_order);this.parent_run?this.dotted_order=this.parent_run.dotted_order+"."+e:this.dotted_order=e}}set metadata(e){this.extra={...this.extra,metadata:{...this.extra?.metadata,...e}}}get metadata(){return this.extra?.metadata}static getDefaultConfig(){return{id:rm(),run_type:"chain",project_name:rA(),child_runs:[],api_url:rM("LANGCHAIN_ENDPOINT")??"http://localhost:1984",api_key:rM("LANGCHAIN_API_KEY"),caller_options:{},start_time:Date.now(),serialized:{},inputs:{},extra:{}}}static getSharedClient(){return aw.sharedClient||(aw.sharedClient=new ag),aw.sharedClient}createChild(e){var t,r;let a=this.child_execution_order+1,i=new aw({...e,parent_run:this,project_name:this.project_name,replicas:this.replicas,client:this.client,tracingEnabled:this.tracingEnabled,execution_order:a,child_execution_order:a});ab in this&&(i[ab]=this[ab]);let n=Symbol.for("lc:child_config"),s=e.extra?.[n]??this.extra[n];if(void 0!==(t=s)&&"object"==typeof t.callbacks&&(ax(t.callbacks?.handlers)||ax(t.callbacks))){let e={...s},t="object"==typeof(r=e.callbacks)&&null!=r&&Array.isArray(r.handlers)?e.callbacks.copy?.():void 0;t&&(Object.assign(t,{_parentRunId:i.id}),t.handlers?.find(aO)?.updateFromRunTree?.(i),e.callbacks=t),i.extra[n]=e}let o=new Set,l=this;for(;null!=l&&!o.has(l.id);)o.add(l.id),l.child_execution_order=Math.max(l.child_execution_order,a),l=l.parent_run;return this.child_runs.push(i),i}async end(e,t,r=Date.now(),a){this.outputs=this.outputs??e,this.error=this.error??t,this.end_time=this.end_time??r,a&&Object.keys(a).length>0&&(this.extra=this.extra?{...this.extra,metadata:{...this.extra.metadata,...a}}:{metadata:a})}_convertToCreate(e,t,r=!0){let a,i,n=e.extra??{};if(n?.runtime?.library===void 0&&(n.runtime||(n.runtime={}),t))for(let[e,r]of Object.entries(t))n.runtime[e]||(n.runtime[e]=r);return r?(i=e.parent_run?.id,a=[]):(a=e.child_runs.map(e=>this._convertToCreate(e,t,r)),i=void 0),{id:e.id,name:e.name,start_time:e.start_time,end_time:e.end_time,run_type:e.run_type,reference_example_id:e.reference_example_id,extra:n,serialized:e.serialized,error:e.error,inputs:e.inputs,outputs:e.outputs,session_name:e.project_name,child_runs:a,parent_run_id:i,trace_id:e.trace_id,dotted_order:e.dotted_order,tags:e.tags,attachments:e.attachments,events:e.events}}_remapForProject(e,t,r=!0){let a,i=this._convertToCreate(this,t,r);if(e===this.project_name)return i;let n=t=>r_(`${t}:${e}`,r_.DNS),s=n(i.id),o=i.trace_id?n(i.trace_id):void 0,l=i.parent_run_id?n(i.parent_run_id):void 0;if(i.dotted_order){let e=i.dotted_order.split(".").map(e=>{let t=e.slice(0,-36),r=e.slice(-36),a=parseInt(t.slice(0,4)),i=parseInt(t.slice(4,6))-1,n=parseInt(t.slice(6,8)),s=parseInt(t.slice(9,11)),o=parseInt(t.slice(11,13));return[new Date(a,i,n,s,o,parseInt(t.slice(13,15)),parseInt(t.slice(15,21))/1e3),r]}),t=[];for(let r=0;r<e.length-1;r++){let[a,i]=e[r],s=n(i);t.push(a.toISOString().replace(/[-:]/g,"").replace(".","")+s)}let[r]=e[e.length-1];t.push(r.toISOString().replace(/[-:]/g,"").replace(".","")+s),a=t.join(".")}else a=void 0;return{...i,id:s,trace_id:o,parent_run_id:l,dotted_order:a,session_name:e}}async postRun(e=!0){try{let t=rL();if(this.replicas&&this.replicas.length>0)for(let[e]of this.replicas){let r=this._remapForProject(e,t,!0);await this.client.createRun(r)}else{let r=this._convertToCreate(this,t,e);await this.client.createRun(r)}if(!e)for(let e of(r6("Posting with excludeChildRuns=false is deprecated and will be removed in a future version."),this.child_runs))await e.postRun(!1)}catch(e){console.error(`Error in postRun for run ${this.id}:`,e)}}async patchRun(){if(this.replicas&&this.replicas.length>0)for(let[e,t]of this.replicas){let r=this._remapForProject(e);await this.client.updateRun(r.id,{inputs:r.inputs,outputs:r.outputs,error:r.error,parent_run_id:r.parent_run_id,session_name:r.session_name,reference_example_id:r.reference_example_id,end_time:r.end_time,dotted_order:r.dotted_order,trace_id:r.trace_id,events:r.events,tags:r.tags,extra:r.extra,attachments:this.attachments,...t})}else try{let e={end_time:this.end_time,error:this.error,inputs:this.inputs,outputs:this.outputs,parent_run_id:this.parent_run?.id,reference_example_id:this.reference_example_id,extra:this.extra,events:this.events,dotted_order:this.dotted_order,trace_id:this.trace_id,tags:this.tags,attachments:this.attachments,session_name:this.project_name};await this.client.updateRun(this.id,e)}catch(e){console.error(`Error in patchRun for run ${this.id}`,e)}}toJSON(){return this._convertToCreate(this,void 0,!1)}addEvent(e){this.events||(this.events=[]),"string"==typeof e?this.events.push({name:"event",time:new Date().toISOString(),message:e}):this.events.push({...e,time:e.time??new Date().toISOString()})}static fromRunnableConfig(e,t){let r,a,i,n=e?.callbacks,s=a_();if(n){let e=n?.getParentRunId?.()??"",t=n?.handlers?.find(e=>e?.name=="langchain_tracer");r=t?.getRun?.(e),a=t?.projectName,i=t?.client,s=s||!!t}return r?new aw({name:r.name,id:r.id,trace_id:r.trace_id,dotted_order:r.dotted_order,client:i,tracingEnabled:s,project_name:a,tags:[...new Set((r?.tags??[]).concat(e?.tags??[]))],extra:{metadata:{...r?.extra?.metadata,...e?.metadata}}}).createChild(t):new aw({...t,client:i,tracingEnabled:s,project_name:a})}static fromDottedOrder(e){return this.fromHeaders({"langsmith-trace":e})}static fromHeaders(e,t){let r="get"in e&&"function"==typeof e.get?{"langsmith-trace":e.get("langsmith-trace"),baggage:e.get("baggage")}:e,a=r["langsmith-trace"];if(!a||"string"!=typeof a)return;let i=a.trim(),n=i.split(".").map(e=>{let[t,r]=e.split("Z");return{strTime:t,time:Date.parse(t+"Z"),uuid:r}}),s=n[0].uuid,o={...t,name:t?.name??"parent",run_type:t?.run_type??"chain",start_time:t?.start_time??Date.now(),id:n.at(-1)?.uuid,trace_id:s,dotted_order:i};if(r.baggage&&"string"==typeof r.baggage){let e=av.fromHeader(r.baggage);o.metadata=e.metadata,o.tags=e.tags,o.project_name=e.project_name,o.replicas=e.replicas}return new aw(o)}toHeaders(e){let t={"langsmith-trace":this.dotted_order,baggage:new av(this.extra?.metadata,this.tags,this.project_name,this.replicas).toHeader()};if(e)for(let[r,a]of Object.entries(t))e.set(r,a);return t}}function aE(e){return void 0!==e&&"function"==typeof e.createChild&&"function"==typeof e.postRun}function aO(e){return"object"==typeof e&&null!=e&&"string"==typeof e.name&&"langchain_tracer"===e.name}function ax(e){return Array.isArray(e)&&e.some(e=>aO(e))}Object.defineProperty(aw,"sharedClient",{enumerable:!0,configurable:!0,writable:!0,value:null});class aT{getStore(){}run(e,t){return t()}}let aS=Symbol.for("ls:tracing_async_local_storage"),ak=new aT;class aA{getInstance(){return globalThis[aS]??ak}initializeGlobalInstance(e){void 0===globalThis[aS]&&(globalThis[aS]=e)}}let aI=new aA;function aC(e){return"function"==typeof e&&"langsmith:traceable"in e}Symbol.for("langsmith:traceable:root");let aP=Object.prototype.hasOwnProperty;function aj(e,t){return aP.call(e,t)}function aR(e){if(Array.isArray(e)){let t=Array(e.length);for(let e=0;e<t.length;e++)t[e]=""+e;return t}if(Object.keys)return Object.keys(e);let t=[];for(let r in e)aj(e,r)&&t.push(r);return t}function a$(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function aN(e){let t,r=0,a=e.length;for(;r<a;){if((t=e.charCodeAt(r))>=48&&t<=57){r++;continue}return!1}return!0}function aL(e){return -1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function aM(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function aU(e,t){let r=[e];for(let e in t){let a="object"==typeof t[e]?JSON.stringify(t[e],null,2):t[e];void 0!==a&&r.push(`${e}: ${a}`)}return r.join("\n")}class aD extends Error{constructor(e,t,r,a,i){super(aU(e,{name:t,index:r,operation:a,tree:i})),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"index",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"operation",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"tree",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.setPrototypeOf(this,new.target.prototype),this.message=aU(e,{name:t,index:r,operation:a,tree:i})}}let aF=aD,az=a$,aB={add:function(e,t,r){return e[t]=this.value,{newDocument:r}},remove:function(e,t,r){var a=e[t];return delete e[t],{newDocument:r,removed:a}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:function(e,t,r){let a=aG(r,this.path);a&&(a=a$(a));let i=aH(r,{op:"remove",path:this.from}).removed;return aH(r,{op:"add",path:this.path,value:i}),{newDocument:r,removed:a}},copy:function(e,t,r){let a=aG(r,this.from);return aH(r,{op:"add",path:this.path,value:a$(a)}),{newDocument:r}},test:function(e,t,r){return{newDocument:r,test:aK(e[t],this.value)}},_get:function(e,t,r){return this.value=e[t],{newDocument:r}}};var aZ={add:function(e,t,r){return aN(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:r,index:t}},remove:function(e,t,r){return{newDocument:r,removed:e.splice(t,1)[0]}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:aB.move,copy:aB.copy,test:aB.test,_get:aB._get};function aG(e,t){if(""==t)return e;var r={op:"_get",path:t};return aH(e,r),r.value}function aH(e,t,r=!1,a=!0,i=!0,n=0){if(r&&("function"==typeof r?r(t,0,e,t.path):aW(t,0)),""===t.path){let a={newDocument:e};if("add"===t.op)return a.newDocument=t.value,a;if("replace"===t.op)return a.newDocument=t.value,a.removed=e,a;if("move"===t.op||"copy"===t.op)return a.newDocument=aG(e,t.from),"move"===t.op&&(a.removed=e),a;else if("test"===t.op){if(a.test=aK(e,t.value),!1===a.test)throw new aF("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a.newDocument=e,a}else if("remove"===t.op)return a.removed=e,a.newDocument=null,a;else if("_get"===t.op)return t.value=e,a;else if(!r)return a;else throw new aF("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",n,t,e)}{let s,o,l;a||(e=a$(e));let u=(t.path||"").split("/"),d=e,c=1,h=u.length;for(o="function"==typeof r?r:aW;;){if((s=u[c])&&-1!=s.indexOf("~")&&(s=aM(s)),i&&("__proto__"==s||"prototype"==s&&c>0&&"constructor"==u[c-1]))throw TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(r&&void 0===l&&(void 0===d[s]?l=u.slice(0,c).join("/"):c==h-1&&(l=t.path),void 0!==l&&o(t,0,e,l)),c++,Array.isArray(d)){if("-"===s)s=d.length;else if(r&&!aN(s))throw new aF("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",n,t,e);else aN(s)&&(s=~~s);if(c>=h){if(r&&"add"===t.op&&s>d.length)throw new aF("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",n,t,e);let a=aZ[t.op].call(t,d,s,e);if(!1===a.test)throw new aF("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a}}else if(c>=h){let r=aB[t.op].call(t,d,s,e);if(!1===r.test)throw new aF("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r}if(d=d[s],r&&c<h&&(!d||"object"!=typeof d))throw new aF("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",n,t,e)}}}function aq(e,t,r,a=!0,i=!0){if(r&&!Array.isArray(t))throw new aF("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");a||(e=a$(e));let n=Array(t.length);for(let a=0,s=t.length;a<s;a++)n[a]=aH(e,t[a],r,!0,i,a),e=n[a].newDocument;return n.newDocument=e,n}function aJ(e,t,r){let a=aH(e,t);if(!1===a.test)throw new aF("Test operation failed","TEST_OPERATION_FAILED",r,t,e);return a.newDocument}function aW(e,t,r,a){if("object"!=typeof e||null===e||Array.isArray(e))throw new aF("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,r);if(aB[e.op]){if("string"!=typeof e.path)throw new aF("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,r);else if(0!==e.path.indexOf("/")&&e.path.length>0)throw new aF('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,r);else if(("move"===e.op||"copy"===e.op)&&"string"!=typeof e.from)throw new aF("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&void 0===e.value)throw new aF("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&function e(t){if(void 0===t)return!0;if(t){if(Array.isArray(t)){for(let r=0,a=t.length;r<a;r++)if(e(t[r]))return!0}else if("object"==typeof t){let a=aR(t),i=a.length;for(var r=0;r<i;r++)if(e(t[a[r]]))return!0}}return!1}(e.value))throw new aF("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,r);else if(r){if("add"==e.op){var i=e.path.split("/").length,n=a.split("/").length;if(i!==n+1&&i!==n)throw new aF("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,r)}else if("replace"===e.op||"remove"===e.op||"_get"===e.op){if(e.path!==a)throw new aF("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,r)}else if("move"===e.op||"copy"===e.op){var s=aV([{op:"_get",path:e.from,value:void 0}],r);if(s&&"OPERATION_PATH_UNRESOLVABLE"===s.name)throw new aF("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,r)}}}else throw new aF("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,r)}function aV(e,t,r){try{if(!Array.isArray(e))throw new aF("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)aq(a$(t),a$(e),r||!0);else{r=r||aW;for(var a=0;a<e.length;a++)r(e[a],a,t,void 0)}}catch(e){if(e instanceof aF)return e;throw e}}function aK(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){var r,a,i,n=Array.isArray(e),s=Array.isArray(t);if(n&&s){if((a=e.length)!=t.length)return!1;for(r=a;0!=r--;)if(!aK(e[r],t[r]))return!1;return!0}if(n!=s)return!1;var o=Object.keys(e);if((a=o.length)!==Object.keys(t).length)return!1;for(r=a;0!=r--;)if(!t.hasOwnProperty(o[r]))return!1;for(r=a;0!=r--;)if(!aK(e[i=o[r]],t[i]))return!1;return!0}return e!=e&&t!=t}var aY=new WeakMap;function aX(e,t,r,a,i){if(t!==e){"function"==typeof t.toJSON&&(t=t.toJSON());for(var n=aR(t),s=aR(e),o=!1,l=s.length-1;l>=0;l--){var u=s[l],d=e[u];if(aj(t,u)&&(void 0!==t[u]||void 0===d||!1!==Array.isArray(t))){var c=t[u];"object"==typeof d&&null!=d&&"object"==typeof c&&null!=c&&Array.isArray(d)===Array.isArray(c)?aX(d,c,r,a+"/"+aL(u),i):d!==c&&(i&&r.push({op:"test",path:a+"/"+aL(u),value:a$(d)}),r.push({op:"replace",path:a+"/"+aL(u),value:a$(c)}))}else Array.isArray(e)===Array.isArray(t)?(i&&r.push({op:"test",path:a+"/"+aL(u),value:a$(d)}),r.push({op:"remove",path:a+"/"+aL(u)}),o=!0):(i&&r.push({op:"test",path:a,value:e}),r.push({op:"replace",path:a,value:t}))}if(o||n.length!=s.length)for(var l=0;l<n.length;l++){var u=n[l];aj(e,u)||void 0===t[u]||r.push({op:"add",path:a+"/"+aL(u),value:a$(t[u])})}}}({...A,JsonPatchError:aD,deepClone:a$,escapePathComponent:aL,unescapePathComponent:aM});class aQ{}function a0(e){return"lc_prefer_streaming"in e&&e.lc_prefer_streaming}class a1 extends aQ{get lc_namespace(){return["langchain_core","callbacks",this.name]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}get lc_serializable_keys(){}static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eE(this.constructor)]}constructor(e){super(),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ignoreLLM",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreChain",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreAgent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"raiseError",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"awaitHandlers",{enumerable:!0,configurable:!0,writable:!0,value:"false"===eg("LANGCHAIN_CALLBACKS_BACKGROUND")}),this.lc_kwargs=e||{},e&&(this.ignoreLLM=e.ignoreLLM??this.ignoreLLM,this.ignoreChain=e.ignoreChain??this.ignoreChain,this.ignoreAgent=e.ignoreAgent??this.ignoreAgent,this.ignoreRetriever=e.ignoreRetriever??this.ignoreRetriever,this.ignoreCustomEvent=e.ignoreCustomEvent??this.ignoreCustomEvent,this.raiseError=e.raiseError??this.raiseError,this.awaitHandlers=this.raiseError||(e._awaitHandler??this.awaitHandlers))}copy(){return new this.constructor(this)}toJSON(){return eO.prototype.toJSON.call(this)}toJSONNotImplemented(){return eO.prototype.toJSONNotImplemented.call(this)}static fromMethods(e){class t extends a1{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:ru()}),Object.assign(this,e)}}return new t}}let a2=e=>void 0!==e&&"function"==typeof e.copy&&"string"==typeof e.name&&"boolean"==typeof e.awaitHandlers,a4=e=>{if(e)return e.events=e.events??[],e.child_runs=e.child_runs??[],e};function a5(e,t){return e&&!Array.isArray(e)&&"object"==typeof e?e:{[t]:e}}function a3(e){return"function"==typeof e._addRunToRunMap}class a9 extends a1{constructor(e){super(...arguments),Object.defineProperty(this,"runMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"runTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"usesRunTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:!1})}copy(){return this}getRunById(e){if(void 0!==e)return this.usesRunTreeMap?a4(this.runTreeMap.get(e)):this.runMap.get(e)}stringifyError(e){return e instanceof Error?e.message+(e?.stack?`

${e.stack}`:""):"string"==typeof e?e:`${e}`}_addChildRun(e,t){e.child_runs.push(t)}_addRunToRunMap(e){let t=function(e,t,r){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(e.start_time,e.id,e.execution_order),r={...e},i=this.getRunById(r.parent_run_id);if(void 0!==r.parent_run_id?i&&(this._addChildRun(i,r),i.child_execution_order=Math.max(i.child_execution_order,r.child_execution_order),r.trace_id=i.trace_id,void 0!==i.dotted_order&&(r.dotted_order=[i.dotted_order,t].join("."))):(r.trace_id=r.id,r.dotted_order=t),this.usesRunTreeMap){let e=function e(t,r){if(t)return new aw({...t,parent_run:e(r),child_runs:t.child_runs.map(t=>e(t)).filter(e=>void 0!==e),extra:{...t.extra,runtime:(void 0===a&&(a={library:"langchain-js",runtime:em()}),a)},tracingEnabled:!1})}(r,i);void 0!==e&&this.runTreeMap.set(r.id,e)}else this.runMap.set(r.id,r);return r}async _endTrace(e){let t=void 0!==e.parent_run_id&&this.getRunById(e.parent_run_id);t?t.child_execution_order=Math.max(t.child_execution_order,e.child_execution_order):await this.persistRun(e),await this.onRunUpdate?.(e),this.usesRunTreeMap?this.runTreeMap.delete(e.id):this.runMap.delete(e.id)}_getExecutionOrder(e){let t=void 0!==e&&this.getRunById(e);return t?t.child_execution_order+1:1}_createRunForLLMStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d=s?{...i,metadata:s}:i,c={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{prompts:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:d??{},tags:n||[]};return this._addRunToRunMap(c)}async handleLLMStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForLLMStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}_createRunForChatModelStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d=s?{...i,metadata:s}:i,c={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{messages:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:d??{},tags:n||[]};return this._addRunToRunMap(c)}async handleChatModelStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForChatModelStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}async handleLLMEnd(e,t,r,a,i){let n=this.getRunById(t);if(!n||n?.run_type!=="llm")throw Error("No LLM run to end.");return n.end_time=Date.now(),n.outputs=e,n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),n.extra={...n.extra,...i},await this.onLLMEnd?.(n),await this._endTrace(n),n}async handleLLMError(e,t,r,a,i){let n=this.getRunById(t);if(!n||n?.run_type!=="llm")throw Error("No LLM run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),n.extra={...n.extra,...i},await this.onLLMError?.(n),await this._endTrace(n),n}_createRunForChainStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:t,execution_order:l,child_execution_order:l,run_type:s??"chain",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(d)}async handleChainStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForChainStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onChainStart?.(l),l}async handleChainEnd(e,t,r,a,i){let n=this.getRunById(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.outputs=a5(e,"output"),n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=a5(i.inputs,"input")),await this.onChainEnd?.(n),await this._endTrace(n),n}async handleChainError(e,t,r,a,i){let n=this.getRunById(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=a5(i.inputs,"input")),await this.onChainError?.(n),await this._endTrace(n),n}_createRunForToolStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{input:t},execution_order:o,child_execution_order:o,run_type:"tool",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleToolStart(e,t,r,a,i,n,s){let o=this.getRunById(r)??this._createRunForToolStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onToolStart?.(o),o}async handleToolEnd(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.outputs={output:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onToolEnd?.(r),await this._endTrace(r),r}async handleToolError(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onToolError?.(r),await this._endTrace(r),r}async handleAgentAction(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.actions=r.actions||[],r.actions.push(e),r.events.push({name:"agent_action",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentAction?.(r))}async handleAgentEnd(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.events.push({name:"agent_end",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentEnd?.(r))}_createRunForRetrieverStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{query:t},execution_order:o,child_execution_order:o,run_type:"retriever",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleRetrieverStart(e,t,r,a,i,n,s){let o=this.getRunById(r)??this._createRunForRetrieverStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onRetrieverStart?.(o),o}async handleRetrieverEnd(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.outputs={documents:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onRetrieverEnd?.(r),await this._endTrace(r),r}async handleRetrieverError(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onRetrieverError?.(r),await this._endTrace(r),r}async handleText(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.events.push({name:"text",time:new Date().toISOString(),kwargs:{text:e}}),await this.onText?.(r))}async handleLLMNewToken(e,t,r,a,i,n){let s=this.getRunById(r);if(!s||s?.run_type!=="llm")throw Error('Invalid "runId" provided to "handleLLMNewToken" callback.');return s.events.push({name:"new_token",time:new Date().toISOString(),kwargs:{token:e,idx:t,chunk:n?.chunk}}),await this.onLLMNewToken?.(s,e,{chunk:n?.chunk}),s}}var a6=r(63391);function a8(e,t){return`${e.open}${t}${e.close}`}function a7(e,t){try{return JSON.stringify(e,null,2)}catch(e){return t}}function ie(e){return"string"==typeof e?e.trim():null==e?e:a7(e,e.toString())}function it(e){if(!e.end_time)return"";let t=e.end_time-e.start_time;return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`}let{color:ir}=a6;class ia extends a9{constructor(){super(...arguments),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"console_callback_handler"})}persistRun(e){return Promise.resolve()}getParents(e){let t=[],r=e;for(;r.parent_run_id;){let e=this.runMap.get(r.parent_run_id);if(e)t.push(e),r=e;else break}return t}getBreadcrumbs(e){let t=[...this.getParents(e).reverse(),e].map((e,t,r)=>{let a=`${e.execution_order}:${e.run_type}:${e.name}`;return t===r.length-1?a8(a6.bold,a):a}).join(" > ");return a8(ir.grey,t)}onChainStart(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.green,"[chain/start]")} [${t}] Entering Chain run with input: ${a7(e.inputs,"[inputs]")}`)}onChainEnd(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.cyan,"[chain/end]")} [${t}] [${it(e)}] Exiting Chain run with output: ${a7(e.outputs,"[outputs]")}`)}onChainError(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.red,"[chain/error]")} [${t}] [${it(e)}] Chain run errored with error: ${a7(e.error,"[error]")}`)}onLLMStart(e){let t=this.getBreadcrumbs(e),r="prompts"in e.inputs?{prompts:e.inputs.prompts.map(e=>e.trim())}:e.inputs;console.log(`${a8(ir.green,"[llm/start]")} [${t}] Entering LLM run with input: ${a7(r,"[inputs]")}`)}onLLMEnd(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.cyan,"[llm/end]")} [${t}] [${it(e)}] Exiting LLM run with output: ${a7(e.outputs,"[response]")}`)}onLLMError(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.red,"[llm/error]")} [${t}] [${it(e)}] LLM run errored with error: ${a7(e.error,"[error]")}`)}onToolStart(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.green,"[tool/start]")} [${t}] Entering Tool run with input: "${ie(e.inputs.input)}"`)}onToolEnd(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.cyan,"[tool/end]")} [${t}] [${it(e)}] Exiting Tool run with output: "${ie(e.outputs?.output)}"`)}onToolError(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.red,"[tool/error]")} [${t}] [${it(e)}] Tool run errored with error: ${a7(e.error,"[error]")}`)}onRetrieverStart(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.green,"[retriever/start]")} [${t}] Entering Retriever run with input: ${a7(e.inputs,"[inputs]")}`)}onRetrieverEnd(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.cyan,"[retriever/end]")} [${t}] [${it(e)}] Exiting Retriever run with output: ${a7(e.outputs,"[outputs]")}`)}onRetrieverError(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.red,"[retriever/error]")} [${t}] [${it(e)}] Retriever run errored with error: ${a7(e.error,"[error]")}`)}onAgentAction(e){let t=this.getBreadcrumbs(e);console.log(`${a8(ir.blue,"[agent/action]")} [${t}] Agent selected action: ${a7(e.actions[e.actions.length-1],"[action]")}`)}}let ii=()=>(void 0===l&&(l=new ag("false"===eg("LANGCHAIN_CALLBACKS_BACKGROUND")?{blockOnRootRunFinalization:!0}:{})),l);class is extends a9{constructor(e={}){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"projectName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"usesRunTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:!0});let{exampleId:t,projectName:r,client:a,replicas:i}=e;this.projectName=r??rA(),this.replicas=i,this.exampleId=t,this.client=a??ii();let n=is.getTraceableRunTree();n&&this.updateFromRunTree(n)}async persistRun(e){}async onRunCreate(e){let t=this.getRunTreeWithTracingConfig(e.id);await t?.postRun()}async onRunUpdate(e){let t=this.getRunTreeWithTracingConfig(e.id);await t?.patchRun()}getRun(e){return this.runTreeMap.get(e)}updateFromRunTree(e){let t=e,r=new Set;for(;t.parent_run&&!r.has(t.id)&&(r.add(t.id),t.parent_run);){;t=t.parent_run}r.clear();let a=[t];for(;a.length>0;){let e=a.shift();!(!e||r.has(e.id))&&(r.add(e.id),this.runTreeMap.set(e.id,e),e.child_runs&&a.push(...e.child_runs))}this.client=e.client??this.client,this.replicas=e.replicas??this.replicas,this.projectName=e.project_name??this.projectName,this.exampleId=e.reference_example_id??this.exampleId}getRunTreeWithTracingConfig(e){let t=this.runTreeMap.get(e);if(t)return new aw({...t,client:this.client,project_name:this.projectName,replicas:this.replicas,reference_example_id:this.exampleId,tracingEnabled:!0})}static getTraceableRunTree(){try{return function(e=!1){let t=aI.getInstance().getStore();if(!e&&!aE(t))throw Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function and that tracing is enabled.");return t}(!0)}catch{return}}}let io=Symbol.for("ls:tracing_async_local_storage"),il=Symbol.for("lc:context_variables"),iu=e=>{globalThis[io]=e},id=()=>globalThis[io];async function ic(e,t){if(!0===t){let t=id();void 0!==t?await t.run(void 0,async()=>e()):await e()}else void 0===u&&(u=new(0,rX.default)({autoStart:!0,concurrency:1})),u.add(async()=>{let t=id();void 0!==t?await t.run(void 0,async()=>e()):await e()})}let ih=e=>void 0!==e?e:!!["LANGSMITH_TRACING_V2","LANGCHAIN_TRACING_V2","LANGSMITH_TRACING","LANGCHAIN_TRACING"].find(e=>"true"===eg(e));function ip(e){let t=id();if(void 0===t)return;let r=t.getStore();return r?.[il]?.[e]}let im=Symbol("lc:configure_hooks"),ig=()=>ip(im)||[];class iy{setHandler(e){return this.setHandlers([e])}}class i_{constructor(e,t,r,a,i,n,s,o){Object.defineProperty(this,"runId",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:s}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:o})}get parentRunId(){return this._parentRunId}async handleText(e){await Promise.all(this.handlers.map(t=>ic(async()=>{try{await t.handleText?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleText: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(r=>ic(async()=>{try{await r.handleCustomEvent?.(e,t,this.runId,this.tags,this.metadata)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleCustomEvent: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}}class ib extends i_{getChild(e){let t=new iO(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleRetrieverEnd(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetriever`),t.raiseError)throw e}},t.awaitHandlers)))}async handleRetrieverError(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverError?.(e,this.runId,this._parentRunId,this.tags)}catch(r){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetrieverError: ${r}`),t.raiseError)throw e}},t.awaitHandlers)))}}class iv extends i_{async handleLLMNewToken(e,t,r,a,i,n){await Promise.all(this.handlers.map(r=>ic(async()=>{if(!r.ignoreLLM)try{await r.handleLLMNewToken?.(e,t??{prompt:0,completion:0},this.runId,this._parentRunId,this.tags,n)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMNewToken: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}async handleLLMError(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreLLM)try{await t.handleLLMError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleLLMEnd(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreLLM)try{await t.handleLLMEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class iw extends i_{getChild(e){let t=new iO(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleChainError(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreChain)try{await t.handleChainError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleChainEnd(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreChain)try{await t.handleChainEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentAction(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreAgent)try{await t.handleAgentAction?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentAction: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentEnd(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreAgent)try{await t.handleAgentEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class iE extends i_{getChild(e){let t=new iO(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleToolError(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreAgent)try{await t.handleToolError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleToolEnd(e){await Promise.all(this.handlers.map(t=>ic(async()=>{if(!t.ignoreAgent)try{await t.handleToolEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class iO extends iy{constructor(e,t){super(),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"callback_manager"}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.handlers=t?.handlers??this.handlers,this.inheritableHandlers=t?.inheritableHandlers??this.inheritableHandlers,this.tags=t?.tags??this.tags,this.inheritableTags=t?.inheritableTags??this.inheritableTags,this.metadata=t?.metadata??this.metadata,this.inheritableMetadata=t?.inheritableMetadata??this.inheritableMetadata,this._parentRunId=e}getParentRunId(){return this._parentRunId}async handleLLMStart(e,t,r,a,i,n,s,o){return Promise.all(t.map(async(t,a)=>{let n=0===a&&r?r:ru();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return a3(r)&&r._createRunForLLMStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),ic(async()=>{try{await r.handleLLMStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new iv(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChatModelStart(e,t,r,a,i,n,s,o){return Promise.all(t.map(async(t,a)=>{let n=0===a&&r?r:ru();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return a3(r)&&r._createRunForChatModelStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),ic(async()=>{try{if(r.handleChatModelStart)await r.handleChatModelStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o);else if(r.handleLLMStart){let a=eX(t);await r.handleLLMStart?.(e,[a],n,this._parentRunId,i,this.tags,this.metadata,o)}}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new iv(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChainStart(e,t,r=ru(),a,i,n,s){return await Promise.all(this.handlers.map(i=>{if(!i.ignoreChain)return a3(i)&&i._createRunForChainStart(e,t,r,this._parentRunId,this.tags,this.metadata,a,s),ic(async()=>{try{await i.handleChainStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,a,s)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleChainStart: ${e}`),i.raiseError)throw e}},i.awaitHandlers)})),new iw(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleToolStart(e,t,r=ru(),a,i,n,s){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreAgent)return a3(a)&&a._createRunForToolStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),ic(async()=>{try{await a.handleToolStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleToolStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new iE(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleRetrieverStart(e,t,r=ru(),a,i,n,s){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreRetriever)return a3(a)&&a._createRunForRetrieverStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),ic(async()=>{try{await a.handleRetrieverStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleRetrieverStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new ib(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(a=>ic(async()=>{if(!a.ignoreCustomEvent)try{await a.handleCustomEvent?.(e,t,r,this.tags,this.metadata)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleCustomEvent: ${e}`),a.raiseError)throw e}},a.awaitHandlers)))}addHandler(e,t=!0){this.handlers.push(e),t&&this.inheritableHandlers.push(e)}removeHandler(e){this.handlers=this.handlers.filter(t=>t!==e),this.inheritableHandlers=this.inheritableHandlers.filter(t=>t!==e)}setHandlers(e,t=!0){for(let r of(this.handlers=[],this.inheritableHandlers=[],e))this.addHandler(r,t)}addTags(e,t=!0){this.removeTags(e),this.tags.push(...e),t&&this.inheritableTags.push(...e)}removeTags(e){this.tags=this.tags.filter(t=>!e.includes(t)),this.inheritableTags=this.inheritableTags.filter(t=>!e.includes(t))}addMetadata(e,t=!0){this.metadata={...this.metadata,...e},t&&(this.inheritableMetadata={...this.inheritableMetadata,...e})}removeMetadata(e){for(let t of Object.keys(e))delete this.metadata[t],delete this.inheritableMetadata[t]}copy(e=[],t=!0){let r=new iO(this._parentRunId);for(let e of this.handlers){let t=this.inheritableHandlers.includes(e);r.addHandler(e,t)}for(let e of this.tags){let t=this.inheritableTags.includes(e);r.addTags([e],t)}for(let e of Object.keys(this.metadata)){let t=Object.keys(this.inheritableMetadata).includes(e);r.addMetadata({[e]:this.metadata[e]},t)}for(let a of e)r.handlers.filter(e=>"console_callback_handler"===e.name).some(e=>e.name===a.name)||r.addHandler(a,t);return r}static fromHandlers(e){class t extends a1{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:ru()}),Object.assign(this,e)}}let r=new this;return r.addHandler(new t),r}static configure(e,t,r,a,i,n,s){return this._configureSync(e,t,r,a,i,n,s)}static _configureSync(e,t,r,a,i,n,s){let o;(e||t)&&(Array.isArray(e)||!e?(o=new iO).setHandlers(e?.map(ix)??[],!0):o=e,o=o.copy(Array.isArray(t)?t.map(ix):t?.handlers,!1));let l="true"===eg("LANGCHAIN_VERBOSE")||s?.verbose,u=is.getTraceableRunTree()?.tracingEnabled||ih(),d=u||(eg("LANGCHAIN_TRACING")??!1);if(l||d){if(o||(o=new iO),l&&!o.handlers.some(e=>e.name===ia.prototype.name)){let e=new ia;o.addHandler(e,!0)}if(d&&!o.handlers.some(e=>"langchain_tracer"===e.name)&&u){let e=new is;o.addHandler(e,!0),o._parentRunId=is.getTraceableRunTree()?.id??o._parentRunId}}for(let{contextVar:e,inheritable:t=!0,handlerClass:r,envVar:a}of ig()){let i,n=a&&"true"===eg(a)&&r,s=void 0!==e?ip(e):void 0;s&&a2(s)?i=s:n&&(i=new r({})),void 0!==i&&(o||(o=new iO),o.handlers.some(e=>e.name===i.name)||o.addHandler(i,t))}return(r||a)&&o&&(o.addTags(r??[]),o.addTags(a??[],!1)),(i||n)&&o&&(o.addMetadata(i??{}),o.addMetadata(n??{},!1)),o}}function ix(e){return"name"in e?e:a1.fromMethods(e)}class iT{getStore(){}run(e,t){return t()}enterWith(e){}}let iS=new iT,ik=Symbol.for("lc:child_config");class iA{getInstance(){return id()??iS}getRunnableConfig(){let e=this.getInstance();return e.getStore()?.extra?.[ik]}runWithConfig(e,t,r){let a,i=iO._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata),n=this.getInstance(),s=n.getStore(),o=i?.getParentRunId(),l=i?.handlers?.find(e=>e?.name==="langchain_tracer");return l&&o?a=l.getRunTreeWithTracingConfig(o):r||(a=new aw({name:"<runnable_lambda>",tracingEnabled:!1})),a&&(a.extra={...a.extra,[ik]:e}),void 0!==s&&void 0!==s[il]&&(void 0===a&&(a={}),a[il]=s[il]),n.run(a,t)}initializeGlobalInstance(e){void 0===id()&&iu(e)}}let iI=new iA;async function iC(e){return iO._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata)}function iP(...e){let t={};for(let r of e.filter(e=>!!e))for(let e of Object.keys(r))if("metadata"===e)t[e]={...t[e],...r[e]};else if("tags"===e){let a=t[e]??[];t[e]=[...new Set(a.concat(r[e]??[]))]}else if("configurable"===e)t[e]={...t[e],...r[e]};else if("timeout"===e)void 0===t.timeout?t.timeout=r.timeout:void 0!==r.timeout&&(t.timeout=Math.min(t.timeout,r.timeout));else if("signal"===e)void 0===t.signal?t.signal=r.signal:void 0!==r.signal&&("any"in AbortSignal?t.signal=AbortSignal.any([t.signal,r.signal]):t.signal=r.signal);else if("callbacks"===e){let e=t.callbacks,a=r.callbacks;if(Array.isArray(a))if(e)if(Array.isArray(e))t.callbacks=e.concat(a);else{let r=e.copy();for(let e of a)r.addHandler(ix(e),!0);t.callbacks=r}else t.callbacks=a;else if(a)if(e)if(Array.isArray(e)){let r=a.copy();for(let t of e)r.addHandler(ix(t),!0);t.callbacks=r}else t.callbacks=new iO(a._parentRunId,{handlers:e.handlers.concat(a.handlers),inheritableHandlers:e.inheritableHandlers.concat(a.inheritableHandlers),tags:Array.from(new Set(e.tags.concat(a.tags))),inheritableTags:Array.from(new Set(e.inheritableTags.concat(a.inheritableTags))),metadata:{...e.metadata,...a.metadata}});else t.callbacks=a}else t[e]=r[e]??t[e];return t}let ij=new Set(["string","number","boolean"]);function iR(e){let t=iI.getRunnableConfig(),r={tags:[],metadata:{},recursionLimit:25,runId:void 0};if(t){let{runId:e,runName:a,...i}=t;r=Object.entries(i).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)}if(e&&(r=Object.entries(e).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)),r?.configurable)for(let e of Object.keys(r.configurable))ij.has(typeof r.configurable[e])&&!r.metadata?.[e]&&(r.metadata||(r.metadata={}),r.metadata[e]=r.configurable[e]);if(void 0!==r.timeout){if(r.timeout<=0)throw Error("Timeout must be a positive number");let e=AbortSignal.timeout(r.timeout);void 0!==r.signal?"any"in AbortSignal&&(r.signal=AbortSignal.any([r.signal,e])):r.signal=e,delete r.timeout}return r}function i$(e={},{callbacks:t,maxConcurrency:r,recursionLimit:a,runName:i,configurable:n,runId:s}={}){let o=iR(e);return void 0!==t&&(delete o.runName,o.callbacks=t),void 0!==a&&(o.recursionLimit=a),void 0!==r&&(o.maxConcurrency=r),void 0!==i&&(o.runName=i),void 0!==n&&(o.configurable={...o.configurable,...n}),void 0!==s&&delete o.runId,o}function iN(e){return e?{configurable:e.configurable,recursionLimit:e.recursionLimit,callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,maxConcurrency:e.maxConcurrency,timeout:e.timeout,signal:e.signal}:void 0}async function iL(e,t){let r;return void 0===t?e:Promise.race([e.catch(e=>{if(!t?.aborted)throw e}),new Promise((e,a)=>{r=()=>{a(Error("Aborted"))},t.addEventListener("abort",r),t.aborted&&a(Error("Aborted"))})]).finally(()=>t.removeEventListener("abort",r))}class iM extends ReadableStream{constructor(){super(...arguments),Object.defineProperty(this,"reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}ensureReader(){this.reader||(this.reader=this.getReader())}async next(){this.ensureReader();try{let e=await this.reader.read();if(e.done)return this.reader.releaseLock(),{done:!0,value:void 0};return{done:!1,value:e.value}}catch(e){throw this.reader.releaseLock(),e}}async return(){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}return{done:!0,value:void 0}}async throw(e){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}throw e}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}static fromReadableStream(e){let t=e.getReader();return new iM({start:e=>(function r(){return t.read().then(({done:t,value:a})=>t?void e.close():(e.enqueue(a),r()))})(),cancel(){t.releaseLock()}})}static fromAsyncGenerator(e){return new iM({async pull(t){let{value:r,done:a}=await e.next();a&&t.close(),t.enqueue(r)},async cancel(t){await e.return(t)}})}}function iU(e,t=2){let r=Array.from({length:t},()=>[]);return r.map(async function*(t){for(;;)if(0===t.length){let t=await e.next();for(let e of r)e.push(t)}else{if(t[0].done)return;yield t.shift().value}})}function iD(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.concat(t);if("string"==typeof e&&"string"==typeof t)return e+t;if("number"==typeof e&&"number"==typeof t)return e+t;if("concat"in e&&"function"==typeof e.concat)return e.concat(t);if("object"==typeof e&&"object"==typeof t){let r={...e};for(let[e,a]of Object.entries(t))e in r&&!Array.isArray(r[e])?r[e]=iD(r[e],a):r[e]=a;return r}else throw Error(`Cannot concat ${typeof e} and ${typeof t}`)}class iF{constructor(e){Object.defineProperty(this,"generator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"setup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signal",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResult",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResultUsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.generator=e.generator,this.config=e.config,this.signal=e.signal??this.config?.signal,this.setup=new Promise((t,r)=>{iI.runWithConfig(iN(e.config),async()=>{this.firstResult=e.generator.next(),e.startSetup?this.firstResult.then(e.startSetup).then(t,r):this.firstResult.then(e=>t(void 0),r)},!0)})}async next(...e){return(this.signal?.throwIfAborted(),this.firstResultUsed)?iI.runWithConfig(iN(this.config),this.signal?async()=>iL(this.generator.next(...e),this.signal):async()=>this.generator.next(...e),!0):(this.firstResultUsed=!0,this.firstResult)}async return(e){return this.generator.return(e)}async throw(e){return this.generator.throw(e)}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}}async function iz(e,t,r,a,...i){let n=new iF({generator:t,startSetup:r,signal:a}),s=await n.setup;return{output:e(n,s,...i),setup:s}}class iB{constructor(e){Object.defineProperty(this,"ops",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.ops=e.ops??[]}concat(e){let t=this.ops.concat(e.ops),r=aq({},t);return new iZ({ops:t,state:r[r.length-1].newDocument})}}class iZ extends iB{constructor(e){super(e),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state=e.state}concat(e){let t=this.ops.concat(e.ops),r=aq(this.state,e.ops);return new iZ({ops:t,state:r[r.length-1].newDocument})}static fromRunLogPatch(e){let t=aq({},e.ops);return new iZ({ops:e.ops,state:t[t.length-1].newDocument})}}let iG=e=>"log_stream_tracer"===e.name;async function iH(e,t){if("original"===t)throw Error("Do not assign inputs with original schema drop the key for now. When inputs are added to streamLog they should be added with standardized schema for streaming events.");let{inputs:r}=e;return["retriever","llm","prompt"].includes(e.run_type)?r:1!==Object.keys(r).length||r?.input!==""?r.input:void 0}async function iq(e,t){let{outputs:r}=e;return"original"===t||["retriever","llm","prompt"].includes(e.run_type)?r:void 0!==r&&1===Object.keys(r).length&&r?.output!==void 0?r.output:r}class iJ extends a9{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaFormat",{enumerable:!0,configurable:!0,writable:!0,value:"original"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"keyMapByRunId",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"counterMapByRunName",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"log_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this._schemaFormat=e?._schemaFormat??this._schemaFormat,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=iM.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){if(e.id===this.rootId)return!1;let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.run_type)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.run_type)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){for await(let r of t){if(e!==this.rootId){let t=this.keyMapByRunId[e];t&&await this.writer.write(new iB({ops:[{op:"add",path:`/logs/${t}/streamed_output/-`,value:r}]}))}yield r}}async onRunCreate(e){if(void 0===this.rootId&&(this.rootId=e.id,await this.writer.write(new iB({ops:[{op:"replace",path:"",value:{id:e.id,name:e.name,type:e.run_type,streamed_output:[],final_output:void 0,logs:{}}}]}))),!this._includeRun(e))return;void 0===this.counterMapByRunName[e.name]&&(this.counterMapByRunName[e.name]=0),this.counterMapByRunName[e.name]+=1;let t=this.counterMapByRunName[e.name];this.keyMapByRunId[e.id]=1===t?e.name:`${e.name}:${t}`;let r={id:e.id,name:e.name,type:e.run_type,tags:e.tags??[],metadata:e.extra?.metadata??{},start_time:new Date(e.start_time).toISOString(),streamed_output:[],streamed_output_str:[],final_output:void 0,end_time:void 0};"streaming_events"===this._schemaFormat&&(r.inputs=await iH(e,this._schemaFormat)),await this.writer.write(new iB({ops:[{op:"add",path:`/logs/${this.keyMapByRunId[e.id]}`,value:r}]}))}async onRunUpdate(e){try{let t=this.keyMapByRunId[e.id];if(void 0===t)return;let r=[];"streaming_events"===this._schemaFormat&&r.push({op:"replace",path:`/logs/${t}/inputs`,value:await iH(e,this._schemaFormat)}),r.push({op:"add",path:`/logs/${t}/final_output`,value:await iq(e,this._schemaFormat)}),void 0!==e.end_time&&r.push({op:"add",path:`/logs/${t}/end_time`,value:new Date(e.end_time).toISOString()});let a=new iB({ops:r});await this.writer.write(a)}finally{if(e.id===this.rootId){let t=new iB({ops:[{op:"replace",path:"/final_output",value:await iq(e,this._schemaFormat)}]});await this.writer.write(t),this.autoClose&&await this.writer.close()}}}async onLLMNewToken(e,t,r){let a,i=this.keyMapByRunId[e.id];if(void 0===i)return;if(void 0!==e.inputs.messages){var n;a=void 0!==(n=r?.chunk)&&void 0!==n.message?r?.chunk:new eM({id:`run-${e.id}`,content:t})}else a=t;let s=new iB({ops:[{op:"add",path:`/logs/${i}/streamed_output_str/-`,value:t},{op:"add",path:`/logs/${i}/streamed_output/-`,value:a}]});await this.writer.write(s)}}let iW="__run";class iV{constructor(e){Object.defineProperty(this,"text",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"generationInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.text=e.text,this.generationInfo=e.generationInfo}concat(e){return new iV({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo}})}}class iK extends iV{constructor(e){super(e),Object.defineProperty(this,"message",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.message=e.message}concat(e){return new iK({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo},message:this.message.concat(e.message)})}}function iY({name:e,serialized:t}){return void 0!==e?e:t?.name!==void 0?t.name:t?.id!==void 0&&Array.isArray(t?.id)?t.id[t.id.length-1]:"Unnamed"}let iX=e=>"event_stream_tracer"===e.name;class iQ extends a9{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runInfoMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"tappedPromises",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"event_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=iM.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.runType)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.runType)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){let r=await t.next();if(r.done)return;let a=this.runInfoMap.get(e);if(void 0===a)return void(yield r.value);function i(e,t){return"llm"===e&&"string"==typeof t?new iV({text:t}):t}let n=this.tappedPromises.get(e);if(void 0===n){let s;n=new Promise(e=>{s=e}),this.tappedPromises.set(e,n);try{let n={event:`on_${a.runType}_stream`,run_id:e,name:a.name,tags:a.tags,metadata:a.metadata,data:{}};for await(let e of(await this.send({...n,data:{chunk:i(a.runType,r.value)}},a),yield r.value,t))"tool"!==a.runType&&"retriever"!==a.runType&&await this.send({...n,data:{chunk:i(a.runType,e)}},a),yield e}finally{s()}}else for await(let e of(yield r.value,t))yield e}async send(e,t){this._includeRun(t)&&await this.writer.write(e)}async sendEndEvent(e,t){let r=this.tappedPromises.get(e.run_id);void 0!==r?r.then(()=>{this.send(e,t)}):await this.send(e,t)}async onLLMStart(e){let t=iY(e),r=void 0!==e.inputs.messages?"chat_model":"llm",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:r,inputs:e.inputs};this.runInfoMap.set(e.id,a);let i=`on_${r}_start`;await this.send({event:i,data:{input:e.inputs},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onLLMNewToken(e,t,r){let a,i,n=this.runInfoMap.get(e.id);if(void 0===n)throw Error(`onLLMNewToken: Run ID ${e.id} not found in run map.`);if(1!==this.runInfoMap.size){if("chat_model"===n.runType)i="on_chat_model_stream",a=r?.chunk===void 0?new eM({content:t,id:`run-${e.id}`}):r.chunk.message;else if("llm"===n.runType)i="on_llm_stream",a=r?.chunk===void 0?new iV({text:t}):r.chunk;else throw Error(`Unexpected run type ${n.runType}`);await this.send({event:i,data:{chunk:a},run_id:e.id,name:n.name,tags:n.tags,metadata:n.metadata},n)}}async onLLMEnd(e){let t,r,a=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===a)throw Error(`onLLMEnd: Run ID ${e.id} not found in run map.`);let i=e.outputs?.generations;if("chat_model"===a.runType){for(let e of i??[]){if(void 0!==r)break;r=e[0]?.message}t="on_chat_model_end"}else if("llm"===a.runType)r={generations:i?.map(e=>e.map(e=>({text:e.text,generationInfo:e.generationInfo}))),llmOutput:e.outputs?.llmOutput??{}},t="on_llm_end";else throw Error(`onLLMEnd: Unexpected run type: ${a.runType}`);await this.sendEndEvent({event:t,data:{output:r,input:a.inputs},run_id:e.id,name:a.name,tags:a.tags,metadata:a.metadata},a)}async onChainStart(e){let t=iY(e),r=e.run_type??"chain",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:e.run_type},i={};""===e.inputs.input&&1===Object.keys(e.inputs).length?(i={},a.inputs={}):void 0!==e.inputs.input?(i.input=e.inputs.input,a.inputs=e.inputs.input):(i.input=e.inputs,a.inputs=e.inputs),this.runInfoMap.set(e.id,a),await this.send({event:`on_${r}_start`,data:i,name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onChainEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onChainEnd: Run ID ${e.id} not found in run map.`);let r=`on_${e.run_type}_end`,a=e.inputs??t.inputs??{},i={output:e.outputs?.output??e.outputs,input:a};a.input&&1===Object.keys(a).length&&(i.input=a.input,t.inputs=a.input),await this.sendEndEvent({event:r,data:i,run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata??{}},t)}async onToolStart(e){let t=iY(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"tool",inputs:e.inputs??{}};this.runInfoMap.set(e.id,r),await this.send({event:"on_tool_start",data:{input:e.inputs??{}},name:t,run_id:e.id,tags:e.tags??[],metadata:e.extra?.metadata??{}},r)}async onToolEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onToolEnd: Run ID ${e.id} not found in run map.`);if(void 0===t.inputs)throw Error(`onToolEnd: Run ID ${e.id} is a tool call, and is expected to have traced inputs.`);let r=e.outputs?.output===void 0?e.outputs:e.outputs.output;await this.sendEndEvent({event:"on_tool_end",data:{output:r,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async onRetrieverStart(e){let t=iY(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"retriever",inputs:{query:e.inputs.query}};this.runInfoMap.set(e.id,r),await this.send({event:"on_retriever_start",data:{input:{query:e.inputs.query}},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onRetrieverEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onRetrieverEnd: Run ID ${e.id} not found in run map.`);await this.sendEndEvent({event:"on_retriever_end",data:{output:e.outputs?.documents??e.outputs,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async handleCustomEvent(e,t,r){let a=this.runInfoMap.get(r);if(void 0===a)throw Error(`handleCustomEvent: Run ID ${r} not found in run map.`);await this.send({event:"on_custom_event",run_id:r,name:e,tags:a.tags,metadata:a.metadata,data:t},a)}async finish(){Promise.all([...this.tappedPromises.values()]).finally(()=>{this.writer.close()})}}let i0=[400,401,402,403,404,405,406,407,409],i1=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")throw e;let t=e?.response?.status??e?.status;if(t&&i0.includes(+t))throw e;if(e?.error?.code==="insufficient_quota"){let t=Error(e?.message);throw t.name="InsufficientQuotaError",t}};class i2{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.onFailedAttempt=e.onFailedAttempt??i1;let t=rX.default;this.queue=new t({concurrency:this.maxConcurrency})}call(e,...t){return this.queue.add(()=>rr(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{onFailedAttempt:this.onFailedAttempt,retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>fetch(...e).then(e=>e.ok?e:Promise.reject(e)))}}class i4 extends a9{constructor({config:e,onStart:t,onEnd:r,onError:a}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"RootListenersTracer"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnStart",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnEnd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnError",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.config=e,this.argOnStart=t,this.argOnEnd=r,this.argOnError=a}persistRun(e){return Promise.resolve()}async onRunCreate(e){!this.rootId&&(this.rootId=e.id,this.argOnStart&&await this.argOnStart(e,this.config))}async onRunUpdate(e){e.id===this.rootId&&(e.error?this.argOnError&&await this.argOnError(e,this.config):this.argOnEnd&&await this.argOnEnd(e,this.config))}}function i5(e){return!!e&&e.lc_runnable}class i3{constructor(e){Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.includeNames=e.includeNames,this.includeTypes=e.includeTypes,this.includeTags=e.includeTags,this.excludeNames=e.excludeNames,this.excludeTypes=e.excludeTypes,this.excludeTags=e.excludeTags}includeEvent(e,t){let r=void 0===this.includeNames&&void 0===this.includeTypes&&void 0===this.includeTags,a=e.tags??[];return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(t)),void 0!==this.includeTags&&(r=r||a.some(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(t)),void 0!==this.excludeTags&&(r=r&&a.every(e=>!this.excludeTags?.includes(e))),r}}let i9=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,i6=function(e){return"string"==typeof e&&i9.test(e)};function i8(e){return e.replace(/[^a-zA-Z-_0-9]/g,"_")}let i7=["*","_","`"];async function ne(e,t){let{backgroundColor:r="white"}=t??{},a=btoa(e);void 0!==r&&(/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(r)||(r=`!${r}`));let i=`https://mermaid.ink/img/${a}?bgColor=${r}`,n=await fetch(i);if(!n.ok)throw Error(`Failed to render the graph using the Mermaid.INK API.
Status code: ${n.status}
Status text: ${n.statusText}`);return await n.blob()}function nt(e,t,r){function a(r,a){var i;for(let n in Object.defineProperty(r,"_zod",{value:r._zod??{},enumerable:!1}),(i=r._zod).traits??(i.traits=new Set),r._zod.traits.add(e),t(r,a),s.prototype)n in r||Object.defineProperty(r,n,{value:s.prototype[n].bind(r)});r._zod.constr=s,r._zod.def=a}let i=r?.Parent??Object;class n extends i{}function s(e){var t;let i=r?.Parent?new n:this;for(let r of(a(i,e),(t=i._zod).deferred??(t.deferred=[]),i._zod.deferred))r();return i}return Object.defineProperty(n,"name",{value:e}),Object.defineProperty(s,"init",{value:a}),Object.defineProperty(s,Symbol.hasInstance,{value:t=>!!r?.Parent&&t instanceof r.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(s,"name",{value:e}),s}Symbol("zod_brand");class nr extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let na={};function ni(e){return e&&Object.assign(na,e),na}function nn(e,t){return"bigint"==typeof t?t.toString():t}let ns=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function no(e,t,r){let a=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(a._zod.parent=e),a}function nl(e,t=0){for(let r=t;r<e.issues.length;r++)if(!0!==e.issues[r].continue)return!0;return!1}function nu(e){return"string"==typeof e?e:e?.message}function nd(e,t,r){let a={...e,path:e.path??[]};return e.message||(a.message=nu(e.inst?._zod.def?.error?.(e))??nu(t?.error?.(e))??nu(r.customError?.(e))??nu(r.localeError?.(e))??"Invalid input"),delete a.inst,delete a.continue,t?.reportInput||delete a.input,a}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let nc=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,nn,2),enumerable:!0})},nh=nt("$ZodError",nc),np=nt("$ZodError",nc,{Parent:Error}),nf=(e,t,r,a)=>{let i=r?Object.assign(r,{async:!1}):{async:!1},n=e._zod.run({value:t,issues:[]},i);if(n instanceof Promise)throw new nr;if(n.issues.length){let e=new(a?.Err??np)(n.issues.map(e=>nd(e,i,ni())));throw ns(e,a?.callee),e}return n.value},nm=async(e,t,r,a)=>{let i=r?Object.assign(r,{async:!0}):{async:!0},n=e._zod.run({value:t,issues:[]},i);if(n instanceof Promise&&(n=await n),n.issues.length){let e=new(a?.Err??np)(n.issues.map(e=>nd(e,i,ni())));throw ns(e,a?.callee),e}return n.value},ng=(e,t,r)=>{let a=r?{...r,async:!1}:{async:!1},i=e._zod.run({value:t,issues:[]},a);if(i instanceof Promise)throw new nr;return i.issues.length?{success:!1,error:new(np??nh)(i.issues.map(e=>nd(e,a,ni())))}:{success:!0,data:i.value}},ny=async(e,t,r)=>{let a=r?Object.assign(r,{async:!0}):{async:!0},i=e._zod.run({value:t,issues:[]},a);return i instanceof Promise&&(i=await i),i.issues.length?{success:!1,error:new np(i.issues.map(e=>nd(e,a,ni())))}:{success:!0,data:i.value}},n_={major:4,minor:0,patch:0},nb=nt("$ZodType",(e,t)=>{var r;e??(e={}),function(e,t,r){Object.defineProperty(e,"id",{get(){{let t=r();return e.id=t,t}},set(r){Object.defineProperty(e,t,{value:r})},configurable:!0})}(e._zod,"id",()=>t.type+"_"+function(e=10){let t="abcdefghijklmnopqrstuvwxyz",r="";for(let a=0;a<e;a++)r+=t[Math.floor(Math.random()*t.length)];return r}(10)),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=n_;let a=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&a.unshift(e),a))for(let r of t._zod.onattach)r(e);if(0===a.length)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,r)=>{let a,i=nl(e);for(let n of t){if(n._zod.when){if(!n._zod.when(e))continue}else if(i)continue;let t=e.issues.length,s=n._zod.check(e);if(s instanceof Promise&&r?.async===!1)throw new nr;if(a||s instanceof Promise)a=(a??Promise.resolve()).then(async()=>{await s,e.issues.length!==t&&(i||(i=nl(e,t)))});else{if(e.issues.length===t)continue;i||(i=nl(e,t))}}return a?a.then(()=>e):e};e._zod.run=(r,i)=>{let n=e._zod.parse(r,i);if(n instanceof Promise){if(!1===i.async)throw new nr;return n.then(e=>t(e,a,i))}return t(n,a,i)}}e["~standard"]={validate:t=>{try{let r=ng(e,t);return r.success?{value:r.data}:{issues:r.error?.issues}}catch(r){return ny(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),nv=nt("$ZodNever",(e,t)=>{nb.init(e,t),e._zod.parse=(t,r)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function nw(e,t,r,a){let i=Math.abs(e),n=i%10,s=i%100;return s>=11&&s<=19?a:1===n?t:n>=2&&n<=4?r:a}let nE=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};function nO(e,t,r,a){let i=Math.abs(e),n=i%10,s=i%100;return s>=11&&s<=19?a:1===n?t:n>=2&&n<=4?r:a}let nx=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};Symbol("ZodOutput"),Symbol("ZodInput");class nT{constructor(){this._map=new WeakMap,this._idmap=new Map}add(e,...t){let r=t[0];if(this._map.set(e,r),r&&"object"==typeof r&&"id"in r){if(this._idmap.has(r.id))throw Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,e)}return this}remove(e){return this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let r={...this.get(t)??{}};return delete r.id,{...r,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let nS=new nT;class nk{constructor(e){this.counter=0,this.metadataRegistry=e?.metadata??nS,this.target=e?.target??"draft-2020-12",this.unrepresentable=e?.unrepresentable??"throw",this.override=e?.override??(()=>{}),this.io=e?.io??"output",this.seen=new Map}process(e,t={path:[],schemaPath:[]}){var r;let a=e._zod.def,i=this.seen.get(e);if(i)return i.count++,t.schemaPath.includes(e)&&(i.cycle=t.path),i.schema;let n={schema:{},count:1,cycle:void 0};this.seen.set(e,n);let s=e._zod.toJSONSchema?.();if(s)n.schema=s;else{let r={...t,schemaPath:[...t.schemaPath,e],path:t.path},i=e._zod.parent;if(i)n.ref=i,this.process(i,r),this.seen.get(i).isParent=!0;else{let t=n.schema;switch(a.type){case"string":{t.type="string";let{minimum:r,maximum:a,format:i,patterns:s,contentEncoding:o}=e._zod.bag;if("number"==typeof r&&(t.minLength=r),"number"==typeof a&&(t.maxLength=a),i&&(t.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[i]??i,""===t.format&&delete t.format),o&&(t.contentEncoding=o),s&&s.size>0){let e=[...s];1===e.length?t.pattern=e[0].source:e.length>1&&(n.schema.allOf=[...e.map(e=>({..."draft-7"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:r,maximum:a,format:i,multipleOf:n,exclusiveMaximum:s,exclusiveMinimum:o}=e._zod.bag;"string"==typeof i&&i.includes("int")?t.type="integer":t.type="number","number"==typeof o&&(t.exclusiveMinimum=o),"number"==typeof r&&(t.minimum=r,"number"==typeof o&&(o>=r?delete t.minimum:delete t.exclusiveMinimum)),"number"==typeof s&&(t.exclusiveMaximum=s),"number"==typeof a&&(t.maximum=a,"number"==typeof s&&(s<=a?delete t.maximum:delete t.exclusiveMaximum)),"number"==typeof n&&(t.multipleOf=n);break}case"boolean":case"success":t.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"undefined":case"null":t.type="null";break;case"any":case"unknown":break;case"never":t.not={};break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:i,maximum:n}=e._zod.bag;"number"==typeof i&&(t.minItems=i),"number"==typeof n&&(t.maxItems=n),t.type="array",t.items=this.process(a.element,{...r,path:[...r.path,"items"]});break}case"object":{t.type="object",t.properties={};let e=a.shape;for(let a in e)t.properties[a]=this.process(e[a],{...r,path:[...r.path,"properties",a]});let i=new Set([...new Set(Object.keys(e))].filter(e=>{let t=a.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));i.size>0&&(t.required=Array.from(i)),a.catchall?._zod.def.type==="never"?t.additionalProperties=!1:a.catchall?a.catchall&&(t.additionalProperties=this.process(a.catchall,{...r,path:[...r.path,"additionalProperties"]})):"output"===this.io&&(t.additionalProperties=!1);break}case"union":t.anyOf=a.options.map((e,t)=>this.process(e,{...r,path:[...r.path,"anyOf",t]}));break;case"intersection":{let e=this.process(a.left,{...r,path:[...r.path,"allOf",0]}),i=this.process(a.right,{...r,path:[...r.path,"allOf",1]}),n=e=>"allOf"in e&&1===Object.keys(e).length;t.allOf=[...n(e)?e.allOf:[e],...n(i)?i.allOf:[i]];break}case"tuple":{t.type="array";let i=a.items.map((e,t)=>this.process(e,{...r,path:[...r.path,"prefixItems",t]}));if("draft-2020-12"===this.target?t.prefixItems=i:t.items=i,a.rest){let e=this.process(a.rest,{...r,path:[...r.path,"items"]});"draft-2020-12"===this.target?t.items=e:t.additionalItems=e}a.rest&&(t.items=this.process(a.rest,{...r,path:[...r.path,"items"]}));let{minimum:n,maximum:s}=e._zod.bag;"number"==typeof n&&(t.minItems=n),"number"==typeof s&&(t.maxItems=s);break}case"record":t.type="object",t.propertyNames=this.process(a.keyType,{...r,path:[...r.path,"propertyNames"]}),t.additionalProperties=this.process(a.valueType,{...r,path:[...r.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=function(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,r])=>-1===t.indexOf(+e)).map(([e,t])=>t)}(a.entries);e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),t.enum=e;break}case"literal":{let e=[];for(let t of a.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let r=e[0];t.type=null===r?"null":typeof r,t.const=r}else e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),e.every(e=>"boolean"==typeof e)&&(t.type="string"),e.every(e=>null===e)&&(t.type="null"),t.enum=e;break}case"file":{let r={type:"string",format:"binary",contentEncoding:"binary"},{minimum:a,maximum:i,mime:n}=e._zod.bag;void 0!==a&&(r.minLength=a),void 0!==i&&(r.maxLength=i),n?1===n.length?(r.contentMediaType=n[0],Object.assign(t,r)):t.anyOf=n.map(e=>({...r,contentMediaType:e})):Object.assign(t,r);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":t.anyOf=[this.process(a.innerType,r),{type:"null"}];break;case"nonoptional":case"promise":case"optional":this.process(a.innerType,r),n.ref=a.innerType;break;case"default":this.process(a.innerType,r),n.ref=a.innerType,t.default=JSON.parse(JSON.stringify(a.defaultValue));break;case"prefault":this.process(a.innerType,r),n.ref=a.innerType,"input"===this.io&&(t._prefault=JSON.parse(JSON.stringify(a.defaultValue)));break;case"catch":{let e;this.process(a.innerType,r),n.ref=a.innerType;try{e=a.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}t.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let r=e._zod.pattern;if(!r)throw Error("Pattern not found in template literal");t.type="string",t.pattern=r.source;break}case"pipe":{let e="input"===this.io?"transform"===a.in._zod.def.type?a.out:a.in:a.out;this.process(e,r),n.ref=e;break}case"readonly":this.process(a.innerType,r),n.ref=a.innerType,t.readOnly=!0;break;case"lazy":{let t=e._zod.innerType;this.process(t,r),n.ref=t;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema")}}}let o=this.metadataRegistry.get(e);return o&&Object.assign(n.schema,o),"input"===this.io&&function e(t,r){let a=r??{seen:new Set};if(a.seen.has(t))return!1;a.seen.add(t);let i=t._zod.def;switch(i.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":return!1;case"array":return e(i.element,a);case"object":for(let t in i.shape)if(e(i.shape[t],a))return!0;return!1;case"union":for(let t of i.options)if(e(t,a))return!0;return!1;case"intersection":return e(i.left,a)||e(i.right,a);case"tuple":for(let t of i.items)if(e(t,a))return!0;if(i.rest&&e(i.rest,a))return!0;return!1;case"record":case"map":return e(i.keyType,a)||e(i.valueType,a);case"set":return e(i.valueType,a);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(i.innerType,a);case"lazy":return e(i.getter(),a);case"transform":return!0;case"pipe":return e(i.in,a)||e(i.out,a)}throw Error(`Unknown schema type: ${i.type}`)}(e)&&(delete n.schema.examples,delete n.schema.default),"input"===this.io&&n.schema._prefault&&((r=n.schema).default??(r.default=n.schema._prefault)),delete n.schema._prefault,this.seen.get(e).schema}emit(e,t){let r={cycles:t?.cycles??"ref",reused:t?.reused??"inline",external:t?.external??void 0},a=this.seen.get(e);if(!a)throw Error("Unprocessed schema. This is a bug in Zod.");let i=e=>{let t="draft-2020-12"===this.target?"$defs":"definitions";if(r.external){let a=r.external.registry.get(e[0])?.id;if(a)return{ref:r.external.uri(a)};let i=e[1].defId??e[1].schema.id??`schema${this.counter++}`;return e[1].defId=i,{defId:i,ref:`${r.external.uri("__shared")}#/${t}/${i}`}}if(e[1]===a)return{ref:"#"};let i=`#/${t}/`,n=e[1].schema.id??`__schema${this.counter++}`;return{defId:n,ref:i+n}},n=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:r,defId:a}=i(e);t.def={...t.schema},a&&(t.defId=a);let n=t.schema;for(let e in n)delete n[e];n.$ref=r};for(let t of this.seen.entries()){let a=t[1];if(e===t[0]){n(t);continue}if(r.external){let a=r.external.registry.get(t[0])?.id;if(e!==t[0]&&a){n(t);continue}}if(this.metadataRegistry.get(t[0])?.id){n(t);continue}if(a.cycle){if("throw"===r.cycles)throw Error(`Cycle detected: #/${a.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`);"ref"===r.cycles&&n(t);continue}if(a.count>1&&"ref"===r.reused){n(t);continue}}let s=(e,t)=>{let r=this.seen.get(e),a=r.def??r.schema,i={...a};if(null===r.ref)return;let n=r.ref;if(r.ref=null,n){s(n,t);let e=this.seen.get(n).schema;e.$ref&&"draft-7"===t.target?(a.allOf=a.allOf??[],a.allOf.push(e)):(Object.assign(a,e),Object.assign(a,i))}r.isParent||this.override({zodSchema:e,jsonSchema:a})};for(let e of[...this.seen.entries()].reverse())s(e[0],{target:this.target});let o={};"draft-2020-12"===this.target?o.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?o.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),Object.assign(o,a.def);let l=r.external?.defs??{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(l[t.defId]=t.def)}!r.external&&Object.keys(l).length>0&&("draft-2020-12"===this.target?o.$defs=l:o.definitions=l);try{return JSON.parse(JSON.stringify(o))}catch(e){throw Error("Error converting schema to JSON.")}}}function nA(e,t){if(e instanceof nT){let r=new nk(t),a={};for(let t of e._idmap.entries()){let[e,a]=t;r.process(a)}let i={},n={registry:e,uri:t?.uri||(e=>e),defs:a};for(let a of e._idmap.entries()){let[e,s]=a;i[e]=r.emit(s,{...t,external:n})}return Object.keys(a).length>0&&(i.__shared={["draft-2020-12"===r.target?"$defs":"definitions"]:a}),{schemas:i}}let r=new nk(t);return r.process(e),r.emit(e,t)}let nI=Symbol("Let zodToJsonSchema decide on which parser to use"),nC={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},nP=e=>"string"==typeof e?{...nC,name:e}:{...nC,...e},nj=e=>{let t=nP(e),r=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,r])=>[r._def,{def:r._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}},nR=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")};function n$(e){if("openAi"!==e.target)return{};let t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===e.$refStrategy?nR(t,e.currentPath):t.join("/")}}function nN(e,t,r,a){a?.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function nL(e,t,r,a,i){e[t]=r,nN(e,t,a,i)}function nM(e,t){return n1(e.type._def,t)}let nU=(e,t)=>n1(e.innerType._def,t),nD=(e,t)=>{let r={type:"integer",format:"unix-time"};if("openApi3"===t.target)return r;for(let a of e.checks)switch(a.kind){case"min":nL(r,"minimum",a.value,a.message,t);break;case"max":nL(r,"maximum",a.value,a.message,t)}return r},nF=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,nz={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===d&&(d=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),d),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function nB(e,t){let r={type:"string"};if(e.checks)for(let a of e.checks)switch(a.kind){case"min":nL(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t);break;case"max":nL(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"email":switch(t.emailStrategy){case"format:email":nH(r,"email",a.message,t);break;case"format:idn-email":nH(r,"idn-email",a.message,t);break;case"pattern:zod":nq(r,nz.email,a.message,t)}break;case"url":nH(r,"uri",a.message,t);break;case"uuid":nH(r,"uuid",a.message,t);break;case"regex":nq(r,a.regex,a.message,t);break;case"cuid":nq(r,nz.cuid,a.message,t);break;case"cuid2":nq(r,nz.cuid2,a.message,t);break;case"startsWith":nq(r,RegExp(`^${nZ(a.value,t)}`),a.message,t);break;case"endsWith":nq(r,RegExp(`${nZ(a.value,t)}$`),a.message,t);break;case"datetime":nH(r,"date-time",a.message,t);break;case"date":nH(r,"date",a.message,t);break;case"time":nH(r,"time",a.message,t);break;case"duration":nH(r,"duration",a.message,t);break;case"length":nL(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t),nL(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"includes":nq(r,RegExp(nZ(a.value,t)),a.message,t);break;case"ip":"v6"!==a.version&&nH(r,"ipv4",a.message,t),"v4"!==a.version&&nH(r,"ipv6",a.message,t);break;case"base64url":nq(r,nz.base64url,a.message,t);break;case"jwt":nq(r,nz.jwt,a.message,t);break;case"cidr":"v6"!==a.version&&nq(r,nz.ipv4Cidr,a.message,t),"v4"!==a.version&&nq(r,nz.ipv6Cidr,a.message,t);break;case"emoji":nq(r,nz.emoji(),a.message,t);break;case"ulid":nq(r,nz.ulid,a.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":nH(r,"binary",a.message,t);break;case"contentEncoding:base64":nL(r,"contentEncoding","base64",a.message,t);break;case"pattern:zod":nq(r,nz.base64,a.message,t)}break;case"nanoid":nq(r,nz.nanoid,a.message,t)}return r}function nZ(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let r=0;r<e.length;r++)nG.has(e[r])||(t+="\\"),t+=e[r];return t}(e):e}let nG=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function nH(e,t,r,a){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&a.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&a.errorMessages&&{errorMessage:{format:r}}})):nL(e,"format",t,r,a)}function nq(e,t,r,a){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&a.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:nJ(t,a),...r&&a.errorMessages&&{errorMessage:{pattern:r}}})):nL(e,"pattern",nJ(t,a),r,a)}function nJ(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},a=r.i?e.source.toLowerCase():e.source,i="",n=!1,s=!1,o=!1;for(let e=0;e<a.length;e++){if(n){i+=a[e],n=!1;continue}if(r.i){if(s){if(a[e].match(/[a-z]/)){o?(i+=a[e],i+=`${a[e-2]}-${a[e]}`.toUpperCase(),o=!1):"-"===a[e+1]&&a[e+2]?.match(/[a-z]/)?(i+=a[e],o=!0):i+=`${a[e]}${a[e].toUpperCase()}`;continue}}else if(a[e].match(/[a-z]/)){i+=`[${a[e]}${a[e].toUpperCase()}]`;continue}}if(r.m){if("^"===a[e]){i+=`(^|(?<=[\r
]))`;continue}else if("$"===a[e]){i+=`($|(?=[\r
]))`;continue}}if(r.s&&"."===a[e]){i+=s?`${a[e]}\r
`:`[${a[e]}\r
]`;continue}i+=a[e],"\\"===a[e]?n=!0:s&&"]"===a[e]?s=!1:s||"["!==a[e]||(s=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function nW(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===S.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((r,a)=>({...r,[a]:n1(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",a]})??n$(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};let r={type:"object",additionalProperties:n1(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return r;if(e.keyType?._def.typeName===S.ZodString&&e.keyType._def.checks?.length){let{type:a,...i}=nB(e.keyType._def,t);return{...r,propertyNames:i}}if(e.keyType?._def.typeName===S.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===S.ZodBranded&&e.keyType._def.type._def.typeName===S.ZodString&&e.keyType._def.type._def.checks?.length){let{type:a,...i}=nM(e.keyType._def,t);return{...r,propertyNames:i}}return r}let nV={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},nK=(e,t)=>{let r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,r)=>n1(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${r}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return r.length?{anyOf:r}:void 0},nY=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return n1(e.innerType._def,t);let r=n1(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:n$(t)},r]}:n$(t)},nX=(e,t)=>{if("input"===t.pipeStrategy)return n1(e.in._def,t);if("output"===t.pipeStrategy)return n1(e.out._def,t);let r=n1(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),a=n1(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,a].filter(e=>void 0!==e)}},nQ=(e,t)=>n1(e.innerType._def,t),n0=(e,t,r)=>{switch(t){case S.ZodString:return nB(e,r);case S.ZodNumber:return function(e,t){let r={type:"number"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"int":r.type="integer",nN(r,"type",a.message,t);break;case"min":"jsonSchema7"===t.target?a.inclusive?nL(r,"minimum",a.value,a.message,t):nL(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),nL(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?nL(r,"maximum",a.value,a.message,t):nL(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),nL(r,"maximum",a.value,a.message,t));break;case"multipleOf":nL(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case S.ZodObject:return function(e,t){let r="openAi"===t.target,a={type:"object",properties:{}},i=[],n=e.shape();for(let e in n){let s=n[e];if(void 0===s||void 0===s._def)continue;let o=function(e){try{return e.isOptional()}catch{return!0}}(s);o&&r&&("ZodOptional"===s._def.typeName&&(s=s._def.innerType),s.isNullable()||(s=s.nullable()),o=!1);let l=n1(s._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==l&&(a.properties[e]=l,o||i.push(e))}i.length&&(a.required=i);let s=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return n1(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==s&&(a.additionalProperties=s),a}(e,r);case S.ZodBigInt:return function(e,t){let r={type:"integer",format:"int64"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"min":"jsonSchema7"===t.target?a.inclusive?nL(r,"minimum",a.value,a.message,t):nL(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),nL(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?nL(r,"maximum",a.value,a.message,t):nL(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),nL(r,"maximum",a.value,a.message,t));break;case"multipleOf":nL(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case S.ZodBoolean:return{type:"boolean"};case S.ZodDate:return function e(t,r,a){let i=a??r.dateStrategy;if(Array.isArray(i))return{anyOf:i.map((a,i)=>e(t,r,a))};switch(i){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return nD(t,r)}}(e,r);case S.ZodUndefined:return{not:n$(r)};case S.ZodNull:return"openApi3"===r.target?{enum:["null"],nullable:!0}:{type:"null"};case S.ZodArray:return function(e,t){let r={type:"array"};return e.type?._def&&e.type?._def?.typeName!==S.ZodAny&&(r.items=n1(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&nL(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&nL(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(nL(r,"minItems",e.exactLength.value,e.exactLength.message,t),nL(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}(e,r);case S.ZodUnion:case S.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return nK(e,t);let r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(e=>e._def.typeName in nV&&(!e._def.checks||!e._def.checks.length))){let e=r.reduce((e,t)=>{let r=nV[t._def.typeName];return r&&!e.includes(r)?[...e,r]:e},[]);return{type:e.length>1?e:e[0]}}if(r.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=r.reduce((e,t)=>{let r=typeof t._def.value;switch(r){case"string":case"number":case"boolean":return[...e,r];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===r.length){let t=e.filter((e,t,r)=>r.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:r.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(r.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:r.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return nK(e,t)}(e,r);case S.ZodIntersection:return function(e,t){let r=[n1(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),n1(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e),a="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0,i=[];return r.forEach(e=>{if(nF(e))i.push(...e.allOf),void 0===e.unevaluatedProperties&&(a=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:r,...a}=e;t=a}else a=void 0;i.push(t)}}),i.length?{allOf:i,...a}:void 0}(e,r);case S.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,r)=>n1(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:n1(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,r)=>n1(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,r);case S.ZodRecord:return nW(e,r);case S.ZodLiteral:return function(e,t){let r=typeof e.value;return"bigint"!==r&&"number"!==r&&"boolean"!==r&&"string"!==r?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===r?"integer":r,enum:[e.value]}:{type:"bigint"===r?"integer":r,const:e.value}}(e,r);case S.ZodEnum:return{type:"string",enum:Array.from(e.values)};case S.ZodNativeEnum:return function(e){let t=e.values,r=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),a=Array.from(new Set(r.map(e=>typeof e)));return{type:1===a.length?"string"===a[0]?"string":"number":["string","number"],enum:r}}(e);case S.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:nV[e.innerType._def.typeName],nullable:!0}:{type:[nV[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){let r=n1(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}let r=n1(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}(e,r);case S.ZodOptional:return nY(e,r);case S.ZodMap:return function(e,t){return"record"===t.mapStrategy?nW(e,t):{type:"array",maxItems:125,items:{type:"array",items:[n1(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||n$(t),n1(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||n$(t)],minItems:2,maxItems:2}}}(e,r);case S.ZodSet:return function(e,t){let r={type:"array",uniqueItems:!0,items:n1(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&nL(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&nL(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}(e,r);case S.ZodLazy:return()=>e.getter()._def;case S.ZodPromise:return n1(e.type._def,r);case S.ZodNaN:case S.ZodNever:return function(e){return"openAi"===e.target?void 0:{not:n$({...e,currentPath:[...e.currentPath,"not"]})}}(r);case S.ZodEffects:return function(e,t){return"input"===t.effectStrategy?n1(e.schema._def,t):n$(t)}(e,r);case S.ZodAny:case S.ZodUnknown:return n$(r);case S.ZodDefault:return function(e,t){return{...n1(e.innerType._def,t),default:e.defaultValue()}}(e,r);case S.ZodBranded:return nM(e,r);case S.ZodReadonly:return nQ(e,r);case S.ZodCatch:return nU(e,r);case S.ZodPipeline:return nX(e,r);case S.ZodFunction:case S.ZodVoid:case S.ZodSymbol:default:return}};function n1(e,t,r=!1){let a=t.seen.get(e);if(t.override){let i=t.override?.(e,t,a,r);if(i!==nI)return i}if(a&&!r){let e=n2(a,t);if(void 0!==e)return e}let i={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,i);let n=n0(e,e.typeName,t),s="function"==typeof n?n1(n(),t):n;if(s&&n4(e,t,s),t.postProcess){let r=t.postProcess(s,e,t);return i.jsonSchema=s,r}return i.jsonSchema=s,s}let n2=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:nR(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,r)=>t.currentPath[r]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),n$(t);return"seen"===t.$refStrategy?n$(t):void 0}},n4=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),n5=(e,t)=>{let r=nj(t),a="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,a])=>({...e,[t]:n1(a._def,{...r,currentPath:[...r.basePath,r.definitionPath,t]},!0)??n$(r)}),{}):void 0,i="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,n=n1(e._def,void 0===i?r:{...r,currentPath:[...r.basePath,r.definitionPath,i]},!1)??n$(r),s="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==s&&(n.title=s),r.flags.hasReferencedOpenAiAnyType&&(a||(a={}),a[r.openAiAnyTypeName]||(a[r.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===r.$refStrategy?"1":[...r.basePath,r.definitionPath,r.openAiAnyTypeName].join("/")}}));let o=void 0===i?a?{...n,[r.definitionPath]:a}:n:{$ref:[..."relative"===r.$refStrategy?[]:r.basePath,r.definitionPath,i].join("/"),[r.definitionPath]:{...a,[i]:n}};return"jsonSchema7"===r.target?o.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===r.target||"openAi"===r.target)&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===r.target&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};"undefined"!=typeof self&&self.location&&"null"!==self.location.origin?new URL(self.location.origin+self.location.pathname+location.search):new URL("https://github.com/cfworker");let n3=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function n9(e){return e.test.bind(e)}(function(e,t){let r=t.match(n3);if(!r)return!1;let a=+r[1],i=+r[2],n=+r[3],s=!!r[5];return(a<=23&&i<=59&&n<=59||23==a&&59==i&&60==n)&&(!e||s)}).bind(void 0,!1),n9(/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i),n9(/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i),n9(/^(?:(?:https?|ftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!10(?:\.\d{1,3}){3})(?!127(?:\.\d{1,3}){3})(?!169\.254(?:\.\d{1,3}){2})(?!192\.168(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)(?:\.(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu),n9(/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i),n9(/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/),n9(/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i),n9(/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i),n9(/^(?:\/(?:[^~/]|~0|~1)*)*$/),n9(/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i),n9(/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/);function n6(e){if("object"!=typeof e||null===e||!("_zod"in e))return!1;let t=e._zod;return"object"==typeof t&&null!==t&&"def"in t}function n8(e){if("object"!=typeof e||null===e||!("_def"in e)||"_zod"in e)return!1;let t=e._def;return"object"==typeof t&&null!=t&&"typeName"in t}function n7(e){return!(!e||"object"!=typeof e||Array.isArray(e))&&!!(n6(e)||n8(e))}async function se(e,t){if(n6(e))try{let r=await nm(e,t);return{success:!0,data:r}}catch(e){return{success:!1,error:e}}if(n8(e))return e.safeParse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}async function st(e,t){if(n6(e))return nf(e,t);if(n8(e))return e.parse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}function sr(e){return n6(e)?nS.get(e)?.description:n8(e)||"description"in e&&"string"==typeof e.description?e.description:void 0}!function(e){e[e.Flag=1]="Flag",e[e.Basic=2]="Basic",e[e.Detailed=4]="Detailed"}(k||(k={}));function sa(e){return!!n6(e)&&("object"==typeof e&&null!==e&&"_zod"in e&&"object"==typeof e._zod&&null!==e._zod&&"def"in e._zod&&"object"==typeof e._zod.def&&null!==e._zod.def&&"type"in e._zod.def&&"object"===e._zod.def.type||!1)}function si(e){return!!n6(e)&&("object"==typeof e&&null!==e&&"_zod"in e&&"object"==typeof e._zod&&null!==e._zod&&"def"in e._zod&&"object"==typeof e._zod.def&&null!==e._zod.def&&"type"in e._zod.def&&"array"===e._zod.def.type||!1)}function sn(e){if(n6(e)){let t=function e(t,r=!1){if(n8(t))return n8(t)&&"typeName"in t._def&&"ZodEffects"===t._def.typeName?e(t._def.schema,r):t;if(n6(t)){let a=t;if(n6(t)&&"pipe"===t._zod.def.type&&(a=e(t._zod.def.in,r)),r){if(sa(a)){let t=a._zod.def.shape;for(let[i,n]of Object.entries(a._zod.def.shape))t[i]=e(n,r);a=no(a,{...a._zod.def,shape:t})}else if(si(a)){let t=e(a._zod.def.element,r);a=no(a,{...a._zod.def,element:t})}}let i=nS.get(t);return i&&nS.add(a,i),a}throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}(e,!0);return sa(t)?nA(function e(t,r=!1){if(n8(t))return t.strict();if(sa(t)){let a=t._zod.def.shape;if(r)for(let[i,n]of Object.entries(t._zod.def.shape)){if(sa(n)){let t=e(n,r);a[i]=t}else if(si(n)){let t=n._zod.def.element;sa(t)&&(t=e(t,r)),a[i]=no(n,{...n._zod.def,element:t})}else a[i]=n;let t=nS.get(n);t&&nS.add(a[i],t)}let i=no(t,{...t._zod.def,shape:a,catchall:new nv({type:"never",...function(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}(void 0)})}),n=nS.get(t);return n&&nS.add(i,n),i}throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")}(t,!0)):nA(e)}return n8(e)?n5(e):e}class ss{constructor(e){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.nodes=e?.nodes??this.nodes,this.edges=e?.edges??this.edges}toJSON(){let e={};return Object.values(this.nodes).forEach((t,r)=>{e[t.id]=i6(t.id)?r:t.id}),{nodes:Object.values(this.nodes).map(t=>({id:e[t.id],...i5(t.data)?{type:"runnable",data:{id:t.data.lc_id,name:t.data.getName()}}:{type:"schema",data:{...sn(t.data.schema),title:t.data.name}}})),edges:this.edges.map(t=>{let r={source:e[t.source],target:e[t.target]};return void 0!==t.data&&(r.data=t.data),void 0!==t.conditional&&(r.conditional=t.conditional),r})}}addNode(e,t,r){if(void 0!==t&&void 0!==this.nodes[t])throw Error(`Node with id ${t} already exists`);let a=t??ru(),i={id:a,data:e,name:function(e,t){if(void 0!==e&&!i6(e))return e;if(!i5(t))return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}(t,e),metadata:r};return this.nodes[a]=i,i}removeNode(e){delete this.nodes[e.id],this.edges=this.edges.filter(t=>t.source!==e.id&&t.target!==e.id)}addEdge(e,t,r,a){if(void 0===this.nodes[e.id])throw Error(`Source node ${e.id} not in graph`);if(void 0===this.nodes[t.id])throw Error(`Target node ${t.id} not in graph`);let i={source:e.id,target:t.id,data:r,conditional:a};return this.edges.push(i),i}firstNode(){return so(this)}lastNode(){return sl(this)}extend(e,t=""){let r=t;Object.values(e.nodes).map(e=>e.id).every(i6)&&(r="");let a=e=>r?`${r}:${e}`:e;Object.entries(e.nodes).forEach(([e,t])=>{this.nodes[a(e)]={...t,id:a(e)}});let i=e.edges.map(e=>({...e,source:a(e.source),target:a(e.target)}));this.edges=[...this.edges,...i];let n=e.firstNode(),s=e.lastNode();return[n?{id:a(n.id),data:n.data}:void 0,s?{id:a(s.id),data:s.data}:void 0]}trimFirstNode(){let e=this.firstNode();e&&so(this,[e.id])&&this.removeNode(e)}trimLastNode(){let e=this.lastNode();e&&sl(this,[e.id])&&this.removeNode(e)}reid(){let e=Object.fromEntries(Object.values(this.nodes).map(e=>[e.id,e.name])),t=new Map;Object.values(e).forEach(e=>{t.set(e,(t.get(e)||0)+1)});let r=r=>{let a=e[r];return i6(r)&&1===t.get(a)?a:r};return new ss({nodes:Object.fromEntries(Object.entries(this.nodes).map(([e,t])=>[r(e),{...t,id:r(e)}])),edges:this.edges.map(e=>({...e,source:r(e.source),target:r(e.target)}))})}drawMermaid(e){let{withStyles:t,curveStyle:r,nodeColors:a={default:"fill:#f2f0ff,line-height:1.2",first:"fill-opacity:0",last:"fill:#bfb6fc"},wrapLabelNWords:i}=e??{},n=this.reid(),s=n.firstNode(),o=n.lastNode();return function(e,t,r){let{firstNode:a,lastNode:i,nodeColors:n,withStyles:s=!0,curveStyle:o="linear",wrapLabelNWords:l=9}=r??{},u=s?`%%{init: {'flowchart': {'curve': '${o}'}}}%%
graph TD;
`:"graph TD;\n";if(s){let t="default",r={[t]:"{0}({1})"};for(let[n,s]of(void 0!==a&&(r[a]="{0}([{1}]):::first"),void 0!==i&&(r[i]="{0}([{1}]):::last"),Object.entries(e))){let e=s.name.split(":").pop()??"",a=i7.some(t=>e.startsWith(t)&&e.endsWith(t))?`<p>${e}</p>`:e;Object.keys(s.metadata??{}).length&&(a+=`<hr/><small><em>${Object.entries(s.metadata??{}).map(([e,t])=>`${e} = ${t}`).join("\n")}</em></small>`);let i=(r[n]??r[t]).replace("{0}",i8(n)).replace("{1}",a);u+=`	${i}
`}}let d={};for(let e of t){let t=e.source.split(":"),r=e.target.split(":"),a=t.filter((e,t)=>e===r[t]).join(":");d[a]||(d[a]=[]),d[a].push(e)}let c=new Set;function h(e,t){let r=1===e.length&&e[0].source===e[0].target;if(t&&!r){let e=t.split(":").pop();if(c.has(e))throw Error(`Found duplicate subgraph '${e}' -- this likely means that you're reusing a subgraph node with the same name. Please adjust your graph to have subgraph nodes with unique names.`);c.add(e),u+=`	subgraph ${e}
`}for(let t of e){let{source:e,target:r,data:a,conditional:i}=t,n="";if(void 0!==a){let e=a,t=e.split(" ");t.length>l&&(e=Array.from({length:Math.ceil(t.length/l)},(e,r)=>t.slice(r*l,(r+1)*l).join(" ")).join("&nbsp;<br>&nbsp;")),n=i?` -. &nbsp;${e}&nbsp; .-> `:` -- &nbsp;${e}&nbsp; --> `}else n=i?" -.-> ":" --\x3e ";u+=`	${i8(e)}${n}${i8(r)};
`}for(let e in d)e.startsWith(`${t}:`)&&e!==t&&h(d[e],e);t&&!r&&(u+="	end\n")}for(let e in h(d[""]??[],""),d)e.includes(":")||""===e||h(d[e],e);return s&&(u+=function(e){let t="";for(let[r,a]of Object.entries(e))t+=`	classDef ${r} ${a};
`;return t}(n??{})),u}(n.nodes,n.edges,{firstNode:s?.id,lastNode:o?.id,withStyles:t,curveStyle:r,nodeColors:a,wrapLabelNWords:i})}async drawMermaidPng(e){return ne(this.drawMermaid(e),{backgroundColor:e?.backgroundColor})}}function so(e,t=[]){let r=new Set(e.edges.filter(e=>!t.includes(e.source)).map(e=>e.target)),a=[];for(let i of Object.values(e.nodes))t.includes(i.id)||r.has(i.id)||a.push(i);return 1===a.length?a[0]:void 0}function sl(e,t=[]){let r=new Set(e.edges.filter(e=>!t.includes(e.target)).map(e=>e.source)),a=[];for(let i of Object.values(e.nodes))t.includes(i.id)||r.has(i.id)||a.push(i);return 1===a.length?a[0]:void 0}function su(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.iterator]&&"function"==typeof e.next}let sd=e=>null!=e&&"object"==typeof e&&"next"in e&&"function"==typeof e.next;function sc(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.asyncIterator]}function*sh(e,t){for(;;){let{value:r,done:a}=iI.runWithConfig(iN(e),t.next.bind(t),!0);if(a)break;yield r}}async function*sp(e,t){let r=t[Symbol.asyncIterator]();for(;;){let{value:a,done:i}=await iI.runWithConfig(iN(e),r.next.bind(t),!0);if(i)break;yield a}}function sf(e,t){return!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{[t]:e}:e}class sm extends eO{constructor(){super(...arguments),Object.defineProperty(this,"lc_runnable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}getName(e){let t=this.name??this.constructor.lc_name()??this.constructor.name;return e?`${t}${e}`:t}bind(e){return new sg({bound:this,kwargs:e,config:{}})}map(){return new sy({bound:this})}withRetry(e){return new s_({bound:this,kwargs:{},config:{},maxAttemptNumber:e?.stopAfterAttempt,...e})}withConfig(e){return new sg({bound:this,config:e,kwargs:{}})}withFallbacks(e){return new sO({runnable:this,fallbacks:Array.isArray(e)?e:e.fallbacks})}_getOptionsList(e,t=0){if(Array.isArray(e)&&e.length!==t)throw Error(`Passed "options" must be an array with the same length as the inputs, but got ${e.length} options for ${t} inputs`);if(Array.isArray(e))return e.map(iR);if(t>1&&!Array.isArray(e)&&e.runId){console.warn("Provided runId will be used only for the first element of the batch.");let r=Object.fromEntries(Object.entries(e).filter(([e])=>"runId"!==e));return Array.from({length:t},(t,a)=>iR(0===a?e:r))}return Array.from({length:t},()=>iR(e))}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=new i2({maxConcurrency:a[0]?.maxConcurrency??r?.maxConcurrency,onFailedAttempt:e=>{throw e}});return Promise.all(e.map((e,t)=>i.call(async()=>{try{return await this.invoke(e,a[t])}catch(e){if(r?.returnExceptions)return e;throw e}})))}async *_streamIterator(e,t){yield this.invoke(e,t)}async stream(e,t){let r=iR(t),a=new iF({generator:this._streamIterator(e,r),config:r});return await a.setup,iM.fromAsyncGenerator(a)}_separateRunnableConfigFromCallOptions(e){let t;t=void 0===e?iR(e):iR({callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,runName:e.runName,configurable:e.configurable,recursionLimit:e.recursionLimit,maxConcurrency:e.maxConcurrency,runId:e.runId,timeout:e.timeout,signal:e.signal});let r={...e};return delete r.callbacks,delete r.tags,delete r.metadata,delete r.runName,delete r.configurable,delete r.recursionLimit,delete r.maxConcurrency,delete r.runId,delete r.timeout,delete r.signal,[t,r]}async _callWithConfig(e,t,r){let a,i=iR(r),n=await iC(i),s=await n?.handleChainStart(this.toJSON(),sf(t,"input"),i.runId,i?.runType,void 0,void 0,i?.runName??this.getName());delete i.runId;try{let n=e.call(this,t,i,s);a=await iL(n,r?.signal)}catch(e){throw await s?.handleChainError(e),e}return await s?.handleChainEnd(sf(a,"output")),a}async _batchWithConfig(e,t,r,a){let i,n=this._getOptionsList(r??{},t.length),s=await Promise.all(n.map(iC)),o=await Promise.all(s.map(async(e,r)=>{let a=await e?.handleChainStart(this.toJSON(),sf(t[r],"input"),n[r].runId,n[r].runType,void 0,void 0,n[r].runName??this.getName());return delete n[r].runId,a}));try{let r=e.call(this,t,n,o,a);i=await iL(r,n?.[0]?.signal)}catch(e){throw await Promise.all(o.map(t=>t?.handleChainError(e))),e}return await Promise.all(o.map(e=>e?.handleChainEnd(sf(i,"output")))),i}async *_transformStreamWithConfig(e,t,r){let a,i,n,s=!0,o=!0,l=iR(r),u=await iC(l);async function*d(){for await(let t of e){if(s)if(void 0===a)a=t;else try{a=iD(a,t)}catch{a=void 0,s=!1}yield t}}try{let e=await iz(t.bind(this),d(),async()=>u?.handleChainStart(this.toJSON(),{input:""},l.runId,l.runType,void 0,void 0,l.runName??this.getName()),r?.signal,l);delete l.runId,n=e.setup;let a=n?.handlers.find(iX),s=e.output;void 0!==a&&void 0!==n&&(s=a.tapOutputIterable(n.runId,s));let c=n?.handlers.find(iG);for await(let e of(void 0!==c&&void 0!==n&&(s=c.tapOutputIterable(n.runId,s)),s))if(yield e,o)if(void 0===i)i=e;else try{i=iD(i,e)}catch{i=void 0,o=!1}}catch(e){throw await n?.handleChainError(e,void 0,void 0,void 0,{inputs:sf(a,"input")}),e}await n?.handleChainEnd(i??{},void 0,void 0,void 0,{inputs:sf(a,"input")})}getGraph(e){let t=new ss,r=t.addNode({name:`${this.getName()}Input`,schema:re()}),a=t.addNode(this),i=t.addNode({name:`${this.getName()}Output`,schema:re()});return t.addEdge(r,a),t.addEdge(a,i),t}pipe(e){return new sb({first:this,last:sx(e)})}pick(e){return this.pipe(new sS(e))}assign(e){return this.pipe(new sT(new sv({steps:e})))}async *transform(e,t){let r;for await(let t of e)r=void 0===r?t:iD(r,t);yield*this._streamIterator(r,iR(t))}async *streamLog(e,t,r){let a=new iJ({...r,autoClose:!1,_schemaFormat:"original"}),i=iR(t);yield*this._streamLog(e,a,i)}async *_streamLog(e,t,r){let{callbacks:a}=r;if(void 0===a)r.callbacks=[t];else if(Array.isArray(a))r.callbacks=a.concat([t]);else{let e=a.copy();e.addHandler(t,!0),r.callbacks=e}let i=this.stream(e,r),n=async function(){try{for await(let e of(await i)){let r=new iB({ops:[{op:"add",path:"/streamed_output/-",value:e}]});await t.writer.write(r)}}finally{await t.writer.close()}}();try{for await(let e of t)yield e}finally{await n}}streamEvents(e,t,r){let a;if("v1"===t.version)a=this._streamEventsV1(e,t,r);else if("v2"===t.version)a=this._streamEventsV2(e,t,r);else throw Error('Only versions "v1" and "v2" of the schema are currently supported.');if("text/event-stream"!==t.encoding)return iM.fromAsyncGenerator(a);var i=a;let n=new TextEncoder,s=new ReadableStream({async start(e){for await(let t of i)e.enqueue(n.encode(`event: data
data: ${JSON.stringify(t)}

`));e.enqueue(n.encode("event: end\n\n")),e.close()}});return iM.fromReadableStream(s)}async *_streamEventsV2(e,t,r){let a,i=new iQ({...r,autoClose:!1}),n=iR(t),s=n.runId??ru();n.runId=s;let o=n.callbacks;if(void 0===o)n.callbacks=[i];else if(Array.isArray(o))n.callbacks=o.concat(i);else{let e=o.copy();e.addHandler(i,!0),n.callbacks=e}let l=new AbortController,u=this,d=async function(){try{let r;t?.signal?"any"in AbortSignal?r=AbortSignal.any([l.signal,t.signal]):(r=t.signal,t.signal.addEventListener("abort",()=>{l.abort()},{once:!0})):r=l.signal;let a=await u.stream(e,{...n,signal:r});for await(let e of i.tapOutputIterable(s,a))if(l.signal.aborted)break}finally{await i.finish()}}(),c=!1;try{for await(let t of i){if(!c){t.data.input=e,c=!0,a=t.run_id,yield t;continue}t.run_id===a&&t.event.endsWith("_end")&&t.data?.input&&delete t.data.input,yield t}}finally{l.abort(),await d}}async *_streamEventsV1(e,t,r){let a,i=!1,n=iR(t),s=n.tags??[],o=n.metadata??{},l=n.runName??this.getName(),u=new iJ({...r,autoClose:!1,_schemaFormat:"streaming_events"}),d=new i3({...r});for await(let t of this._streamLog(e,u,n)){if(void 0===(a=a?a.concat(t):iZ.fromRunLogPatch(t)).state)throw Error('Internal error: "streamEvents" state is missing. Please open a bug report.');if(!i){i=!0;let t={...a.state},r={run_id:t.id,event:`on_${t.type}_start`,name:l,tags:s,metadata:o,data:{input:e}};d.includeEvent(r,t.type)&&(yield r)}for(let e of[...new Set(t.ops.filter(e=>e.path.startsWith("/logs/")).map(e=>e.path.split("/")[2]))]){let t,r={},i=a.state.logs[e];if("start"==(t=void 0===i.end_time?i.streamed_output.length>0?"stream":"start":"end"))void 0!==i.inputs&&(r.input=i.inputs);else if("end"===t)void 0!==i.inputs&&(r.input=i.inputs),r.output=i.final_output;else if("stream"===t){let e=i.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${i.name}"`);r={chunk:i.streamed_output[0]},i.streamed_output=[]}yield{event:`on_${i.type}_${t}`,name:i.name,run_id:i.id,tags:i.tags,metadata:i.metadata,data:r}}let{state:r}=a;if(r.streamed_output.length>0){let e=r.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${r.name}"`);let t={chunk:r.streamed_output[0]};r.streamed_output=[];let a={event:`on_${r.type}_stream`,run_id:r.id,tags:s,metadata:o,name:l,data:t};d.includeEvent(a,r.type)&&(yield a)}}let c=a?.state;if(void 0!==c){let e={event:`on_${c.type}_end`,name:l,run_id:c.id,tags:s,metadata:o,data:{output:c.final_output}};d.includeEvent(e,c.type)&&(yield e)}}static isRunnable(e){return i5(e)}withListeners({onStart:e,onEnd:t,onError:r}){return new sg({bound:this,config:{},configFactories:[a=>({callbacks:[new i4({config:a,onStart:e,onEnd:t,onError:r})]})]})}asTool(e){return function(e,t){var r;let a=t.name??e.getName(),i=t.description??sr(t.schema);return new sk(n7(r=t.schema)&&(n8(r)?"ZodString"===r._def.typeName:!!n6(r)&&"string"===r._zod.def.type)?{name:a,description:i,schema:rt({input:t7()}).transform(e=>e.input),bound:e}:{name:a,description:i,schema:t.schema,bound:e})}(this,e)}}class sg extends sm{static lc_name(){return"RunnableBinding"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"configFactories",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound,this.kwargs=e.kwargs,this.config=e.config,this.configFactories=e.configFactories}getName(e){return this.bound.getName(e)}async _mergeConfig(...e){let t=iP(this.config,...e);return iP(t,...this.configFactories?await Promise.all(this.configFactories.map(async e=>await e(t))):[])}bind(e){return new this.constructor({bound:this.bound,kwargs:{...this.kwargs,...e},config:this.config})}withConfig(e){return new this.constructor({bound:this.bound,kwargs:this.kwargs,config:{...this.config,...e}})}withRetry(e){return new s_({bound:this.bound,kwargs:this.kwargs,config:this.config,maxAttemptNumber:e?.stopAfterAttempt,...e})}async invoke(e,t){return this.bound.invoke(e,await this._mergeConfig(iR(t),this.kwargs))}async batch(e,t,r){let a=Array.isArray(t)?await Promise.all(t.map(async e=>this._mergeConfig(iR(e),this.kwargs))):await this._mergeConfig(iR(t),this.kwargs);return this.bound.batch(e,a,r)}async *_streamIterator(e,t){yield*this.bound._streamIterator(e,await this._mergeConfig(iR(t),this.kwargs))}async stream(e,t){return this.bound.stream(e,await this._mergeConfig(iR(t),this.kwargs))}async *transform(e,t){yield*this.bound.transform(e,await this._mergeConfig(iR(t),this.kwargs))}streamEvents(e,t,r){let a=this,i=async function*(){yield*a.bound.streamEvents(e,{...await a._mergeConfig(iR(t),a.kwargs),version:t.version},r)};return iM.fromAsyncGenerator(i())}static isRunnableBinding(e){return e.bound&&sm.isRunnable(e.bound)}withListeners({onStart:e,onEnd:t,onError:r}){return new sg({bound:this.bound,kwargs:this.kwargs,config:this.config,configFactories:[a=>({callbacks:[new i4({config:a,onStart:e,onEnd:t,onError:r})]})]})}}class sy extends sm{static lc_name(){return"RunnableEach"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound}bind(e){return new sy({bound:this.bound.bind(e)})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _invoke(e,t,r){return this.bound.batch(e,i$(t,{callbacks:r?.getChild()}))}withListeners({onStart:e,onEnd:t,onError:r}){return new sy({bound:this.bound.withListeners({onStart:e,onEnd:t,onError:r})})}}class s_ extends sg{static lc_name(){return"RunnableRetry"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"maxAttemptNumber",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:()=>{}}),this.maxAttemptNumber=e.maxAttemptNumber??this.maxAttemptNumber,this.onFailedAttempt=e.onFailedAttempt??this.onFailedAttempt}_patchConfigForRetry(e,t,r){let a=e>1?`retry:attempt:${e}`:void 0;return i$(t,{callbacks:r?.getChild(a)})}async _invoke(e,t,r){return rr(a=>super.invoke(e,this._patchConfigForRetry(a,t,r)),{onFailedAttempt:t=>this.onFailedAttempt(t,e),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _batch(e,t,r,a){let i={};try{await rr(async n=>{let s,o=e.map((e,t)=>t).filter(e=>void 0===i[e.toString()]||i[e.toString()]instanceof Error),l=o.map(t=>e[t]),u=o.map(e=>this._patchConfigForRetry(n,t?.[e],r?.[e])),d=await super.batch(l,u,{...a,returnExceptions:!0});for(let e=0;e<d.length;e+=1){let t=d[e],r=o[e];t instanceof Error&&void 0===s&&((s=t).input=l[e]),i[r.toString()]=t}if(s)throw s;return d},{onFailedAttempt:e=>this.onFailedAttempt(e,e.input),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}catch(e){if(a?.returnExceptions!==!0)throw e}return Object.keys(i).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(e=>i[parseInt(e,10)])}async batch(e,t,r){return this._batchWithConfig(this._batch.bind(this),e,t,r)}}class sb extends sm{static lc_name(){return"RunnableSequence"}constructor(e){super(e),Object.defineProperty(this,"first",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"middle",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"last",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"omitSequenceTags",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),this.first=e.first,this.middle=e.middle??this.middle,this.last=e.last,this.name=e.name,this.omitSequenceTags=e.omitSequenceTags??this.omitSequenceTags}get steps(){return[this.first,...this.middle,this.last]}async invoke(e,t){let r,a=iR(t),i=await iC(a),n=await i?.handleChainStart(this.toJSON(),sf(e,"input"),a.runId,void 0,void 0,void 0,a?.runName);delete a.runId;let s=e;try{let e=[this.first,...this.middle];for(let r=0;r<e.length;r+=1){let i=e[r].invoke(s,i$(a,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${r+1}`)}));s=await iL(i,t?.signal)}if(t?.signal?.aborted)throw Error("Aborted");r=await this.last.invoke(s,i$(a,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${this.steps.length}`)}))}catch(e){throw await n?.handleChainError(e),e}return await n?.handleChainEnd(sf(r,"output")),r}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=await Promise.all(a.map(iC)),n=await Promise.all(i.map(async(t,r)=>{let i=await t?.handleChainStart(this.toJSON(),sf(e[r],"input"),a[r].runId,void 0,void 0,void 0,a[r].runName);return delete a[r].runId,i})),s=e;try{for(let e=0;e<this.steps.length;e+=1){let t=this.steps[e].batch(s,n.map((t,r)=>{let i=t?.getChild(this.omitSequenceTags?void 0:`seq:step:${e+1}`);return i$(a[r],{callbacks:i})}),r);s=await iL(t,a[0]?.signal)}}catch(e){throw await Promise.all(n.map(t=>t?.handleChainError(e))),e}return await Promise.all(n.map(e=>e?.handleChainEnd(sf(s,"output")))),s}async *_streamIterator(e,t){let r,a=await iC(t),{runId:i,...n}=t??{},s=await a?.handleChainStart(this.toJSON(),sf(e,"input"),i,void 0,void 0,void 0,n?.runName),o=[this.first,...this.middle,this.last],l=!0;async function*u(){yield e}try{let e=o[0].transform(u(),i$(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:"seq:step:1")}));for(let t=1;t<o.length;t+=1){let r=o[t];e=await r.transform(e,i$(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:`seq:step:${t+1}`)}))}for await(let a of e)if(t?.signal?.throwIfAborted(),yield a,l)if(void 0===r)r=a;else try{r=iD(r,a)}catch(e){r=void 0,l=!1}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(sf(r,"output"))}getGraph(e){let t=new ss,r=null;return this.steps.forEach((a,i)=>{let n=a.getGraph(e);0!==i&&n.trimFirstNode(),i!==this.steps.length-1&&n.trimLastNode(),t.extend(n);let s=n.firstNode();if(!s)throw Error(`Runnable ${a} has no first node`);r&&t.addEdge(r,s),r=n.lastNode()}),t}pipe(e){return new sb(sb.isRunnableSequence(e)?{first:this.first,middle:this.middle.concat([this.last,e.first,...e.middle]),last:e.last,name:this.name??e.name}:{first:this.first,middle:[...this.middle,this.last],last:sx(e),name:this.name})}static isRunnableSequence(e){return Array.isArray(e.middle)&&sm.isRunnable(e)}static from([e,...t],r){let a={};return"string"==typeof r?a.name=r:void 0!==r&&(a=r),new sb({...a,first:sx(e),middle:t.slice(0,-1).map(sx),last:sx(t[t.length-1])})}}class sv extends sm{static lc_name(){return"RunnableMap"}getStepsKeys(){return Object.keys(this.steps)}constructor(e){for(let[t,r]of(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"steps",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.steps={},Object.entries(e.steps)))this.steps[t]=sx(r)}static from(e){return new sv({steps:e})}async invoke(e,t){let r=iR(t),a=await iC(r),i=await a?.handleChainStart(this.toJSON(),{input:e},r.runId,void 0,void 0,void 0,r?.runName);delete r.runId;let n={};try{let a=Object.entries(this.steps).map(async([t,a])=>{n[t]=await a.invoke(e,i$(r,{callbacks:i?.getChild(`map:key:${t}`)}))});await iL(Promise.all(a),t?.signal)}catch(e){throw await i?.handleChainError(e),e}return await i?.handleChainEnd(n),n}async *_transform(e,t,r){let a={...this.steps},i=iU(e,Object.keys(a).length),n=new Map(Object.entries(a).map(([e,a],n)=>{let s=a.transform(i[n],i$(r,{callbacks:t?.getChild(`map:key:${e}`)}));return[e,s.next().then(t=>({key:e,gen:s,result:t}))]}));for(;n.size;){let e=Promise.race(n.values()),{key:t,result:a,gen:i}=await iL(e,r?.signal);n.delete(t),a.done||(yield{[t]:a.value},n.set(t,i.next().then(e=>({key:t,gen:i,result:e}))))}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=iR(t),i=new iF({generator:this.transform(r(),a),config:a});return await i.setup,iM.fromAsyncGenerator(i)}}class sw extends sm{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!aC(e.func))throw Error("RunnableTraceable requires a function that is wrapped in traceable higher-order function");this.func=e.func}async invoke(e,t){let[r]=this._getOptionsList(t??{},1),a=await iC(r);return iL(this.func(i$(r,{callbacks:a}),e),r?.signal)}async *_streamIterator(e,t){let[r]=this._getOptionsList(t??{},1),a=await this.invoke(e,t);if(sc(a)){for await(let e of a)r?.signal?.throwIfAborted(),yield e;return}if(sd(a)){for(;;){r?.signal?.throwIfAborted();let e=a.next();if(e.done)break;yield e.value}return}yield a}static from(e){return new sw({func:e})}}class sE extends sm{static lc_name(){return"RunnableLambda"}constructor(e){if(aC(e.func))return sw.from(e.func);if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),aC(e.func))throw Error("RunnableLambda requires a function that is not wrapped in traceable higher-order function. This shouldn't happen.");this.func=e.func}static from(e){return new sE({func:e})}async _invoke(e,t,r){return new Promise((a,i)=>{let n=i$(t,{callbacks:r?.getChild(),recursionLimit:(t?.recursionLimit??25)-1});iI.runWithConfig(iN(n),async()=>{try{let r=await this.func(e,{...n});if(r&&sm.isRunnable(r)){if(t?.recursionLimit===0)throw Error("Recursion limit reached.");r=await r.invoke(e,{...n,recursionLimit:(n.recursionLimit??25)-1})}else if(sc(r)){let e;for await(let a of sp(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=iD(e,a)}catch(t){e=a}r=e}else if(su(r)){let e;for(let a of sh(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=iD(e,a)}catch(t){e=a}r=e}a(r)}catch(e){i(e)}})})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async *_transform(e,t,r){let a;for await(let t of e)if(void 0===a)a=t;else try{a=iD(a,t)}catch(e){a=t}let i=i$(r,{callbacks:t?.getChild(),recursionLimit:(r?.recursionLimit??25)-1}),n=await new Promise((e,t)=>{iI.runWithConfig(iN(i),async()=>{try{let t=await this.func(a,{...i,config:i});e(t)}catch(e){t(e)}})});if(n&&sm.isRunnable(n)){if(r?.recursionLimit===0)throw Error("Recursion limit reached.");for await(let e of(await n.stream(a,i)))yield e}else if(sc(n))for await(let e of sp(i,n))r?.signal?.throwIfAborted(),yield e;else if(su(n))for(let e of sh(i,n))r?.signal?.throwIfAborted(),yield e;else yield n}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=iR(t),i=new iF({generator:this.transform(r(),a),config:a});return await i.setup,iM.fromAsyncGenerator(i)}}class sO extends sm{static lc_name(){return"RunnableWithFallbacks"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fallbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.fallbacks=e.fallbacks}*runnables(){for(let e of(yield this.runnable,this.fallbacks))yield e}async invoke(e,t){let r=iR(t),a=await iC(r),{runId:i,...n}=r,s=await a?.handleChainStart(this.toJSON(),sf(e,"input"),i,void 0,void 0,void 0,n?.runName),o=i$(n,{callbacks:s?.getChild()});return await iI.runWithConfig(o,async()=>{let t;for(let a of this.runnables()){r?.signal?.throwIfAborted();try{let t=await a.invoke(e,o);return await s?.handleChainEnd(sf(t,"output")),t}catch(e){void 0===t&&(t=e)}}if(void 0===t)throw Error("No error stored at end of fallback.");throw await s?.handleChainError(t),t})}async *_streamIterator(e,t){let r,a,i,n=iR(t),s=await iC(n),{runId:o,...l}=n,u=await s?.handleChainStart(this.toJSON(),sf(e,"input"),o,void 0,void 0,void 0,l?.runName);for(let t of this.runnables()){n?.signal?.throwIfAborted();let i=i$(l,{callbacks:u?.getChild()});try{let r=await t.stream(e,i);a=sp(i,r);break}catch(e){void 0===r&&(r=e)}}if(void 0===a){let e=r??Error("No error stored at end of fallback.");throw await u?.handleChainError(e),e}try{for await(let e of a){yield e;try{i=void 0===i?i:iD(i,e)}catch(e){i=void 0}}}catch(e){throw await u?.handleChainError(e),e}await u?.handleChainEnd(sf(i,"output"))}async batch(e,t,r){let a;if(r?.returnExceptions)throw Error("Not implemented.");let i=this._getOptionsList(t??{},e.length),n=await Promise.all(i.map(e=>iC(e))),s=await Promise.all(n.map(async(t,r)=>{let a=await t?.handleChainStart(this.toJSON(),sf(e[r],"input"),i[r].runId,void 0,void 0,void 0,i[r].runName);return delete i[r].runId,a}));for(let t of this.runnables()){i[0].signal?.throwIfAborted();try{let a=await t.batch(e,s.map((e,t)=>i$(i[t],{callbacks:e?.getChild()})),r);return await Promise.all(s.map((e,t)=>e?.handleChainEnd(sf(a[t],"output")))),a}catch(e){void 0===a&&(a=e)}}if(!a)throw Error("No error stored at end of fallbacks.");throw await Promise.all(s.map(e=>e?.handleChainError(a))),a}}function sx(e){if("function"==typeof e)return new sE({func:e});if(sm.isRunnable(e))return e;if(Array.isArray(e)||"object"!=typeof e)throw Error(`Expected a Runnable, function or object.
Instead got an unsupported type.`);{let t={};for(let[r,a]of Object.entries(e))t[r]=sx(a);return new sv({steps:t})}}class sT extends sm{static lc_name(){return"RunnableAssign"}constructor(e){e instanceof sv&&(e={mapper:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.mapper=e.mapper}async invoke(e,t){let r=await this.mapper.invoke(e,t);return{...e,...r}}async *_transform(e,t,r){let a=this.mapper.getStepsKeys(),[i,n]=iU(e),s=this.mapper.transform(n,i$(r,{callbacks:t?.getChild()})),o=s.next();for await(let e of i){if("object"!=typeof e||Array.isArray(e))throw Error(`RunnableAssign can only be used with objects as input, got ${typeof e}`);let t=Object.fromEntries(Object.entries(e).filter(([e])=>!a.includes(e)));Object.keys(t).length>0&&(yield t)}for await(let e of(yield(await o).value,s))yield e}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=iR(t),i=new iF({generator:this.transform(r(),a),config:a});return await i.setup,iM.fromAsyncGenerator(i)}}class sS extends sm{static lc_name(){return"RunnablePick"}constructor(e){("string"==typeof e||Array.isArray(e))&&(e={keys:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"keys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keys=e.keys}async _pick(e){if("string"==typeof this.keys)return e[this.keys];{let t=this.keys.map(t=>[t,e[t]]).filter(e=>void 0!==e[1]);return 0===t.length?void 0:Object.fromEntries(t)}}async invoke(e,t){return this._callWithConfig(this._pick.bind(this),e,t)}async *_transform(e){for await(let t of e){let e=await this._pick(t);void 0!==e&&(yield e)}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=iR(t),i=new iF({generator:this.transform(r(),a),config:a});return await i.setup,iM.fromAsyncGenerator(i)}}class sk extends sg{constructor(e){super({bound:sb.from([sE.from(async e=>{let t;if(eJ(e))try{t=await st(this.schema,e.args)}catch(t){throw new eW("Received tool input did not match expected schema",JSON.stringify(e.args))}else t=e;return t}).withConfig({runName:`${e.name}:parse_input`}),e.bound]).withConfig({runName:e.name}),config:e.config??{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}static lc_name(){return"RunnableToolLike"}}class sA extends ek{constructor(e){super({...e,content:""}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.id=e.id}_getType(){return"remove"}get _printableFields(){return{...super._printableFields,id:this.id}}}let sI=(e,t)=>{let r=[...new Set(t?.map(e=>{if("string"==typeof e)return e;let t=new e({});if(!("getType"in t)||"function"!=typeof t.getType)throw Error("Invalid type provided.");return t.getType()}))],a=e.getType();return r.some(e=>e===a)};async function sC(e,t){let{maxTokens:r,tokenCounter:a,textSplitter:i,partialStrategy:n,endOn:s}=t,o=[...e],l=0;for(let e=0;e<o.length;e+=1){let t=e>0?o.slice(0,-e):o;if(await a(t)<=r){l=o.length-e;break}}if(l<o.length-1&&n){let e=!1;if(Array.isArray(o[l].content)){let t=o[l];if("string"==typeof t.content)throw Error("Expected content to be an array.");let i=t.content.length,s="last"===n?[...t.content].reverse():t.content;for(let u=1;u<=i;u+=1){let i="first"===n?s.slice(0,u):s.slice(-u),d=Object.fromEntries(Object.entries(t).filter(([e])=>"type"!==e&&!e.startsWith("lc_"))),c=sR(t.getType(),{...d,content:i}),h=[...o.slice(0,l),c];if(await a(h)<=r)o=h,l+=1,e=!0;else break}e&&"last"===n&&(t.content=[...s].reverse())}if(!e){let e,t=o[l];if(Array.isArray(t.content)&&t.content.some(e=>"string"==typeof e||"text"===e.type)){let r=t.content.find(e=>"text"===e.type&&e.text);e=r?.text}else"string"==typeof t.content&&(e=t.content);if(e){let s=await i(e),u=s.length;"last"===n&&s.reverse();for(let e=0;e<u-1;e+=1)if(s.pop(),t.content=s.join(""),await a([...o.slice(0,l),t])<=r){"last"===n&&(t.content=[...s].reverse().join("")),o=[...o.slice(0,l),t],l+=1;break}}}}if(s){let e=Array.isArray(s)?s:[s];for(;l>0&&!sI(o[l-1],e);)l-=1}return o.slice(0,l)}async function sP(e,t){let{allowPartial:r=!1,includeSystem:a=!1,endOn:i,startOn:n,...s}=t,o=e.map(e=>{let t=Object.fromEntries(Object.entries(e).filter(([e])=>"type"!==e&&!e.startsWith("lc_")));return sR(e.getType(),t,isBaseMessageChunk(e))});if(i){let e=Array.isArray(i)?i:[i];for(;o.length>0&&!sI(o[o.length-1],e);)o=o.slice(0,-1)}let l=a&&o[0]?.getType()==="system",u=l?o.slice(0,1).concat(o.slice(1).reverse()):o.reverse();return(u=await sC(u,{...s,partialStrategy:r?"last":void 0,endOn:n}),l)?[u[0],...u.slice(1).reverse()]:u.reverse()}let sj={human:{message:eB,messageChunk:eZ},ai:{message:e$,messageChunk:eM},system:{message:eG,messageChunk:eH},developer:{message:eG,messageChunk:eH},tool:{message:ej,messageChunk:eR},function:{message:eF,messageChunk:ez},generic:{message:eU,messageChunk:eD},remove:{message:sA,messageChunk:sA}};function sR(e,t,r){let a,i;switch(e){case"human":r?a=new HumanMessageChunk(t):i=new HumanMessage(t);break;case"ai":if(r){let e={...t};"tool_calls"in e&&(e={...e,tool_call_chunks:e.tool_calls?.map(e=>({...e,type:"tool_call_chunk",index:void 0,args:JSON.stringify(e.args)}))}),a=new AIMessageChunk(e)}else i=new AIMessage(t);break;case"system":r?a=new SystemMessageChunk(t):i=new SystemMessage(t);break;case"developer":r?a=new SystemMessageChunk({...t,additional_kwargs:{...t.additional_kwargs,__openai_role__:"developer"}}):i=new SystemMessage({...t,additional_kwargs:{...t.additional_kwargs,__openai_role__:"developer"}});break;case"tool":if("tool_call_id"in t)r?a=new ToolMessageChunk(t):i=new ToolMessage(t);else throw Error("Can not convert ToolMessage to ToolMessageChunk if 'tool_call_id' field is not defined.");break;case"function":if(r)a=new FunctionMessageChunk(t);else{if(!t.name)throw Error("FunctionMessage must have a 'name' field");i=new FunctionMessage(t)}break;case"generic":if("role"in t)r?a=new ChatMessageChunk(t):i=new ChatMessage(t);else throw Error("Can not convert ChatMessage to ChatMessageChunk if 'role' field is not defined.");break;default:throw Error(`Unrecognized message type ${e}`)}if(r&&a)return a;if(i)return i;throw Error(`Unrecognized message type ${e}`)}function s$(e){let t=e.split("\n");return Promise.resolve([...t.slice(0,-1).map(e=>`${e}
`),t[t.length-1]])}var sN="object"==typeof window?window:{},sL="0123456789abcdef".split(""),sM=[-0x80000000,8388608,32768,128],sU=[24,16,8,0],sD=[];function sF(e){e?(sD[0]=sD[16]=sD[1]=sD[2]=sD[3]=sD[4]=sD[5]=sD[6]=sD[7]=sD[8]=sD[9]=sD[10]=sD[11]=sD[12]=sD[13]=sD[14]=sD[15]=0,this.blocks=sD):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=0x67452301,this.h1=0xefcdab89,this.h2=0x98badcfe,this.h3=0x10325476,this.h4=0xc3d2e1f0,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}sF.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===sN.ArrayBuffer&&(e=new Uint8Array(e));for(var r,a,i=0,n=e.length||0,s=this.blocks;i<n;){if(this.hashed&&(this.hashed=!1,s[0]=this.block,s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)for(a=this.start;i<n&&a<64;++i)s[a>>2]|=e[i]<<sU[3&a++];else for(a=this.start;i<n&&a<64;++i)(r=e.charCodeAt(i))<128?s[a>>2]|=r<<sU[3&a++]:(r<2048?s[a>>2]|=(192|r>>6)<<sU[3&a++]:(r<55296||r>=57344?s[a>>2]|=(224|r>>12)<<sU[3&a++]:(r=65536+((1023&r)<<10|1023&e.charCodeAt(++i)),s[a>>2]|=(240|r>>18)<<sU[3&a++],s[a>>2]|=(128|r>>12&63)<<sU[3&a++]),s[a>>2]|=(128|r>>6&63)<<sU[3&a++]),s[a>>2]|=(128|63&r)<<sU[3&a++]);this.lastByteIndex=a,this.bytes+=a-this.start,a>=64?(this.block=s[16],this.start=a-64,this.hash(),this.hashed=!0):this.start=a}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000|0,this.bytes=this.bytes%0x100000000),this}},sF.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=sM[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},sF.prototype.hash=function(){var e,t,r,a=this.h0,i=this.h1,n=this.h2,s=this.h3,o=this.h4,l=this.blocks;for(t=16;t<80;++t)r=l[t-3]^l[t-8]^l[t-14]^l[t-16],l[t]=r<<1|r>>>31;for(t=0;t<20;t+=5)e=i&n|~i&s,o=(r=a<<5|a>>>27)+e+o+0x5a827999+l[t]|0,e=a&(i=i<<30|i>>>2)|~a&n,s=(r=o<<5|o>>>27)+e+s+0x5a827999+l[t+1]|0,e=o&(a=a<<30|a>>>2)|~o&i,n=(r=s<<5|s>>>27)+e+n+0x5a827999+l[t+2]|0,e=s&(o=o<<30|o>>>2)|~s&a,i=(r=n<<5|n>>>27)+e+i+0x5a827999+l[t+3]|0,e=n&(s=s<<30|s>>>2)|~n&o,a=(r=i<<5|i>>>27)+e+a+0x5a827999+l[t+4]|0,n=n<<30|n>>>2;for(;t<40;t+=5)e=i^n^s,o=(r=a<<5|a>>>27)+e+o+0x6ed9eba1+l[t]|0,e=a^(i=i<<30|i>>>2)^n,s=(r=o<<5|o>>>27)+e+s+0x6ed9eba1+l[t+1]|0,e=o^(a=a<<30|a>>>2)^i,n=(r=s<<5|s>>>27)+e+n+0x6ed9eba1+l[t+2]|0,e=s^(o=o<<30|o>>>2)^a,i=(r=n<<5|n>>>27)+e+i+0x6ed9eba1+l[t+3]|0,e=n^(s=s<<30|s>>>2)^o,a=(r=i<<5|i>>>27)+e+a+0x6ed9eba1+l[t+4]|0,n=n<<30|n>>>2;for(;t<60;t+=5)e=i&n|i&s|n&s,o=(r=a<<5|a>>>27)+e+o-0x70e44324+l[t]|0,e=a&(i=i<<30|i>>>2)|a&n|i&n,s=(r=o<<5|o>>>27)+e+s-0x70e44324+l[t+1]|0,e=o&(a=a<<30|a>>>2)|o&i|a&i,n=(r=s<<5|s>>>27)+e+n-0x70e44324+l[t+2]|0,e=s&(o=o<<30|o>>>2)|s&a|o&a,i=(r=n<<5|n>>>27)+e+i-0x70e44324+l[t+3]|0,e=n&(s=s<<30|s>>>2)|n&o|s&o,a=(r=i<<5|i>>>27)+e+a-0x70e44324+l[t+4]|0,n=n<<30|n>>>2;for(;t<80;t+=5)e=i^n^s,o=(r=a<<5|a>>>27)+e+o-0x359d3e2a+l[t]|0,e=a^(i=i<<30|i>>>2)^n,s=(r=o<<5|o>>>27)+e+s-0x359d3e2a+l[t+1]|0,e=o^(a=a<<30|a>>>2)^i,n=(r=s<<5|s>>>27)+e+n-0x359d3e2a+l[t+2]|0,e=s^(o=o<<30|o>>>2)^a,i=(r=n<<5|n>>>27)+e+i-0x359d3e2a+l[t+3]|0,e=n^(s=s<<30|s>>>2)^o,a=(r=i<<5|i>>>27)+e+a-0x359d3e2a+l[t+4]|0,n=n<<30|n>>>2;this.h0=this.h0+a|0,this.h1=this.h1+i|0,this.h2=this.h2+n|0,this.h3=this.h3+s|0,this.h4=this.h4+o|0},sF.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4;return sL[e>>28&15]+sL[e>>24&15]+sL[e>>20&15]+sL[e>>16&15]+sL[e>>12&15]+sL[e>>8&15]+sL[e>>4&15]+sL[15&e]+sL[t>>28&15]+sL[t>>24&15]+sL[t>>20&15]+sL[t>>16&15]+sL[t>>12&15]+sL[t>>8&15]+sL[t>>4&15]+sL[15&t]+sL[r>>28&15]+sL[r>>24&15]+sL[r>>20&15]+sL[r>>16&15]+sL[r>>12&15]+sL[r>>8&15]+sL[r>>4&15]+sL[15&r]+sL[a>>28&15]+sL[a>>24&15]+sL[a>>20&15]+sL[a>>16&15]+sL[a>>12&15]+sL[a>>8&15]+sL[a>>4&15]+sL[15&a]+sL[i>>28&15]+sL[i>>24&15]+sL[i>>20&15]+sL[i>>16&15]+sL[i>>12&15]+sL[i>>8&15]+sL[i>>4&15]+sL[15&i]},sF.prototype.toString=sF.prototype.hex,sF.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,r>>24&255,r>>16&255,r>>8&255,255&r,a>>24&255,a>>16&255,a>>8&255,255&a,i>>24&255,i>>16&255,i>>8&255,255&i]},sF.prototype.array=sF.prototype.digest,sF.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};let sz=!1,sB=e=>(sz||(console.warn(`The default method for hashing keys is insecure and will be replaced in a future version,
but hasn't been replaced yet as to not break existing caches. It's recommended that you use
a more secure hashing algorithm to avoid cache poisoning.

See this page for more information:
|
└> https://js.langchain.com/docs/troubleshooting/warnings/insecure-cache-algorithm`),sz=!0),new sF(!0).update(e).hex());var sZ="0123456789abcdef".split(""),sG=[-0x80000000,8388608,32768,128],sH=[24,16,8,0],sq=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],sJ=[];function sW(e,t){t?(sJ[0]=sJ[16]=sJ[1]=sJ[2]=sJ[3]=sJ[4]=sJ[5]=sJ[6]=sJ[7]=sJ[8]=sJ[9]=sJ[10]=sJ[11]=sJ[12]=sJ[13]=sJ[14]=sJ[15]=0,this.blocks=sJ):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=0xc1059ed8,this.h1=0x367cd507,this.h2=0x3070dd17,this.h3=0xf70e5939,this.h4=0xffc00b31,this.h5=0x68581511,this.h6=0x64f98fa7,this.h7=0xbefa4fa4):(this.h0=0x6a09e667,this.h1=0xbb67ae85,this.h2=0x3c6ef372,this.h3=0xa54ff53a,this.h4=0x510e527f,this.h5=0x9b05688c,this.h6=0x1f83d9ab,this.h7=0x5be0cd19),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}sW.prototype.update=function(e){if(!this.finalized){var t,r=typeof e;if("string"!==r){if("object"===r){if(null===e)throw Error(ERROR);else if(ARRAY_BUFFER&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(e)))throw Error(ERROR)}else throw Error(ERROR);t=!0}for(var a,i,n=0,s=e.length,o=this.blocks;n<s;){if(this.hashed&&(this.hashed=!1,o[0]=this.block,this.block=o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),t)for(i=this.start;n<s&&i<64;++n)o[i>>>2]|=e[n]<<sH[3&i++];else for(i=this.start;n<s&&i<64;++n)(a=e.charCodeAt(n))<128?o[i>>>2]|=a<<sH[3&i++]:(a<2048?o[i>>>2]|=(192|a>>>6)<<sH[3&i++]:(a<55296||a>=57344?o[i>>>2]|=(224|a>>>12)<<sH[3&i++]:(a=65536+((1023&a)<<10|1023&e.charCodeAt(++n)),o[i>>>2]|=(240|a>>>18)<<sH[3&i++],o[i>>>2]|=(128|a>>>12&63)<<sH[3&i++]),o[i>>>2]|=(128|a>>>6&63)<<sH[3&i++]),o[i>>>2]|=(128|63&a)<<sH[3&i++]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.block=o[16],this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000|0,this.bytes=this.bytes%0x100000000),this}},sW.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>>2]|=sG[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},sW.prototype.hash=function(){var e,t,r,a,i,n,s,o,l,u,d,c=this.h0,h=this.h1,p=this.h2,f=this.h3,m=this.h4,g=this.h5,y=this.h6,_=this.h7,b=this.blocks;for(e=16;e<64;++e)t=((i=b[e-15])>>>7|i<<25)^(i>>>18|i<<14)^i>>>3,r=((i=b[e-2])>>>17|i<<15)^(i>>>19|i<<13)^i>>>10,b[e]=b[e-16]+t+b[e-7]+r|0;for(e=0,d=h&p;e<64;e+=4)this.first?(this.is224?(o=300032,_=(i=b[0]-0x543c9a5b)-0x8f1a6c7|0,f=i+0x170e9b5|0):(o=0x2a01a605,_=(i=b[0]-0xc881298)-0x5ab00ac6|0,f=i+0x8909ae5|0),this.first=!1):(t=(c>>>2|c<<30)^(c>>>13|c<<19)^(c>>>22|c<<10),r=(m>>>6|m<<26)^(m>>>11|m<<21)^(m>>>25|m<<7),a=(o=c&h)^c&p^d,i=_+r+(m&g^~m&y)+sq[e]+b[e],n=t+a,_=f+i|0,f=i+n|0),t=(f>>>2|f<<30)^(f>>>13|f<<19)^(f>>>22|f<<10),r=(_>>>6|_<<26)^(_>>>11|_<<21)^(_>>>25|_<<7),a=(l=f&c)^f&h^o,i=g+r+(y&_^~y&m)+sq[e+1]+b[e+1],n=t+a,y=p+i|0,t=((p=i+n|0)>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),r=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),a=(u=p&f)^p&c^l,i=m+r+(g&y^~g&_)+sq[e+2]+b[e+2],n=t+a,g=h+i|0,t=((h=i+n|0)>>>2|h<<30)^(h>>>13|h<<19)^(h>>>22|h<<10),r=(g>>>6|g<<26)^(g>>>11|g<<21)^(g>>>25|g<<7),a=(d=h&p)^h&f^u,i=m+r+(g&y^~g&_)+sq[e+3]+b[e+3],n=t+a,m=c+i|0,c=i+n|0,this.chromeBugWorkAround=!0;this.h0=this.h0+c|0,this.h1=this.h1+h|0,this.h2=this.h2+p|0,this.h3=this.h3+f|0,this.h4=this.h4+m|0,this.h5=this.h5+g|0,this.h6=this.h6+y|0,this.h7=this.h7+_|0},sW.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4,n=this.h5,s=this.h6,o=this.h7,l=sZ[e>>>28&15]+sZ[e>>>24&15]+sZ[e>>>20&15]+sZ[e>>>16&15]+sZ[e>>>12&15]+sZ[e>>>8&15]+sZ[e>>>4&15]+sZ[15&e]+sZ[t>>>28&15]+sZ[t>>>24&15]+sZ[t>>>20&15]+sZ[t>>>16&15]+sZ[t>>>12&15]+sZ[t>>>8&15]+sZ[t>>>4&15]+sZ[15&t]+sZ[r>>>28&15]+sZ[r>>>24&15]+sZ[r>>>20&15]+sZ[r>>>16&15]+sZ[r>>>12&15]+sZ[r>>>8&15]+sZ[r>>>4&15]+sZ[15&r]+sZ[a>>>28&15]+sZ[a>>>24&15]+sZ[a>>>20&15]+sZ[a>>>16&15]+sZ[a>>>12&15]+sZ[a>>>8&15]+sZ[a>>>4&15]+sZ[15&a]+sZ[i>>>28&15]+sZ[i>>>24&15]+sZ[i>>>20&15]+sZ[i>>>16&15]+sZ[i>>>12&15]+sZ[i>>>8&15]+sZ[i>>>4&15]+sZ[15&i]+sZ[n>>>28&15]+sZ[n>>>24&15]+sZ[n>>>20&15]+sZ[n>>>16&15]+sZ[n>>>12&15]+sZ[n>>>8&15]+sZ[n>>>4&15]+sZ[15&n]+sZ[s>>>28&15]+sZ[s>>>24&15]+sZ[s>>>20&15]+sZ[s>>>16&15]+sZ[s>>>12&15]+sZ[s>>>8&15]+sZ[s>>>4&15]+sZ[15&s];return this.is224||(l+=sZ[o>>>28&15]+sZ[o>>>24&15]+sZ[o>>>20&15]+sZ[o>>>16&15]+sZ[o>>>12&15]+sZ[o>>>8&15]+sZ[o>>>4&15]+sZ[15&o]),l},sW.prototype.toString=sW.prototype.hex,sW.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4,n=this.h5,s=this.h6,o=this.h7,l=[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,r>>>24&255,r>>>16&255,r>>>8&255,255&r,a>>>24&255,a>>>16&255,a>>>8&255,255&a,i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,s>>>24&255,s>>>16&255,s>>>8&255,255&s];return this.is224||l.push(o>>>24&255,o>>>16&255,o>>>8&255,255&o),l},sW.prototype.array=sW.prototype.digest,sW.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e};let sV=(...e)=>sB(e.join("_"));class sK{constructor(){Object.defineProperty(this,"keyEncoder",{enumerable:!0,configurable:!0,writable:!0,value:sV})}makeDefaultKeyEncoder(e){this.keyEncoder=e}}let sY=new Map;class sX extends sK{constructor(e){super(),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cache=e??new Map}lookup(e,t){return Promise.resolve(this.cache.get(this.keyEncoder(e,t))??null)}async update(e,t,r){this.cache.set(this.keyEncoder(e,t),r)}static global(){return new sX(sY)}}class sQ extends eO{}class s0 extends sQ{static lc_name(){return"StringPromptValue"}constructor(e){super({value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.value=e}toString(){return this.value}toChatMessages(){return[new eB(this.value)]}}class s1 extends sQ{static lc_name(){return"ChatPromptValue"}constructor(e){Array.isArray(e)&&(e={messages:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.messages=e.messages}toString(){return eX(this.messages)}toChatMessages(){return this.messages}}var s2=r(18929),s4=Object.defineProperty,s5=(e,t,r)=>t in e?s4(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,s3=class{specialTokens;inverseSpecialTokens;patStr;textEncoder=new TextEncoder;textDecoder=new TextDecoder("utf-8");rankMap=new Map;textMap=new Map;constructor(e,t){for(let[t,r]of(this.patStr=e.pat_str,Object.entries(e.bpe_ranks.split("\n").filter(Boolean).reduce((e,t)=>{let[r,a,...i]=t.split(" "),n=Number.parseInt(a,10);return i.forEach((t,r)=>e[t]=n+r),e},{})))){let e=s2.toByteArray(t);this.rankMap.set(e.join(","),r),this.textMap.set(r,e)}this.specialTokens={...e.special_tokens,...t},this.inverseSpecialTokens=Object.entries(this.specialTokens).reduce((e,[t,r])=>(e[r]=this.textEncoder.encode(t),e),{})}encode(e,t=[],r="all"){let a=RegExp(this.patStr,"ug"),i=s3.specialTokenRegex(Object.keys(this.specialTokens)),n=[],s=new Set("all"===t?Object.keys(this.specialTokens):t),o=new Set("all"===r?Object.keys(this.specialTokens).filter(e=>!s.has(e)):r);if(o.size>0){let t=s3.specialTokenRegex([...o]),r=e.match(t);if(null!=r)throw Error(`The text contains a special token that is not allowed: ${r[0]}`)}let l=0;for(;;){let t=null,r=l;for(;i.lastIndex=r,!(null==(t=i.exec(e))||s.has(t[0]));)r=t.index+1;let o=t?.index??e.length;for(let t of e.substring(l,o).matchAll(a)){let e=this.textEncoder.encode(t[0]),r=this.rankMap.get(e.join(","));if(null!=r){n.push(r);continue}n.push(...function(e,t){return 1===e.length?[t.get(e.join(","))]:(function(e,t){let r=Array.from({length:e.length},(e,t)=>({start:t,end:t+1}));for(;r.length>1;){let a=null;for(let i=0;i<r.length-1;i++){let n=e.slice(r[i].start,r[i+1].end),s=t.get(n.join(","));null!=s&&(null==a||s<a[0])&&(a=[s,i])}if(null!=a){let e=a[1];r[e]={start:r[e].start,end:r[e+1].end},r.splice(e+1,1)}else break}return r})(e,t).map(r=>t.get(e.slice(r.start,r.end).join(","))).filter(e=>null!=e)}(e,this.rankMap))}if(null==t)break;let u=this.specialTokens[t[0]];n.push(u),l=t.index+t[0].length}return n}decode(e){let t=[],r=0;for(let a=0;a<e.length;++a){let i=e[a],n=this.textMap.get(i)??this.inverseSpecialTokens[i];null!=n&&(t.push(n),r+=n.length)}let a=new Uint8Array(r),i=0;for(let e of t)a.set(e,i),i+=e.length;return this.textDecoder.decode(a)}};((e,t,r)=>s5(e,"symbol"!=typeof t?t+"":t,r))(s3,"specialTokenRegex",e=>RegExp(e.map(e=>e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")).join("|"),"g"));let s9={},s6=new i2({});async function s8(e){return e in s9||(s9[e]=s6.fetch(`https://tiktoken.pages.dev/js/${e}.json`).then(e=>e.json()).then(e=>new s3(e)).catch(t=>{throw delete s9[e],t})),await s9[e]}async function s7(e){return s8(function(e){switch(e){case"gpt2":return"gpt2";case"code-cushman-001":case"code-cushman-002":case"code-davinci-001":case"code-davinci-002":case"cushman-codex":case"davinci-codex":case"davinci-002":case"text-davinci-002":case"text-davinci-003":return"p50k_base";case"code-davinci-edit-001":case"text-davinci-edit-001":return"p50k_edit";case"ada":case"babbage":case"babbage-002":case"code-search-ada-code-001":case"code-search-babbage-code-001":case"curie":case"davinci":case"text-ada-001":case"text-babbage-001":case"text-curie-001":case"text-davinci-001":case"text-search-ada-doc-001":case"text-search-babbage-doc-001":case"text-search-curie-doc-001":case"text-search-davinci-doc-001":case"text-similarity-ada-001":case"text-similarity-babbage-001":case"text-similarity-curie-001":case"text-similarity-davinci-001":return"r50k_base";case"gpt-3.5-turbo-instruct-0914":case"gpt-3.5-turbo-instruct":case"gpt-3.5-turbo-16k-0613":case"gpt-3.5-turbo-16k":case"gpt-3.5-turbo-0613":case"gpt-3.5-turbo-0301":case"gpt-3.5-turbo":case"gpt-4-32k-0613":case"gpt-4-32k-0314":case"gpt-4-32k":case"gpt-4-0613":case"gpt-4-0314":case"gpt-4":case"gpt-3.5-turbo-1106":case"gpt-35-turbo":case"gpt-4-1106-preview":case"gpt-4-vision-preview":case"gpt-3.5-turbo-0125":case"gpt-4-turbo":case"gpt-4-turbo-2024-04-09":case"gpt-4-turbo-preview":case"gpt-4-0125-preview":case"text-embedding-ada-002":case"text-embedding-3-small":case"text-embedding-3-large":return"cl100k_base";case"gpt-4o":case"gpt-4o-2024-05-13":case"gpt-4o-2024-08-06":case"gpt-4o-2024-11-20":case"gpt-4o-mini-2024-07-18":case"gpt-4o-mini":case"gpt-4o-search-preview":case"gpt-4o-search-preview-2025-03-11":case"gpt-4o-mini-search-preview":case"gpt-4o-mini-search-preview-2025-03-11":case"gpt-4o-audio-preview":case"gpt-4o-audio-preview-2024-12-17":case"gpt-4o-audio-preview-2024-10-01":case"gpt-4o-mini-audio-preview":case"gpt-4o-mini-audio-preview-2024-12-17":case"o1":case"o1-2024-12-17":case"o1-mini":case"o1-mini-2024-09-12":case"o1-preview":case"o1-preview-2024-09-12":case"o1-pro":case"o1-pro-2025-03-19":case"o3":case"o3-2025-04-16":case"o3-mini":case"o3-mini-2025-01-31":case"o4-mini":case"o4-mini-2025-04-16":case"chatgpt-4o-latest":case"gpt-4o-realtime":case"gpt-4o-realtime-preview-2024-10-01":case"gpt-4o-realtime-preview-2024-12-17":case"gpt-4o-mini-realtime-preview":case"gpt-4o-mini-realtime-preview-2024-12-17":case"gpt-4.1":case"gpt-4.1-2025-04-14":case"gpt-4.1-mini":case"gpt-4.1-mini-2025-04-14":case"gpt-4.1-nano":case"gpt-4.1-nano-2025-04-14":case"gpt-4.5-preview":case"gpt-4.5-preview-2025-02-27":return"o200k_base";default:throw Error("Unknown model")}}(e))}let oe=e=>e.startsWith("gpt-3.5-turbo-16k")?"gpt-3.5-turbo-16k":e.startsWith("gpt-3.5-turbo-")?"gpt-3.5-turbo":e.startsWith("gpt-4-32k")?"gpt-4-32k":e.startsWith("gpt-4-")?"gpt-4":e.startsWith("gpt-4o")?"gpt-4o":e;function ot(e){return"object"==typeof e&&!!e&&("type"in e&&"function"===e.type&&"function"in e&&"object"==typeof e.function&&!!e.function&&"name"in e.function&&"parameters"in e.function||!1)}let or=()=>!1;class oa extends sm{get lc_attributes(){return{callbacks:void 0,verbose:void 0}}constructor(e){super(e),Object.defineProperty(this,"verbose",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.verbose=e.verbose??or(),this.callbacks=e.callbacks,this.tags=e.tags??[],this.metadata=e.metadata??{}}}class oi extends oa{get callKeys(){return["stop","timeout","signal","tags","metadata","callbacks"]}constructor({callbacks:e,callbackManager:t,...r}){let{cache:a,...i}=r;super({callbacks:e??t,...i}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_encoding",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"object"==typeof a?this.cache=a:a?this.cache=sX.global():this.cache=void 0,this.caller=new i2(r??{})}async getNumTokens(e){let t,r=Math.ceil((t="string"==typeof e?e:e.map(e=>"string"==typeof e?e:"text"===e.type&&"text"in e?e.text:"").join("")).length/4);if(!this._encoding)try{this._encoding=await s7("modelName"in this?oe(this.modelName):"gpt2")}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}if(this._encoding)try{r=this._encoding.encode(t).length}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}return r}static _convertInputToPromptValue(e){return"string"==typeof e?new s0(e):Array.isArray(e)?new s1(e.map(eY)):e}_identifyingParams(){return{}}_getSerializedCacheKeyParametersForCall({config:e,...t}){return Object.entries({...this._identifyingParams(),...t,_type:this._llmType(),_model:this._modelType()}).filter(([e,t])=>void 0!==t).map(([e,t])=>`${e}:${JSON.stringify(t)}`).sort().join(",")}serialize(){return{...this._identifyingParams(),_type:this._llmType(),_model:this._modelType()}}static async deserialize(e){throw Error("Use .toJSON() instead")}}class on extends sm{static lc_name(){return"RunnablePassthrough"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),e&&(this.func=e.func)}async invoke(e,t){let r=iR(t);return this.func&&await this.func(e,r),this._callWithConfig(e=>Promise.resolve(e),e,r)}async *transform(e,t){let r,a=iR(t),i=!0;for await(let t of this._transformStreamWithConfig(e,e=>e,a))if(yield t,i)if(void 0===r)r=t;else try{r=iD(r,t)}catch{r=void 0,i=!1}this.func&&void 0!==r&&await this.func(r,a)}static assign(e){return new sT(new sv({steps:e}))}}function os(e){let t=[];for(let r of e){let e=r;if(Array.isArray(r.content))for(let t=0;t<r.content.length;t++){let a=r.content[t];(ex(a)&&"url"===a.source_type&&"url"in a&&"string"==typeof a.url||ex(a)&&"base64"===a.source_type&&"data"in a&&"string"==typeof a.data)&&e===r&&(e=new r.constructor({...e,content:[...r.content.slice(0,t),function(e){if(ex(e)){if("url"===e.source_type)return{type:"image_url",image_url:{url:e.url}};if("base64"===e.source_type){if(!e.mime_type)throw Error("mime_type key is required for base64 data.");let t=e.mime_type;return{type:"image_url",image_url:{url:`data:${t};base64,${e.data}`}}}}throw Error("Unsupported source type. Only 'url' and 'base64' are supported.")}(a),...r.content.slice(t+1)]}))}t.push(e)}return t}class oo extends oi{constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","chat_models",this._llmType()]}),Object.defineProperty(this,"disableStreaming",{enumerable:!0,configurable:!0,writable:!0,value:!1})}_separateRunnableConfigFromCallOptionsCompat(e){let[t,r]=super._separateRunnableConfigFromCallOptions(e);return r.signal=t.signal,[t,r]}async invoke(e,t){let r=oo._convertInputToPromptValue(e);return(await this.generatePrompt([r],t,t?.callbacks)).generations[0][0].message}async *_streamResponseChunks(e,t,r){throw Error("Not implemented.")}async *_streamIterator(e,t){if(this._streamResponseChunks===oo.prototype._streamResponseChunks||this.disableStreaming)yield this.invoke(e,t);else{let r,a,i=oo._convertInputToPromptValue(e).toChatMessages(),[n,s]=this._separateRunnableConfigFromCallOptionsCompat(t),o={...n.metadata,...this.getLsParams(s)},l=await iO.configure(n.callbacks,this.callbacks,n.tags,this.tags,o,this.metadata,{verbose:this.verbose}),u={options:s,invocation_params:this?.invocationParams(s),batch_size:1},d=await l?.handleChatModelStart(this.toJSON(),[os(i)],n.runId,void 0,u,void 0,void 0,n.runName);try{for await(let e of this._streamResponseChunks(i,s,d?.[0])){if(null==e.message.id){let t=d?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata},yield e.message,r=r?r.concat(e):e,eL(e.message)&&void 0!==e.message.usage_metadata&&(a={tokenUsage:{promptTokens:e.message.usage_metadata.input_tokens,completionTokens:e.message.usage_metadata.output_tokens,totalTokens:e.message.usage_metadata.total_tokens}})}}catch(e){throw await Promise.all((d??[]).map(t=>t?.handleLLMError(e))),e}await Promise.all((d??[]).map(e=>e?.handleLLMEnd({generations:[[r]],llmOutput:a})))}}getLsParams(e){let t=this.getName().startsWith("Chat")?this.getName().replace("Chat",""):this.getName();return{ls_model_type:"chat",ls_stop:e.stop,ls_provider:t}}async _generateUncached(e,t,r,a){let i,n=e.map(e=>e.map(eY));if(void 0!==a&&a.length===n.length)i=a;else{let e={...r.metadata,...this.getLsParams(t)},a=await iO.configure(r.callbacks,this.callbacks,r.tags,this.tags,e,this.metadata,{verbose:this.verbose}),s={options:t,invocation_params:this?.invocationParams(t),batch_size:1};i=await a?.handleChatModelStart(this.toJSON(),n.map(os),r.runId,void 0,s,void 0,void 0,r.runName)}let s=[],o=[];if(i?.[0].handlers.find(a0)&&!this.disableStreaming&&1===n.length&&this._streamResponseChunks!==oo.prototype._streamResponseChunks)try{let e,r;for await(let a of(await this._streamResponseChunks(n[0],t,i?.[0]))){if(null==a.message.id){let e=i?.at(0)?.runId;null!=e&&a.message._updateId(`run-${e}`)}e=void 0===e?a:iD(e,a),eL(a.message)&&void 0!==a.message.usage_metadata&&(r={tokenUsage:{promptTokens:a.message.usage_metadata.input_tokens,completionTokens:a.message.usage_metadata.output_tokens,totalTokens:a.message.usage_metadata.total_tokens}})}if(void 0===e)throw Error("Received empty response from chat model call.");s.push([e]),await i?.[0].handleLLMEnd({generations:s,llmOutput:r})}catch(e){throw await i?.[0].handleLLMError(e),e}else{let e=await Promise.allSettled(n.map((e,r)=>this._generate(e,{...t,promptIndex:r},i?.[r])));await Promise.all(e.map(async(e,t)=>{if("fulfilled"!==e.status)return await i?.[t]?.handleLLMError(e.reason),Promise.reject(e.reason);{let r=e.value;for(let e of r.generations){if(null==e.message.id){let t=i?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata}}return 1===r.generations.length&&(r.generations[0].message.response_metadata={...r.llmOutput,...r.generations[0].message.response_metadata}),s[t]=r.generations,o[t]=r.llmOutput,i?.[t]?.handleLLMEnd({generations:[r.generations],llmOutput:r.llmOutput})}}))}let l={generations:s,llmOutput:o.length?this._combineLLMOutput?.(...o):void 0};return Object.defineProperty(l,iW,{value:i?{runIds:i?.map(e=>e.runId)}:void 0,configurable:!0}),l}async _generateCached({messages:e,cache:t,llmStringKey:r,parsedOptions:a,handledOptions:i}){let n=e.map(e=>e.map(eY)),s={...i.metadata,...this.getLsParams(a)},o=await iO.configure(i.callbacks,this.callbacks,i.tags,this.tags,s,this.metadata,{verbose:this.verbose}),l={options:a,invocation_params:this?.invocationParams(a),batch_size:1},u=await o?.handleChatModelStart(this.toJSON(),n.map(os),i.runId,void 0,l,void 0,void 0,i.runName),d=[],c=(await Promise.allSettled(n.map(async(e,a)=>{let i=oo._convertInputToPromptValue(e).toString(),n=await t.lookup(i,r);return null==n&&d.push(a),n}))).map((e,t)=>({result:e,runManager:u?.[t]})).filter(({result:e})=>"fulfilled"===e.status&&null!=e.value||"rejected"===e.status),h=[];await Promise.all(c.map(async({result:e,runManager:t},r)=>{if("fulfilled"!==e.status)return await t?.handleLLMError(e.reason,void 0,void 0,void 0,{cached:!0}),Promise.reject(e.reason);{let a=e.value;return h[r]=a.map(e=>("message"in e&&eP(e.message)&&eN(e.message)&&(e.message.usage_metadata={input_tokens:0,output_tokens:0,total_tokens:0}),e.generationInfo={...e.generationInfo,tokenUsage:{}},e)),a.length&&await t?.handleLLMNewToken(a[0].text),t?.handleLLMEnd({generations:[a]},void 0,void 0,void 0,{cached:!0})}}));let p={generations:h,missingPromptIndices:d,startedRunManagers:u};return Object.defineProperty(p,iW,{value:u?{runIds:u?.map(e=>e.runId)}:void 0,configurable:!0}),p}async generate(e,t,r){let a;a=Array.isArray(t)?{stop:t}:t;let i=e.map(e=>e.map(eY)),[n,s]=this._separateRunnableConfigFromCallOptionsCompat(a);if(n.callbacks=n.callbacks??r,!this.cache)return this._generateUncached(i,s,n);let{cache:o}=this,l=this._getSerializedCacheKeyParametersForCall(s),{generations:u,missingPromptIndices:d,startedRunManagers:c}=await this._generateCached({messages:i,cache:o,llmStringKey:l,parsedOptions:s,handledOptions:n}),h={};if(d.length>0){let e=await this._generateUncached(d.map(e=>i[e]),s,n,void 0!==c?d.map(e=>c?.[e]):void 0);await Promise.all(e.generations.map(async(e,t)=>{let r=d[t];u[r]=e;let a=oo._convertInputToPromptValue(i[r]).toString();return o.update(a,l,e)})),h=e.llmOutput??{}}return{generations:u,llmOutput:h}}invocationParams(e){return{}}_modelType(){return"base_chat_model"}serialize(){return{...this.invocationParams(),_type:this._llmType(),_model:this._modelType()}}async generatePrompt(e,t,r){let a=e.map(e=>e.toChatMessages());return this.generate(a,t,r)}async call(e,t,r){return(await this.generate([e.map(eY)],t,r)).generations[0][0].message}async callPrompt(e,t,r){let a=e.toChatMessages();return this.call(a,t,r)}async predictMessages(e,t,r){return this.call(e,t,r)}async predict(e,t,r){let a=new eB(e),i=await this.call([a],t,r);if("string"!=typeof i.content)throw Error("Cannot use predict when output is not a string.");return i.content}withStructuredOutput(e,t){let r;if("function"!=typeof this.bindTools)throw Error('Chat model must implement ".bindTools()" to use withStructuredOutput.');if(t?.strict)throw Error('"strict" mode is not supported for this model by default.');let a=t?.name,i=sr(e)??"A function available to call.",n=t?.method,s=t?.includeRaw;if("jsonMode"===n)throw Error('Base withStructuredOutput implementation only supports "functionCalling" as a method.');let o=a??"extract";n7(e)?r=[{type:"function",function:{name:o,description:i,parameters:sn(e)}}]:("name"in e&&(o=e.name),r=[{type:"function",function:{name:o,description:i,parameters:e}}]);let l=this.bindTools(r),u=sE.from(e=>{if(!e.tool_calls||0===e.tool_calls.length)throw Error("No tool calls found in the response.");let t=e.tool_calls.find(e=>e.name===o);if(!t)throw Error(`No tool call found with name ${o}.`);return t.args});if(!s)return l.pipe(u).withConfig({runName:"StructuredOutput"});let d=on.assign({parsed:(e,t)=>u.invoke(e.raw,t)}),c=on.assign({parsed:()=>null}),h=d.withFallbacks({fallbacks:[c]});return sb.from([{raw:l},h]).withConfig({runName:"StructuredOutputRunnable"})}}class ol extends sm{parseResultWithPrompt(e,t,r){return this.parseResult(e,r)}_baseMessageToString(e){return"string"==typeof e.content?e.content:this._baseMessageContentToString(e.content)}_baseMessageContentToString(e){return JSON.stringify(e)}async invoke(e,t){return"string"==typeof e?this._callWithConfig(async(e,t)=>this.parseResult([{text:e}],t?.callbacks),e,{...t,runType:"parser"}):this._callWithConfig(async(e,t)=>this.parseResult([{message:e,text:this._baseMessageToString(e)}],t?.callbacks),e,{...t,runType:"parser"})}}class ou extends ol{parseResult(e,t){return this.parse(e[0].text,t)}async parseWithPrompt(e,t,r){return this.parse(e,r)}_type(){throw Error("_type not implemented")}}class od extends Error{constructor(e,t,r,a=!1){if(super(e),Object.defineProperty(this,"llmOutput",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"observation",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sendToLLM",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.llmOutput=t,this.observation=r,this.sendToLLM=a,a&&(void 0===r||void 0===t))throw Error("Arguments 'observation' & 'llmOutput' are required if 'sendToLlm' is true");eq(this,"OUTPUT_PARSING_FAILURE")}}class oc extends ou{async *_transform(e){for await(let t of e)"string"==typeof t?yield this.parseResult([{text:t}]):yield this.parseResult([{message:t,text:this._baseMessageToString(t)}])}async *transform(e,t){yield*this._transformStreamWithConfig(e,this._transform.bind(this),{...t,runType:"parser"})}}class oh extends oc{constructor(e){super(e),Object.defineProperty(this,"diff",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.diff=e?.diff??this.diff}async *_transform(e){let t,r;for await(let a of e){let e;if("string"!=typeof a&&"string"!=typeof a.content)throw Error("Cannot handle non-string output.");if(eP(a)&&"function"==typeof a.concat){if("string"!=typeof a.content)throw Error("Cannot handle non-string message output.");e=new iK({message:a,text:a.content})}else if(eP(a)){if("string"!=typeof a.content)throw Error("Cannot handle non-string message output.");e=new iK({message:function(e){let t=e._getType();if("human"===t)return new eZ({...e});if("ai"===t){let t={...e};return"tool_calls"in t&&(t={...t,tool_call_chunks:t.tool_calls?.map(e=>({...e,type:"tool_call_chunk",index:void 0,args:JSON.stringify(e.args)}))}),new eM({...t})}if("system"===t)return new eH({...e});if("function"===t)return new ez({...e});if(eU.isInstance(e))return new eD({...e});else throw Error("Unknown message type.")}(a),text:a.content})}else e=new iV({text:a});r=void 0===r?e:r.concat(e);let i=await this.parsePartialResult([r]);null!=i&&!function e(t,r){let a=typeof t;if(a!==typeof r)return!1;if(Array.isArray(t)){if(!Array.isArray(r))return!1;let a=t.length;if(a!==r.length)return!1;for(let i=0;i<a;i++)if(!e(t[i],r[i]))return!1;return!0}if("object"===a){if(!t||!r)return t===r;let a=Object.keys(t),i=Object.keys(r);if(a.length!==i.length)return!1;for(let i of a)if(!e(t[i],r[i]))return!1;return!0}return t===r}(i,t)&&(this.diff?yield this._diff(t,i):yield i,t=i)}}getFormatInstructions(){return""}}class op extends oh{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"JsonOutputParser"}_diff(e,t){if(t)return e?function(e,t,r=!1){var a=[];return aX(e,t,a,"",r),a}(e,t):[{op:"replace",path:"",value:t}]}async parsePartialResult(e){return ey(e[0].text)}async parse(e){return ey(e,JSON.parse)}getFormatInstructions(){return""}}let of=e=>{if(0===Object.keys(e).length)return{};let t={};return e.children.length>0?t[e.name]=e.children.map(of):t[e.name]=e.text??void 0,t};function om(e){if("object"==typeof e&&null!==e){let t={...e};for(let e in"additionalProperties"in t&&delete t.additionalProperties,"$schema"in t&&delete t.$schema,"strict"in t&&delete t.strict,t)e in t&&(Array.isArray(t[e])?t[e]=t[e].map(om):"object"==typeof t[e]&&null!==t[e]&&(t[e]=om(t[e])));return t}return e}function og(e){let{$schema:t,...r}=om(n7(e)?sn(e):e);return r}function oy(e){return!!e&&"object"==typeof e&&"name"in e&&"schema"in e&&(n7(e.schema)||null!=e.schema&&"object"==typeof e.schema&&"type"in e.schema&&"string"==typeof e.schema.type&&["null","boolean","object","array","number","string"].includes(e.schema.type))||void 0!==e&&sm.isRunnable(e)&&"lc_name"in e.constructor&&"function"==typeof e.constructor.lc_name&&"RunnableToolLike"===e.constructor.lc_name()||void 0!==e&&Array.isArray(e.lc_namespace)}var o_=r(23870);function ob(e,t){if(ex(e)){var r={providerName:"Google Gemini",fromStandardTextBlock:e=>({text:e.text}),fromStandardImageBlock(e){if(!t)throw Error("This model does not support images");if("url"===e.source_type){let t=eT({dataUrl:e.url});return t?{inlineData:{mimeType:t.mime_type,data:t.data}}:{fileData:{mimeType:e.mime_type??"",fileUri:e.url}}}if("base64"===e.source_type)return{inlineData:{mimeType:e.mime_type??"",data:e.data}};throw Error(`Unsupported source type: ${e.source_type}`)},fromStandardAudioBlock(e){if(!t)throw Error("This model does not support audio");if("url"===e.source_type){let t=eT({dataUrl:e.url});return t?{inlineData:{mimeType:t.mime_type,data:t.data}}:{fileData:{mimeType:e.mime_type??"",fileUri:e.url}}}if("base64"===e.source_type)return{inlineData:{mimeType:e.mime_type??"",data:e.data}};throw Error(`Unsupported source type: ${e.source_type}`)},fromStandardFileBlock(e){if(!t)throw Error("This model does not support files");if("text"===e.source_type)return{text:e.text};if("url"===e.source_type){let t=eT({dataUrl:e.url});return t?{inlineData:{mimeType:t.mime_type,data:t.data}}:{fileData:{mimeType:e.mime_type??"",fileUri:e.url}}}if("base64"===e.source_type)return{inlineData:{mimeType:e.mime_type??"",data:e.data}};throw Error(`Unsupported source type: ${e.source_type}`)}};if("text"===e.type){if(!r.fromStandardTextBlock)throw Error(`Converter for ${r.providerName} does not implement \`fromStandardTextBlock\` method.`);return r.fromStandardTextBlock(e)}if("image"===e.type){if(!r.fromStandardImageBlock)throw Error(`Converter for ${r.providerName} does not implement \`fromStandardImageBlock\` method.`);return r.fromStandardImageBlock(e)}if("audio"===e.type){if(!r.fromStandardAudioBlock)throw Error(`Converter for ${r.providerName} does not implement \`fromStandardAudioBlock\` method.`);return r.fromStandardAudioBlock(e)}if("file"===e.type){if(!r.fromStandardFileBlock)throw Error(`Converter for ${r.providerName} does not implement \`fromStandardFileBlock\` method.`);return r.fromStandardFileBlock(e)}throw Error(`Unable to convert content block type '${e.type}' to provider-specific format: not recognized.`)}if("text"===e.type)return{text:e.text};if("executableCode"===e.type)return{executableCode:e.executableCode};if("codeExecutionResult"===e.type)return{codeExecutionResult:e.codeExecutionResult};if("image_url"===e.type){let r;if(!t)throw Error("This model does not support images");if("string"==typeof e.image_url)r=e.image_url;else if("object"==typeof e.image_url&&"url"in e.image_url)r=e.image_url.url;else throw Error("Please provide image as base64 encoded data URL");let[a,i]=r.split(",");if(!a.startsWith("data:"))throw Error("Please provide image as base64 encoded data URL");let[n,s]=a.replace(/^data:/,"").split(";");if("base64"!==s)throw Error("Please provide image as base64 encoded data URL");return{inlineData:{data:i,mimeType:n}}}if("media"===e.type){if("mimeType"in e&&"data"in e)return{inlineData:{mimeType:e.mimeType,data:e.data}};if("mimeType"in e&&"fileUri"in e)return{fileData:{mimeType:e.mimeType,fileUri:e.fileUri}};throw Error("Invalid media content")}else if("tool_use"===e.type)return{functionCall:{name:e.name,args:e.input}};else if(e.type?.includes("/")&&2===e.type.split("/").length&&"data"in e&&"string"==typeof e.data)return{inlineData:{mimeType:e.type,data:e.data}};else if("functionCall"in e)return;else if("type"in e)throw Error(`Unknown content type ${e.type}`);else throw Error(`Unknown content ${JSON.stringify(e)}`)}function ov(e,t,r=!1){return e.reduce((a,i,n)=>{if(!eP(i))throw Error("Unsupported message input");let s=function(e){let t=e._getType();return eU.isInstance(e)?e.role:"tool"===t?t:e.name??t}(i);if("system"===s&&0!==n)throw Error("System message should be the first one");let o=function(e){switch(e){case"supervisor":case"ai":case"model":return"model";case"system":return"system";case"human":return"user";case"tool":case"function":return"function";default:throw Error(`Unknown / unsupported author: ${e}`)}}(s),l=a.content[a.content.length];if(!a.mergeWithPreviousContent&&l&&l.role===o)throw Error("Google Generative AI requires alternate messages between authors");let u=function(e,t,r){if("tool"===e._getType()){let a=e.name??r.map(e=>eN(e)?e.tool_calls??[]:[]).flat().find(t=>t.id===e.tool_call_id)?.name;if(void 0===a)throw Error(`Google requires a tool name for each tool call response, and we could not infer a called tool name for ToolMessage "${e.id}" from your passed messages. Please populate a "name" field on that ToolMessage explicitly.`);let i=Array.isArray(e.content)?e.content.map(e=>ob(e,t)).filter(e=>void 0!==e):e.content;return"error"===e.status?[{functionResponse:{name:a,response:{error:{details:i}}}}]:[{functionResponse:{name:a,response:{result:i}}}]}let a=[],i=[];return"string"==typeof e.content&&e.content&&i.push({text:e.content}),Array.isArray(e.content)&&i.push(...e.content.map(e=>ob(e,t)).filter(e=>void 0!==e)),eN(e)&&e.tool_calls?.length&&(a=e.tool_calls.map(e=>({functionCall:{name:e.name,args:e.args}}))),[...i,...a]}(i,t,e.slice(0,n));if(a.mergeWithPreviousContent){let e=a.content[a.content.length-1];if(!e)throw Error("There was a problem parsing your system message. Please try a prompt without one.");return e.parts.push(...u),{mergeWithPreviousContent:!1,content:a.content}}let d=o;"function"!==d&&("system"!==d||r)||(d="user");let c={role:d,parts:u};return{mergeWithPreviousContent:"system"===s&&!r,content:[...a.content,c]}},{content:[],mergeWithPreviousContent:!1}).content}class ow extends ol{static lc_name(){return"GoogleGenerativeAIToolsOutputParser"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","google_genai","output_parsers"]}),Object.defineProperty(this,"returnId",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"keyName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"returnSingle",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"zodSchema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keyName=e.keyName,this.returnSingle=e.returnSingle??this.returnSingle,this.zodSchema=e.zodSchema}async _validateResult(e){if(void 0===this.zodSchema)return e;let t=await se(this.zodSchema,e);if(t.success)return t.data;throw new od(`Failed to parse. Text: "${JSON.stringify(e,null,2)}". Error: ${JSON.stringify(t.error.issues)}`,JSON.stringify(e,null,2))}async parseResult(e){let t=e.flatMap(e=>{let{message:t}=e;return"tool_calls"in t&&Array.isArray(t.tool_calls)?t.tool_calls:[]});if(void 0===t[0])throw Error("No parseable tool calls provided to GoogleGenerativeAIToolsOutputParser.");let[r]=t;return await this._validateResult(r.args)}}function oE(e,t){let r=function(e){let t=[],r=[];return(e.forEach(e=>{var a,i;if(oy(e)){let[r]=(a=[e]).every(e=>"functionDeclarations"in e&&Array.isArray(e.functionDeclarations))?a:[{functionDeclarations:a.map(e=>{if(oy(e)){let t=og(e.schema);return"object"===t.type&&"properties"in t&&0===Object.keys(t.properties).length?{name:e.name,description:e.description}:{name:e.name,description:e.description,parameters:t}}return ot(e)?{name:e.function.name,description:e.function.description??"A function available to call.",parameters:function(e){let{$schema:t,...r}=om(e);return r}(e.function.parameters)}:e})}];r.functionDeclarations&&t.push(...r.functionDeclarations)}else if(ot(e)){let{functionDeclarations:r}={functionDeclarations:[{name:(i=e).function.name,description:i.function.description,parameters:om(i.function.parameters)}]};if(r)t.push(...r);else throw Error("Failed to convert OpenAI structured tool to GenerativeAI tool")}else r.push(e)}),r.find(e=>"functionDeclarations"in e))?r.map(e=>{if(t?.length>0&&"functionDeclarations"in e){let r={functionDeclarations:[...e.functionDeclarations||[],...t]};return t=[],r}return e}):[...r,...t.length>0?[{functionDeclarations:t}]:[]]}(e),a=function(e,t){if(!e.length||!t)return;let{toolChoice:r,allowedFunctionNames:a}=t,i={any:v.ANY,auto:v.AUTO,none:v.NONE};return r&&["any","auto","none"].includes(r)?{functionCallingConfig:{mode:i[r]??"MODE_UNSPECIFIED",allowedFunctionNames:a}}:"string"==typeof r||a?{functionCallingConfig:{mode:v.ANY,allowedFunctionNames:[...a??[],...r&&"string"==typeof r?[r]:[]]}}:void 0}(r,t);return{tools:r,toolConfig:a}}class oO extends oo{static lc_name(){return"ChatGoogleGenerativeAI"}get lc_secrets(){return{apiKey:"GOOGLE_API_KEY"}}get lc_aliases(){return{apiKey:"google_api_key"}}get _isMultimodalModel(){return this.model.includes("vision")||this.model.startsWith("gemini-1.5")||this.model.startsWith("gemini-2")}constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","chat_models","google_genai"]}),Object.defineProperty(this,"model",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"temperature",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxOutputTokens",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"topP",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"topK",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stopSequences",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"safetySettings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"streaming",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"json",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"streamUsage",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"convertSystemMessageToHumanContent",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.model=e.model.replace(/^models\//,""),this.maxOutputTokens=e.maxOutputTokens??this.maxOutputTokens,this.maxOutputTokens&&this.maxOutputTokens<0)throw Error("`maxOutputTokens` must be a positive integer");if(this.temperature=e.temperature??this.temperature,this.temperature&&(this.temperature<0||this.temperature>2))throw Error("`temperature` must be in the range of [0.0,2.0]");if(this.topP=e.topP??this.topP,this.topP&&this.topP<0)throw Error("`topP` must be a positive integer");if(this.topP&&this.topP>1)throw Error("`topP` must be below 1.");if(this.topK=e.topK??this.topK,this.topK&&this.topK<0)throw Error("`topK` must be a positive integer");if(this.stopSequences=e.stopSequences??this.stopSequences,this.apiKey=e.apiKey??eg("GOOGLE_API_KEY"),!this.apiKey)throw Error("Please set an API key for Google GenerativeAI in the environment variable GOOGLE_API_KEY or in the `apiKey` field of the ChatGoogleGenerativeAI constructor");if(this.safetySettings=e.safetySettings??this.safetySettings,this.safetySettings&&this.safetySettings.length>0&&new Set(this.safetySettings.map(e=>e.category)).size!==this.safetySettings.length)throw Error("The categories in `safetySettings` array must be unique");this.streaming=e.streaming??this.streaming,this.json=e.json,this.client=new eu(this.apiKey).getGenerativeModel({model:this.model,safetySettings:this.safetySettings,generationConfig:{stopSequences:this.stopSequences,maxOutputTokens:this.maxOutputTokens,temperature:this.temperature,topP:this.topP,topK:this.topK,...this.json?{responseMimeType:"application/json"}:{}}},{apiVersion:e.apiVersion,baseUrl:e.baseUrl}),this.streamUsage=e.streamUsage??this.streamUsage}useCachedContent(e,t,r){this.apiKey&&(this.client=new eu(this.apiKey).getGenerativeModelFromCachedContent(e,t,r))}get useSystemInstruction(){return"boolean"==typeof this.convertSystemMessageToHumanContent?!this.convertSystemMessageToHumanContent:this.computeUseSystemInstruction}get computeUseSystemInstruction(){return!("gemini-1.0-pro-001"===this.model||this.model.startsWith("gemini-pro-vision"))&&!this.model.startsWith("gemini-1.0-pro-vision")&&"gemini-pro"!==this.model&&!0}getLsParams(e){return{ls_provider:"google_genai",ls_model_name:this.model,ls_model_type:"chat",ls_temperature:this.client.generationConfig.temperature,ls_max_tokens:this.client.generationConfig.maxOutputTokens,ls_stop:e.stop}}_combineLLMOutput(){return[]}_llmType(){return"googlegenerativeai"}bindTools(e,t){return this.withConfig({tools:oE(e)?.tools,...t})}invocationParams(e){let t=e?.tools?.length?oE(e.tools,{toolChoice:e.tool_choice,allowedFunctionNames:e.allowedFunctionNames}):void 0;return e?.responseSchema?(this.client.generationConfig.responseSchema=e.responseSchema,this.client.generationConfig.responseMimeType="application/json"):(this.client.generationConfig.responseSchema=void 0,this.client.generationConfig.responseMimeType=this.json?"application/json":void 0),{...t?.tools?{tools:t.tools}:{},...t?.toolConfig?{toolConfig:t.toolConfig}:{}}}async _generate(e,t,r){let a,i=ov(e,this._isMultimodalModel,this.useSystemInstruction),n=i;if("system"===i[0].role){let[e]=i;this.client.systemInstruction=e,n=i.slice(1)}let s=this.invocationParams(t);if(this.streaming){let a=this._streamResponseChunks(e,t,r),i={};for await(let e of a){let t=e.generationInfo?.completion??0;void 0===i[t]?i[t]=e:i[t]=i[t].concat(e)}return{generations:Object.entries(i).sort(([e],[t])=>parseInt(e,10)-parseInt(t,10)).map(([e,t])=>t),llmOutput:{estimatedTokenUsage:{}}}}let o=await this.completionWithRetry({...s,contents:n});if("usageMetadata"in o.response){let e=o.response.usageMetadata;a={input_tokens:e.promptTokenCount??0,output_tokens:e.candidatesTokenCount??0,total_tokens:e.totalTokenCount??0}}let l=function(e,t){let r;if(!e.candidates||0===e.candidates.length||!e.candidates[0])return{generations:[],llmOutput:{filters:e.promptFeedback}};let a=e.functionCalls(),[i]=e.candidates,{content:n,...s}=i;r=Array.isArray(n?.parts)&&1===n.parts.length&&n.parts[0].text?n.parts[0].text:Array.isArray(n?.parts)&&n.parts.length>0?n.parts.map(e=>"text"in e?{type:"text",text:e.text}:"executableCode"in e?{type:"executableCode",executableCode:e.executableCode}:"codeExecutionResult"in e?{type:"codeExecutionResult",codeExecutionResult:e.codeExecutionResult}:e):[];let o="";if("string"==typeof r)o=r;else if(Array.isArray(r)&&r.length>0){let e=r.find(e=>"text"in e);o=e?.text??o}return{generations:[{text:o,message:new e$({content:r??"",tool_calls:a?.map(e=>({...e,type:"tool_call",id:"id"in e&&"string"==typeof e.id?e.id:(0,o_.A)()})),additional_kwargs:{...s},usage_metadata:t?.usageMetadata}),generationInfo:s}],llmOutput:{tokenUsage:{promptTokens:t?.usageMetadata?.input_tokens,completionTokens:t?.usageMetadata?.output_tokens,totalTokens:t?.usageMetadata?.total_tokens}}}}(o.response,{usageMetadata:a});return l.generations?.length>0&&await r?.handleLLMNewToken(l.generations[0]?.text??""),l}async *_streamResponseChunks(e,t,r){let a,i=ov(e,this._isMultimodalModel,this.useSystemInstruction),n=i;if("system"===i[0].role){let[e]=i;this.client.systemInstruction=e,n=i.slice(1)}let s={...this.invocationParams(t),contents:n},o=await this.caller.callWithOptions({signal:t?.signal},async()=>{let{stream:e}=await this.client.generateContentStream(s);return e}),l=0;for await(let e of o){if("usageMetadata"in e&&!1!==this.streamUsage&&!1!==t.streamUsage){let t=e.usageMetadata;if(a){let e=(t.candidatesTokenCount??0)-a.output_tokens;a={input_tokens:0,output_tokens:e,total_tokens:e}}else a={input_tokens:t.promptTokenCount??0,output_tokens:t.candidatesTokenCount??0,total_tokens:t.totalTokenCount??0}}let i=function(e,t){let r;if(!e.candidates||0===e.candidates.length)return null;let a=e.functionCalls(),[i]=e.candidates,{content:n,...s}=i;r=Array.isArray(n?.parts)&&n.parts.every(e=>"text"in e)?n.parts.map(e=>e.text).join(""):Array.isArray(n?.parts)?n.parts.map(e=>"text"in e?{type:"text",text:e.text}:"executableCode"in e?{type:"executableCode",executableCode:e.executableCode}:"codeExecutionResult"in e?{type:"codeExecutionResult",codeExecutionResult:e.codeExecutionResult}:e):[];let o="";if(r&&"string"==typeof r)o=r;else if(Array.isArray(r)){let e=r.find(e=>"text"in e);o=e?.text??""}let l=[];return a&&l.push(...a.map(e=>({...e,args:JSON.stringify(e.args),index:t.index,type:"tool_call_chunk",id:"id"in e&&"string"==typeof e.id?e.id:(0,o_.A)()}))),new iK({text:o,message:new eM({content:r||"",name:n?n.role:void 0,tool_call_chunks:l,additional_kwargs:{},usage_metadata:t.usageMetadata}),generationInfo:s})}(e,{usageMetadata:a,index:l});l+=1,i&&(yield i,await r?.handleLLMNewToken(i.text??""))}}async completionWithRetry(e,t){return this.caller.callWithOptions({signal:t?.signal},async()=>{try{return await this.client.generateContent(e)}catch(e){throw e.message?.includes("400 Bad Request")&&(e.status=400),e}})}withStructuredOutput(e,t){let r,a,i=t?.name,n=t?.method,s=t?.includeRaw;if("jsonMode"===n)throw Error('ChatGoogleGenerativeAI only supports "jsonSchema" or "functionCalling" as a method.');if("functionCalling"===n){let t,n=i??"extract";if(n7(e)){let r=og(e);t=[{functionDeclarations:[{name:n,description:r.description??"A function available to call.",parameters:r}]}],a=new ow({returnSingle:!0,keyName:n,zodSchema:e})}else{let r;"string"==typeof e.name&&"object"==typeof e.parameters&&null!=e.parameters?((r=e).parameters=om(e.parameters),n=e.name):r={name:n,description:e.description??"",parameters:om(e)},t=[{functionDeclarations:[r]}],a=new ow({returnSingle:!0,keyName:n})}r=this.bindTools(t).withConfig({allowedFunctionNames:[n]})}else{let t=og(e);r=this.withConfig({responseSchema:t}),a=new op}if(!s)return r.pipe(a).withConfig({runName:"ChatGoogleGenerativeAIStructuredOutput"});let o=on.assign({parsed:(e,t)=>a.invoke(e.raw,t)}),l=on.assign({parsed:()=>null}),u=o.withFallbacks({fallbacks:[l]});return sb.from([{raw:r},u]).withConfig({runName:"StructuredOutputRunnable"})}}},51862:e=>{"use strict";let t=/[\p{Lu}]/u,r=/[\p{Ll}]/u,a=/^[\p{Lu}](?![\p{Lu}])/gu,i=/([\p{Alpha}\p{N}_]|$)/u,n=/[_.\- ]+/,s=RegExp("^"+n.source),o=RegExp(n.source+i.source,"gu"),l=RegExp("\\d+"+i.source,"gu"),u=(e,a,i)=>{let n=!1,s=!1,o=!1;for(let l=0;l<e.length;l++){let u=e[l];n&&t.test(u)?(e=e.slice(0,l)+"-"+e.slice(l),n=!1,o=s,s=!0,l++):s&&o&&r.test(u)?(e=e.slice(0,l-1)+"-"+e.slice(l-1),o=s,s=!1,n=!0):(n=a(u)===u&&i(u)!==u,o=s,s=i(u)===u&&a(u)!==u)}return e},d=(e,t)=>(a.lastIndex=0,e.replace(a,e=>t(e))),c=(e,t)=>(o.lastIndex=0,l.lastIndex=0,e.replace(o,(e,r)=>t(r)).replace(l,e=>t(e))),h=(e,t)=>{if(!("string"==typeof e||Array.isArray(e)))throw TypeError("Expected the input to be `string | string[]`");if(t={pascalCase:!1,preserveConsecutiveUppercase:!1,...t},0===(e=Array.isArray(e)?e.map(e=>e.trim()).filter(e=>e.length).join("-"):e.trim()).length)return"";let r=!1===t.locale?e=>e.toLowerCase():e=>e.toLocaleLowerCase(t.locale),a=!1===t.locale?e=>e.toUpperCase():e=>e.toLocaleUpperCase(t.locale);return 1===e.length?t.pascalCase?a(e):r(e):(e!==r(e)&&(e=u(e,r,a)),e=e.replace(s,""),e=t.preserveConsecutiveUppercase?d(e,r):r(e),t.pascalCase&&(e=a(e.charAt(0))+e.slice(1)),c(e,a))};e.exports=h,e.exports.default=h},55332:(e,t,r)=>{var a=r(81174);t.operation=function(e){return new a(t.timeouts(e),{forever:e&&(e.forever||e.retries===1/0),unref:e&&e.unref,maxRetryTime:e&&e.maxRetryTime})},t.timeouts=function(e){if(e instanceof Array)return[].concat(e);var t={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var r in e)t[r]=e[r];if(t.minTimeout>t.maxTimeout)throw Error("minTimeout is greater than maxTimeout");for(var a=[],i=0;i<t.retries;i++)a.push(this.createTimeout(i,t));return e&&e.forever&&!a.length&&a.push(this.createTimeout(i,t)),a.sort(function(e,t){return e-t}),a},t.createTimeout=function(e,t){var r=Math.round((t.randomize?Math.random()+1:1)*Math.max(t.minTimeout,1)*Math.pow(t.factor,e));return Math.min(r,t.maxTimeout)},t.wrap=function(e,r,a){if(r instanceof Array&&(a=r,r=null),!a)for(var i in a=[],e)"function"==typeof e[i]&&a.push(i);for(var n=0;n<a.length;n++){var s=a[n],o=e[s];e[s]=(function(a){var i=t.operation(r),n=Array.prototype.slice.call(arguments,1),s=n.pop();n.push(function(e){i.retry(e)||(e&&(arguments[0]=i.mainError()),s.apply(this,arguments))}),i.attempt(function(){a.apply(e,n)})}).bind(e,o),e[s].options=r}}},57543:e=>{"use strict";e.exports=function(e,t){if("string"!=typeof e)throw TypeError("Expected a string");return t=void 0===t?"_":t,e.replace(/([a-z\d])([A-Z])/g,"$1"+t+"$2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g,"$1"+t+"$2").toLowerCase()}},58361:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r=!1)=>{if(e instanceof a)return e;try{return new a(e,t)}catch(e){if(!r)return null;throw e}}},60301:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0>=a(e,t,r)},62502:(e,t,r)=>{"use strict";let a=r(25706);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,r)=>new Promise((n,s)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof r){try{n(r())}catch(e){s(e)}return}let a="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(a);"function"==typeof e.cancel&&e.cancel(),s(o)},t);a(e.then(n,s),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=i},63391:(e,t,r)=>{"use strict";e=r.nmd(e);let a=(e=0)=>t=>`\u001B[${38+e};5;${t}m`,i=(e=0)=>(t,r,a)=>`\u001B[${38+e};2;${t};${r};${a}m`;Object.defineProperty(e,"exports",{enumerable:!0,get:function(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};for(let[r,a]of(t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright,Object.entries(t))){for(let[r,i]of Object.entries(a))t[r]={open:`\u001B[${i[0]}m`,close:`\u001B[${i[1]}m`},a[r]=t[r],e.set(i[0],i[1]);Object.defineProperty(t,r,{value:a,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1b[39m",t.bgColor.close="\x1b[49m",t.color.ansi256=a(),t.color.ansi16m=i(),t.bgColor.ansi256=a(10),t.bgColor.ansi16m=i(10),Object.defineProperties(t,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{let t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map(e=>e+e).join(""));let a=Number.parseInt(r,16);return[a>>16&255,a>>8&255,255&a]},enumerable:!1},hexToAnsi256:{value:e=>t.rgbToAnsi256(...t.hexToRgb(e)),enumerable:!1}}),t}})},63611:(e,t,r)=>{"use strict";let a=r(23518),i=["Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Network request failed"];class n extends Error{constructor(e){super(),e instanceof Error?(this.originalError=e,{message:e}=e):(this.originalError=Error(e),this.originalError.stack=this.stack),this.name="AbortError",this.message=e}}let s=(e,t,r)=>{let a=r.retries-(t-1);return e.attemptNumber=t,e.retriesLeft=a,e},o=e=>i.includes(e),l=(e,t)=>new Promise((r,i)=>{t={onFailedAttempt:()=>{},retries:10,...t};let l=a.operation(t);l.attempt(async a=>{try{r(await e(a))}catch(e){if(!(e instanceof Error))return void i(TypeError(`Non-error was thrown: "${e}". You should only throw errors.`));if(e instanceof n)l.stop(),i(e.originalError);else if(e instanceof TypeError&&!o(e.message))l.stop(),i(e);else{s(e,a,t);try{await t.onFailedAttempt(e)}catch(e){i(e);return}l.retry(e)||i(l.mainError())}}})});e.exports=l,e.exports.default=l,e.exports.AbortError=n},64487:(e,t,r)=>{"use strict";let a=r(38267),{MAX_LENGTH:i,MAX_SAFE_INTEGER:n}=r(32397),{safeRe:s,t:o}=r(26515),l=r(98300),{compareIdentifiers:u}=r(78668);class d{constructor(e,t){if(t=l(t),e instanceof d)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else e=e.version;else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>i)throw TypeError(`version is longer than ${i} characters`);a("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?s[o.LOOSE]:s[o.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<n)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(a("SemVer.compare",this.version,this.options,e),!(e instanceof d)){if("string"==typeof e&&e===this.version)return 0;e=new d(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof d||(e=new d(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof d||(e=new d(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],i=e.prerelease[t];if(a("prerelease compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;else if(r===i)continue;else return u(r,i)}while(++t)}compareBuild(e){e instanceof d||(e=new d(e,this.options));let t=0;do{let r=this.build[t],i=e.build[t];if(a("build compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;else if(r===i)continue;else return u(r,i)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=`-${t}`.match(this.options.loose?s[o.PRERELEASELOOSE]:s[o.PRERELEASE]);if(!e||e[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(r);if(0===this.prerelease.length)this.prerelease=[e];else{let a=this.prerelease.length;for(;--a>=0;)"number"==typeof this.prerelease[a]&&(this.prerelease[a]++,a=-2);if(-1===a){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let a=[t,e];!1===r&&(a=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=a):this.prerelease=a}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=d},71505:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t,r)=>(e=new a(e,r),t=new a(t,r),e.intersects(t,r))},71611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let a=0,i=e.length;for(;i>0;){let n=i/2|0,s=a+n;0>=r(e[s],t)?(a=++s,i-=n+1):i=n}return a}},71719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let a=r(339),i=r(62502),n=r(12441),s=()=>{},o=new i.TimeoutError;class l extends a{constructor(e){var t,r,a,i;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:n.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(r=null==(t=e.intervalCap)?void 0:t.toString())?r:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(i=null==(a=e.interval)?void 0:a.toString())?i:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,t={}){return new Promise((r,a)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===t.timeout?e():i.default(Promise.resolve(e()),void 0===t.timeout?this._timeout:t.timeout,()=>{(void 0===t.throwOnTimeout?this._throwOnTimeout:t.throwOnTimeout)&&a(o)});r(await n)}catch(e){a(e)}this._next()};this._queue.enqueue(n,t),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}t.default=l},73051:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},73438:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0===a(e,t,r)},77860:(e,t,r)=>{"use strict";let a=r(42679),i=r(33877);e.exports=(e,t,r)=>{let n=[],s=null,o=null,l=e.sort((e,t)=>i(e,t,r));for(let e of l)a(e,t,r)?(o=e,s||(s=e)):(o&&n.push([s,o]),o=null,s=null);s&&n.push([s,null]);let u=[];for(let[e,t]of n)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let d=u.join(" || "),c="string"==typeof t.raw?t.raw:String(t);return d.length<c.length?d:t}},78172:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).patch},78668:e=>{"use strict";let t=/^[0-9]+$/,r=(e,r)=>{let a=t.test(e),i=t.test(r);return a&&i&&(e*=1,r*=1),e===r?0:a&&!i?-1:i&&!a?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},81174:e=>{function t(e,t){"boolean"==typeof t&&(t={forever:t}),this._originalTimeouts=JSON.parse(JSON.stringify(e)),this._timeouts=e,this._options=t||{},this._maxRetryTime=t&&t.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}e.exports=t,t.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},t.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},t.prototype.retry=function(e){if(this._timeout&&clearTimeout(this._timeout),!e)return!1;var t=new Date().getTime();if(e&&t-this._operationStart>=this._maxRetryTime)return this._errors.push(e),this._errors.unshift(Error("RetryOperation timeout occurred")),!1;this._errors.push(e);var r=this._timeouts.shift();if(void 0===r)if(!this._cachedTimeouts)return!1;else this._errors.splice(0,this._errors.length-1),r=this._cachedTimeouts.slice(-1);var a=this;return this._timer=setTimeout(function(){a._attempts++,a._operationTimeoutCb&&(a._timeout=setTimeout(function(){a._operationTimeoutCb(a._attempts)},a._operationTimeout),a._options.unref&&a._timeout.unref()),a._fn(a._attempts)},r),this._options.unref&&this._timer.unref(),!0},t.prototype.attempt=function(e,t){this._fn=e,t&&(t.timeout&&(this._operationTimeout=t.timeout),t.cb&&(this._operationTimeoutCb=t.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)},t.prototype.try=function(e){console.log("Using RetryOperation.try() is deprecated"),this.attempt(e)},t.prototype.start=function(e){console.log("Using RetryOperation.start() is deprecated"),this.attempt(e)},t.prototype.start=t.prototype.try,t.prototype.errors=function(){return this._errors},t.prototype.attempts=function(){return this._attempts},t.prototype.mainError=function(){if(0===this._errors.length)return null;for(var e={},t=null,r=0,a=0;a<this._errors.length;a++){var i=this._errors[a],n=i.message,s=(e[n]||0)+1;e[n]=s,s>=r&&(t=i,r=s)}return t}},84450:(e,t,r)=>{"use strict";let a=r(73438),i=r(27290),n=r(42699),s=r(44156),o=r(40720),l=r(60301);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return a(e,r,u);case"!=":return i(e,r,u);case">":return n(e,r,u);case">=":return s(e,r,u);case"<":return o(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},86605:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(t,e,r)},90726:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r,i,n)=>{"string"==typeof r&&(n=i,i=r,r=void 0);try{return new a(e instanceof a?e.version:e,r).inc(t,i,n).version}catch(e){return null}}},93419:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,null,!0),i=a(t,null,!0),n=r.compare(i);if(0===n)return null;let s=n>0,o=s?r:i,l=s?i:r,u=!!o.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(o))return l.minor&&!l.patch?"minor":"patch"}let d=u?"pre":"";return r.major!==i.major?d+"major":r.minor!==i.minor?d+"minor":r.patch!==i.patch?d+"patch":"prerelease"}},98300:e=>{"use strict";let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r}};