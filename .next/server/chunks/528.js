exports.id=528,exports.ids=[528],exports.modules={8116:(e,t,r)=>{"use strict";r.d(t,{l:()=>R});var a=r(71682),s=r(32767),i=r(94634);let n={personalityInsight:`
你是一位专业的心理学家和人格分析师。请基于用户的详细资料，生成一份深度的"灵魂画像"。

用户资料：
姓名: {name}
年龄: {age}
性别: {gender}
自我描述: {selfDescription}
兴趣爱好: {interests}
价值观: {values}
生活方式: {lifestyle}
感情目标: {relationshipGoals}

请分析并生成：
1. 核心人格特质（5-7个关键词）
2. 沟通风格描述
3. 价值观体系
4. 情感需求
5. 生活态度
6. 一句话人格总结

请以JSON格式返回：
{
  "personalityTraits": {
    "openness": 0.8,
    "conscientiousness": 0.7,
    "extraversion": 0.6,
    "agreeableness": 0.9,
    "neuroticism": 0.3
  },
  "coreValues": ["诚实", "成长", "家庭"],
  "communicationStyle": "温和而深度的交流者",
  "emotionalNeeds": ["理解", "支持", "共同成长"],
  "lifeAttitude": "积极向上，注重内在成长",
  "summary": "一个温暖、有深度、追求真实连接的灵魂"
}
`,compatibilityInference:`
你是一位资深的情感匹配专家。请分析两个人的兼容性。

用户A的灵魂画像：
{userSoulProfile}

候选人B的资料：
姓名: {candidateName}
年龄: {candidateAge}
自我描述: {candidateSelfDescription}
兴趣爱好: {candidateInterests}
价值观: {candidateValues}
生活方式: {candidateLifestyle}

请进行深度分析并给出：
1. 兼容性分数 (0-100)
2. 详细推理过程
3. 关系亮点 (3-5个)
4. 潜在挑战 (2-3个)
5. 候选人人格摘要

请以JSON格式返回：
{
  "compatibilityScore": 85,
  "reasoning": "详细的兼容性分析...",
  "highlights": ["共同的价值观", "互补的性格", "相似的生活目标"],
  "challenges": ["沟通方式差异", "生活节奏不同"],
  "personalitySummary": "一个温暖、独立、有创造力的人"
}
`,relationshipHighlight:`
基于兼容性分析，请深入挖掘这段关系的潜力。

用户A: {userSoulProfile}
候选人B: {candidateAnalysis}

请提供：
1. 关系优势 (3-4个具体方面)
2. 成长机会 (2-3个)
3. 相处建议 (3-4条实用建议)
4. 沟通技巧 (2-3个针对性建议)

请以JSON格式返回：
{
  "strengths": ["深度的精神连接", "互补的技能组合"],
  "growthOpportunities": ["共同探索新兴趣", "相互学习不同视角"],
  "suggestions": ["定期深度对话", "尊重彼此的独立空间"],
  "communicationTips": ["使用'我'语句表达感受", "积极倾听对方观点"]
}
`,conversationSimulation:`
请模拟用户A和候选人B的一段自然对话。

用户A资料: {userProfile}
候选人B资料: {candidateProfile}
对话场景: {scenario}

要求：
1. 对话要体现双方的性格特点
2. 包含6-8轮对话
3. 展现自然的互动和化学反应
4. 体现共同兴趣或价值观

请以JSON格式返回：
{
  "scenario": "咖啡厅初次见面",
  "messages": [
    {"speaker": "user", "content": "...", "emotion": "好奇"},
    {"speaker": "candidate", "content": "...", "emotion": "友善"}
  ],
  "analysis": "这段对话展现了双方的..."
}
`,datePlanGeneration:`
基于两人的共同兴趣和性格特点，设计一个完美的初次约会计划。

用户A: {userProfile}
候选人B: {candidateProfile}
共同兴趣: {commonInterests}

请设计：
1. 约会主题和地点
2. 具体活动安排
3. 时间规划
4. 预算建议
5. 为什么这个计划适合他们

请以JSON格式返回：
{
  "title": "艺术与咖啡的邂逅",
  "description": "结合艺术欣赏和深度交流的约会",
  "location": "市中心艺术区",
  "activities": ["参观画廊", "咖啡厅聊天", "街头艺术漫步"],
  "duration": "3-4小时",
  "budget": "200-300元",
  "reasoning": "这个计划结合了双方对艺术的热爱..."
}
`},o={default:`你是寡佬AI的专业红娘助手，专门负责深度的情感匹配分析。你的分析要准确、深入、有洞察力，同时保持温暖和专业的语调。请始终以JSON格式返回结构化的结果。`,personality:`你是一位专业的心理学家，擅长人格分析和深度洞察。你的分析要基于心理学理论，同时具有实用性。`,compatibility:`你是一位资深的情感匹配专家，拥有丰富的关系咨询经验。你的分析要客观、全面，既看到优势也识别挑战。`,conversation:`你是一位对话专家，擅长模拟真实的人际互动。你的对话要自然、有趣，体现人物的真实性格。`,dating:`你是一位约会策划专家，了解各种约会形式和场所。你的建议要实用、有创意，适合不同性格的人。`};var l=r(44633);let c=(e=o.default)=>new l.y({model:"gemini-1.5-pro",temperature:.7,maxOutputTokens:2048,apiKey:process.env.GEMINI_API_KEY});async function d(e){try{console.log("\uD83E\uDDE0 执行人格洞察 Agent...");let{userProfile:t}=e;if(!t)throw Error("用户资料不存在");let r=c(o.personality),a=n.personalityInsight.replace("{name}",t.name||"未知").replace("{age}",t.age?.toString()||"未知").replace("{gender}",t.gender||"未知").replace("{selfDescription}",t.selfDescription||"").replace("{interests}",JSON.stringify(t.interests||[])).replace("{values}",JSON.stringify(t.values||[])).replace("{lifestyle}",JSON.stringify(t.lifestyle||{})).replace("{relationshipGoals}",t.relationshipGoals||""),s=await r.invoke([{role:"system",content:o.personality},{role:"user",content:a}]),i=JSON.parse(s.content),l={userId:t.userId,personalityTraits:i.personalityTraits,coreValues:i.coreValues,communicationStyle:i.communicationStyle,relationshipGoals:t.relationshipGoals,lifestyle:t.lifestyle,summary:i.summary};return console.log("✅ 人格洞察完成"),{userSoulProfile:l,step:"personality_insight_completed"}}catch(e){return console.error("❌ 人格洞察失败:",e),{error:`人格洞察失败: ${e instanceof Error?e.message:"未知错误"}`,step:"personality_insight_failed"}}}async function u(e){try{console.log("\uD83D\uDD0D 执行兼容性推理 Agent...");let{userSoulProfile:t,candidatePoolIds:r}=e;if(!t||!r)throw Error("缺少必要的状态数据");let a=[],s=c(o.compatibility),i=r.map(async e=>{let r=await m(e),a=n.compatibilityInference.replace("{userSoulProfile}",JSON.stringify(t)).replace("{candidateName}",r.name||"未知").replace("{candidateAge}",r.age?.toString()||"未知").replace("{candidateSelfDescription}",r.selfDescription||"").replace("{candidateInterests}",JSON.stringify(r.interests||[])).replace("{candidateValues}",JSON.stringify(r.values||[])).replace("{candidateLifestyle}",JSON.stringify(r.lifestyle||{})),i=await s.invoke([{role:"system",content:o.compatibility},{role:"user",content:a}]),l=JSON.parse(i.content);return{candidateId:e,compatibilityScore:l.compatibilityScore,reasoning:l.reasoning,highlights:l.highlights,challenges:l.challenges,personalitySummary:l.personalitySummary}}),l=await Promise.all(i);return a.push(...l),console.log(`✅ 兼容性推理完成，分析了 ${a.length} 个候选人`),{candidatesWithAnalysis:a,step:"compatibility_inference_completed"}}catch(e){return console.error("❌ 兼容性推理失败:",e),{error:`兼容性推理失败: ${e instanceof Error?e.message:"未知错误"}`,step:"compatibility_inference_failed"}}}async function m(e){let{db:t}=await Promise.resolve().then(r.bind(r,71682)),{users:a,userProfiles:s}=await Promise.resolve().then(r.bind(r,32767)),{eq:i}=await r.e(523).then(r.bind(r,65523)),n=await t.select({user:a,profile:s}).from(a).leftJoin(s,i(a.id,s.userId)).where(i(a.id,e)).limit(1);if(0===n.length)throw Error(`候选人 ${e} 不存在`);let o=n[0];return{userId:o.user.id,name:o.user.name||"未知",age:o.user.age||0,selfDescription:o.profile?.selfDescription||"",interests:o.user.interests||[],values:o.profile?.values||[],lifestyle:o.profile?.lifestyle||{}}}async function p(e){try{console.log("\uD83C\uDFC6 执行排序和最终决策 Agent...");let{candidatesWithAnalysis:t}=e;if(!t||0===t.length)throw Error("没有候选人分析数据");let r=t.sort((e,t)=>t.compatibilityScore-e.compatibilityScore).slice(0,5);return console.log(`✅ 排序完成，选出前 ${r.length} 名候选人`),{rankedCandidates:r,step:"ranking_completed"}}catch(e){return console.error("❌ 排序失败:",e),{error:`排序失败: ${e instanceof Error?e.message:"未知错误"}`,step:"ranking_failed"}}}async function f(e){try{console.log("\uD83D\uDCDD 生成完整报告...");let{rankedCandidates:t,userSoulProfile:r}=e;if(!t||!r)throw Error("缺少必要数据");let a=t[0],[s,i,n]=await Promise.all([h(r,a),g(r,a),y(r,a)]),o={topMatch:{candidate:a,relationshipInsight:s,conversationSimulation:i,datePlan:n},potentialMatches:t.slice(1,5).map(e=>({candidate:e,highlights:e.highlights,compatibilityReason:e.reasoning})),generatedAt:new Date,requestId:e.requesterId};return console.log("✅ 完整报告生成完成"),{finalMatrix:o,step:"report_generation_completed"}}catch(e){return console.error("❌ 报告生成失败:",e),{error:`报告生成失败: ${e instanceof Error?e.message:"未知错误"}`,step:"report_generation_failed"}}}async function h(e,t){let r=c(o.compatibility),a=n.relationshipHighlight.replace("{userSoulProfile}",JSON.stringify(e)).replace("{candidateAnalysis}",JSON.stringify(t));return JSON.parse((await r.invoke([{role:"system",content:o.compatibility},{role:"user",content:a}])).content)}async function g(e,t){let r=c(o.conversation),a=n.conversationSimulation.replace("{userProfile}",JSON.stringify(e)).replace("{candidateProfile}",JSON.stringify(t)).replace("{scenario}","咖啡厅初次见面");return JSON.parse((await r.invoke([{role:"system",content:o.conversation},{role:"user",content:a}])).content)}async function y(e,t){let r=c(o.dating),a=e.coreValues.filter(e=>t.highlights.some(t=>t.includes(e))),s=n.datePlanGeneration.replace("{userProfile}",JSON.stringify(e)).replace("{candidateProfile}",JSON.stringify(t)).replace("{commonInterests}",JSON.stringify(a));return JSON.parse((await r.invoke([{role:"system",content:o.dating},{role:"user",content:s}])).content)}async function w(e){try{console.log("\uD83D\uDD0D 检索候选人池...");let{requesterId:t}=e;if(!t)throw Error("缺少请求者ID");let r=await a.db.select({user:s.users,profile:s.userProfiles}).from(s.users).leftJoin(s.userProfiles,(0,i.eq)(s.users.id,s.userProfiles.userId)).where((0,i.eq)(s.users.id,t)).limit(1);if(0===r.length)throw Error("用户不存在");let n={userId:r[0].user.id,name:r[0].user.name,age:r[0].user.age,gender:r[0].user.gender,interests:r[0].user.interests,selfDescription:r[0].profile?.selfDescription,values:r[0].profile?.values,lifestyle:r[0].profile?.lifestyle,relationshipGoals:r[0].profile?.relationshipGoals},o=(await a.db.select({id:s.users.id}).from(s.users).leftJoin(s.userProfiles,(0,i.eq)(s.users.id,s.userProfiles.userId)).where((0,i.Uo)((0,i.ne)(s.users.id,t),(0,i.eq)(s.users.isActive,!0),"male"===n.gender?(0,i.eq)(s.users.gender,"female"):(0,i.eq)(s.users.gender,"male"))).limit(50)).map(e=>e.id);return console.log(`✅ 检索到 ${o.length} 个候选人`),{userProfile:n,candidatePoolIds:o,step:"candidates_retrieved"}}catch(e){return console.error("❌ 候选人检索失败:",e),{error:`候选人检索失败: ${e instanceof Error?e.message:"未知错误"}`,step:"candidates_retrieval_failed"}}}class q{constructor(){this.workflow={async invoke(e){let t={...e};try{let e=await w(t);if(e.error)throw Error(e.error);t={...t,...e};let r=await d(t);if(r.error)throw Error(r.error);t={...t,...r};let a=await u(t);if(a.error)throw Error(a.error);t={...t,...a};let s=await p(t);if(s.error)throw Error(s.error);t={...t,...s};let i=await f(t);if(i.error)throw Error(i.error);return t={...t,...i}}catch(e){return{...t,error:e instanceof Error?e.message:"工作流执行失败"}}},async *stream(e){let t={...e};try{yield{step:"retrieving_candidates",data:t,timestamp:new Date};let e=await w(t);if(e.error)throw Error(e.error);t={...t,...e},yield{step:"generating_soul_profile",data:t,timestamp:new Date};let r=await d(t);if(r.error)throw Error(r.error);t={...t,...r},yield{step:"running_compatibility_inference",data:t,timestamp:new Date};let a=await u(t);if(a.error)throw Error(a.error);t={...t,...a},yield{step:"ranking_candidates",data:t,timestamp:new Date};let s=await p(t);if(s.error)throw Error(s.error);t={...t,...s},yield{step:"generating_report",data:t,timestamp:new Date};let i=await f(t);if(i.error)throw Error(i.error);t={...t,...i},yield{step:"completed",data:t,timestamp:new Date}}catch(e){yield{step:"error",error:e instanceof Error?e.message:"工作流执行失败",timestamp:new Date}}}}}async generateCandidateMatrix(e){try{console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${e}`);let t=await this.workflow.invoke({requesterId:e,step:"initialized"});if(t.error)throw Error(t.error);if(!t.finalMatrix)throw Error("工作流未生成最终结果");return console.log("✅ ARAG-Soul 工作流执行完成"),t.finalMatrix}catch(e){throw console.error("❌ ARAG-Soul 工作流执行失败:",e),e}}async *generateCandidateMatrixStream(e){try{for await(let t of this.workflow.stream({requesterId:e,step:"initialized"}))yield{step:t.step,data:t,timestamp:new Date}}catch(e){yield{step:"error",error:e instanceof Error?e.message:"未知错误",timestamp:new Date}}}}let _=new q;class R{static async processQueuedRequests(){try{console.log("\uD83D\uDD04 开始处理匹配队列...");let e=await a.db.select().from(s.matchQueue).where((0,i.eq)(s.matchQueue.status,"pending")).limit(5);if(0===e.length)return void console.log("\uD83D\uDCED 队列为空，无待处理任务");console.log(`📋 发现 ${e.length} 个待处理任务`);let t=e.map(e=>this.processSingleRequest(e.matchRequestId,e.requesterId));await Promise.allSettled(t),console.log("✅ 队列处理完成")}catch(e){console.error("❌ 队列处理失败:",e)}}static async processSingleRequest(e,t){try{console.log(`🎯 开始处理请求: ${e}`),await a.db.update(s.matchQueue).set({status:"processing"}).where((0,i.eq)(s.matchQueue.matchRequestId,e)),await a.db.update(s.matchRequests).set({status:"processing"}).where((0,i.eq)(s.matchRequests.id,e));let r=await _.generateCandidateMatrix(t);await this.saveCandidateMatrix(e,r),await a.db.update(s.matchRequests).set({status:"completed",finalReport:r}).where((0,i.eq)(s.matchRequests.id,e)),await a.db.update(s.matchQueue).set({status:"completed"}).where((0,i.eq)(s.matchQueue.matchRequestId,e)),console.log(`✅ 请求处理完成: ${e}`)}catch(r){console.error(`❌ 请求处理失败: ${e}`,r);let t=r instanceof Error?r.message:"未知错误";await a.db.update(s.matchRequests).set({status:"failed",errorMessage:t}).where((0,i.eq)(s.matchRequests.id,e)),await a.db.update(s.matchQueue).set({status:"failed"}).where((0,i.eq)(s.matchQueue.matchRequestId,e))}}static async saveCandidateMatrix(e,t){try{let r={requestId:e,candidateId:t.topMatch.candidate.candidateId,rank:1,userDecision:"pending"};await a.db.insert(s.matchCandidates).values(r);let i=t.potentialMatches.map((t,r)=>({requestId:e,candidateId:t.candidate.candidateId,rank:r+2,userDecision:"pending"}));i.length>0&&await a.db.insert(s.matchCandidates).values(i),console.log(`💾 候选人矩阵已保存: ${e}`)}catch(e){throw console.error("保存候选人矩阵失败:",e),e}}static async updateCandidateDecision(e,t,r,n){try{let o=await a.db.select().from(s.matchRequests).where((0,i.eq)(s.matchRequests.id,e)).limit(1);if(0===o.length||o[0].requesterId!==r)throw Error("无权限操作此请求");await a.db.update(s.matchCandidates).set({userDecision:n}).where((0,i.Uo)((0,i.eq)(s.matchCandidates.requestId,e),(0,i.eq)(s.matchCandidates.candidateId,t)));let l=!1;return"liked"===n&&(l=await this.checkMutualMatch(r,t)),{success:!0,mutualMatch:l}}catch(e){throw console.error("更新候选人决策失败:",e),e}}static async checkMutualMatch(e,t){try{return(await a.db.select({request:s.matchRequests,candidate:s.matchCandidates}).from(s.matchRequests).innerJoin(s.matchCandidates,(0,i.eq)(s.matchRequests.id,s.matchCandidates.requestId)).where((0,i.Uo)((0,i.eq)(s.matchRequests.requesterId,t),(0,i.eq)(s.matchCandidates.candidateId,e),(0,i.eq)(s.matchCandidates.userDecision,"liked")))).length>0}catch(e){return console.error("检查互相匹配失败:",e),!1}}static async getUserMatchHistory(e,t=10){try{return await a.db.select({request:s.matchRequests,candidates:s.matchCandidates}).from(s.matchRequests).leftJoin(s.matchCandidates,(0,i.eq)(s.matchRequests.id,s.matchCandidates.requestId)).where((0,i.eq)(s.matchRequests.requesterId,e)).orderBy(s.matchRequests.createdAt).limit(t)}catch(e){throw console.error("获取匹配历史失败:",e),e}}static async getMatchingStats(e){try{let t=await a.db.select().from(s.matchRequests).where((0,i.eq)(s.matchRequests.requesterId,e)),r=await a.db.select().from(s.matchRequests).innerJoin(s.matchCandidates,(0,i.eq)(s.matchRequests.id,s.matchCandidates.requestId)).where((0,i.Uo)((0,i.eq)(s.matchRequests.requesterId,e),(0,i.eq)(s.matchCandidates.userDecision,"liked")));return{totalRequests:t.length,successfulMatches:r.length,mutualMatches:0,successRate:t.length>0?(r.length/t.length*100).toFixed(1):"0"}}catch(e){throw console.error("获取匹配统计失败:",e),e}}}},32767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{aiAgentFeedback:()=>p,conversations:()=>f,matchCandidates:()=>q,matchQueue:()=>y,matchRequests:()=>w,matches:()=>m,messages:()=>h,userProfiles:()=>u,userSessions:()=>g,users:()=>d});var a=r(92768),s=r(9594),i=r(29334),n=r(63431),o=r(34509),l=r(54693),c=r(9253);let d=(0,a.cJ)("users",{id:(0,s.uR)("id").primaryKey().defaultRandom(),email:(0,i.Qq)("email").notNull().unique(),name:(0,i.Qq)("name"),avatar:(0,i.Qq)("avatar"),bio:(0,i.Qq)("bio"),age:(0,n.nd)("age"),gender:(0,i.Qq)("gender"),location:(0,i.Qq)("location"),interests:(0,o.Fx)("interests").$type().default([]),personalityTraits:(0,o.Fx)("personality_traits").$type(),personalitySummary:(0,i.Qq)("personality_summary"),isActive:(0,l.zM)("is_active").default(!0),createdAt:(0,c.vE)("created_at").defaultNow(),updatedAt:(0,c.vE)("updated_at").defaultNow()}),u=(0,a.cJ)("user_profiles",{id:(0,s.uR)("id").primaryKey().defaultRandom(),userId:(0,s.uR)("user_id").references(()=>d.id).notNull(),selfDescription:(0,i.Qq)("self_description"),lookingFor:(0,i.Qq)("looking_for"),relationshipGoals:(0,i.Qq)("relationship_goals"),lifestyle:(0,o.Fx)("lifestyle").$type(),values:(0,o.Fx)("values").$type().default([]),photos:(0,o.Fx)("photos").$type().default([]),preferences:(0,o.Fx)("preferences").$type(),createdAt:(0,c.vE)("created_at").defaultNow(),updatedAt:(0,c.vE)("updated_at").defaultNow()}),m=(0,a.cJ)("matches",{id:(0,s.uR)("id").primaryKey().defaultRandom(),user1Id:(0,s.uR)("user1_id").references(()=>d.id).notNull(),user2Id:(0,s.uR)("user2_id").references(()=>d.id).notNull(),compatibilityScore:(0,n.nd)("compatibility_score"),aiAnalysis:(0,o.Fx)("ai_analysis").$type(),conversationSimulation:(0,o.Fx)("conversation_simulation").$type(),status:(0,i.Qq)("status").default("pending"),user1Liked:(0,l.zM)("user1_liked").default(!1),user2Liked:(0,l.zM)("user2_liked").default(!1),user1Viewed:(0,l.zM)("user1_viewed").default(!1),user2Viewed:(0,l.zM)("user2_viewed").default(!1),createdAt:(0,c.vE)("created_at").defaultNow(),updatedAt:(0,c.vE)("updated_at").defaultNow()}),p=(0,a.cJ)("ai_agent_feedback",{id:(0,s.uR)("id").primaryKey().defaultRandom(),matchId:(0,s.uR)("match_id").references(()=>m.id).notNull(),userId:(0,s.uR)("user_id").references(()=>d.id).notNull(),feedbackType:(0,i.Qq)("feedback_type").notNull(),feedbackText:(0,i.Qq)("feedback_text"),rating:(0,n.nd)("rating"),aspectRated:(0,i.Qq)("aspect_rated"),createdAt:(0,c.vE)("created_at").defaultNow()}),f=(0,a.cJ)("conversations",{id:(0,s.uR)("id").primaryKey().defaultRandom(),matchId:(0,s.uR)("match_id").references(()=>m.id).notNull(),isActive:(0,l.zM)("is_active").default(!0),createdAt:(0,c.vE)("created_at").defaultNow(),updatedAt:(0,c.vE)("updated_at").defaultNow()}),h=(0,a.cJ)("messages",{id:(0,s.uR)("id").primaryKey().defaultRandom(),conversationId:(0,s.uR)("conversation_id").references(()=>f.id).notNull(),senderId:(0,s.uR)("sender_id").references(()=>d.id).notNull(),content:(0,i.Qq)("content").notNull(),messageType:(0,i.Qq)("message_type").default("text"),isRead:(0,l.zM)("is_read").default(!1),createdAt:(0,c.vE)("created_at").defaultNow()}),g=(0,a.cJ)("user_sessions",{id:(0,s.uR)("id").primaryKey().defaultRandom(),userId:(0,s.uR)("user_id").references(()=>d.id).notNull(),sessionToken:(0,i.Qq)("session_token").notNull().unique(),expiresAt:(0,c.vE)("expires_at").notNull(),createdAt:(0,c.vE)("created_at").defaultNow()}),y=(0,a.cJ)("match_queue",{id:(0,s.uR)("id").primaryKey().defaultRandom(),matchRequestId:(0,s.uR)("match_request_id").notNull().unique(),requesterId:(0,s.uR)("requester_id").references(()=>d.id).notNull(),status:(0,i.Qq)("status").default("pending"),attempts:(0,n.nd)("attempts").default(0),createdAt:(0,c.vE)("created_at").defaultNow()}),w=(0,a.cJ)("match_requests",{id:(0,s.uR)("id").primaryKey().defaultRandom(),requesterId:(0,s.uR)("requester_id").references(()=>d.id).notNull(),status:(0,i.Qq)("status").default("processing"),finalReport:(0,o.Fx)("final_report"),errorMessage:(0,i.Qq)("error_message"),createdAt:(0,c.vE)("created_at").defaultNow()}),q=(0,a.cJ)("match_candidates",{id:(0,s.uR)("id").primaryKey().defaultRandom(),requestId:(0,s.uR)("request_id").references(()=>w.id).notNull(),candidateId:(0,s.uR)("candidate_id").references(()=>d.id).notNull(),rank:(0,n.nd)("rank").notNull(),userDecision:(0,i.Qq)("user_decision").default("pending"),createdAt:(0,c.vE)("created_at").defaultNow()})},71682:(e,t,r)=>{"use strict";let a;r.d(t,{db:()=>a});var s=r(37383),i=r(43971),n=r(32767);let o=process.env.DATABASE_URL;if(o){let e=(0,i.A)(o,{max:1,idle_timeout:20,connect_timeout:10,ssl:"require",prepare:!1});a=(0,s.f)(e,{schema:n})}else a=(0,s.f)({},{schema:n})},78335:()=>{},96487:()=>{}};