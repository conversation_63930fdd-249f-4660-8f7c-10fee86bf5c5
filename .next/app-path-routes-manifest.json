{"/api/auth/sync/route": "/api/auth/sync", "/api/feedback/route": "/api/feedback", "/api/health/route": "/api/health", "/api/matches/[id]/route": "/api/matches/[id]", "/api/matches/matrix/[requestId]/candidates/[candidateId]/route": "/api/matches/matrix/[requestId]/candidates/[candidateId]", "/api/matches/matrix/route": "/api/matches/matrix", "/api/matches/route": "/api/matches", "/api/profile/generate/route": "/api/profile/generate", "/api/profile/route": "/api/profile", "/api/worker/process-queue/route": "/api/worker/process-queue", "/auth/callback/route": "/auth/callback", "/_not-found/page": "/_not-found", "/auth/login/page": "/auth/login", "/auth/register/page": "/auth/register", "/match/[id]/page": "/match/[id]", "/matches/matrix/[requestId]/page": "/matches/matrix/[requestId]", "/dashboard/page": "/dashboard", "/page": "/", "/profile/page": "/profile"}