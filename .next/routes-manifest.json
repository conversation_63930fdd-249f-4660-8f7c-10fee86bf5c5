{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/matches/matrix/[requestId]/candidates/[candidateId]", "regex": "^/api/matches/matrix/([^/]+?)/candidates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrequestId": "nxtPrequestId", "nxtPcandidateId": "nxtPcandidateId"}, "namedRegex": "^/api/matches/matrix/(?<nxtPrequestId>[^/]+?)/candidates/(?<nxtPcandidateId>[^/]+?)(?:/)?$"}, {"page": "/api/matches/[id]", "regex": "^/api/matches/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/matches/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/match/[id]", "regex": "^/match/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/match/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/matches/matrix/[requestId]", "regex": "^/matches/matrix/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrequestId": "nxtPrequestId"}, "namedRegex": "^/matches/matrix/(?<nxtPrequestId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/register", "regex": "^/auth/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/register(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}