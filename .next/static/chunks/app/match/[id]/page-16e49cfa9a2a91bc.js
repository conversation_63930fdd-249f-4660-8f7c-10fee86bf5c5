(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[534],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155),l=a(2115),n=a(9708),r=a(2085),i=a(9434);let c=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,s)=>{let{className:a,variant:l,size:r,asChild:d=!1,...o}=e,m=d?n.DX:"button";return(0,t.jsx)(m,{className:(0,i.cn)(c({variant:l,size:r,className:a})),ref:s,...o})});d.displayName="Button"},5010:(e,s,a)=>{Promise.resolve().then(a.bind(a,6568))},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var t=a(5155);a(2115);var l=a(2085),n=a(9434);let r=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...l}=e;return(0,t.jsx)("div",{className:(0,n.cn)(r({variant:a}),s),...l})}},6568:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(5155),l=a(2115),n=a(5695),r=a(285),i=a(6695),c=a(6126),d=a(1644),o=a(4139),m=a(1975),x=a(8533),u=a(1525),h=a(3219),p=a(8632),j=a(6891),g=a(6874),f=a.n(g),v=a(792);function N(){var e,s,a,g,N,b,y,w,k,A,C,S,D,R,P,T;let E=(0,n.useParams)(),z=(0,n.useRouter)(),[F,$]=(0,l.useState)(null),[B,I]=(0,l.useState)(!0),[Z,U]=(0,l.useState)(0),[_,W]=(0,l.useState)(!1),[M,O]=(0,l.useState)(!1),[H,q]=(0,l.useState)(null),[J,V]=(0,l.useState)(!1),L=(0,l.useRef)(null),Q={id:"1",compatibilityScore:85,otherUser:{name:"小雨",age:26,location:"北京",bio:"喜欢阅读和旅行，寻找有趣的灵魂",interests:["阅读","旅行","摄影","咖啡","电影","音乐","瑜伽"],avatar:"\uD83C\uDF38"},aiAnalysis:{explanation:"你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补，你们可能会在艺术、文化和人生哲学方面有很多共同话题。建议从共同的兴趣话题开始交流，比如最近读过的书或者想去的旅行目的地。",strengths:["价值观高度一致","兴趣爱好互补","都热爱学习成长","沟通风格匹配"],challenges:["生活节奏可能不同","需要平衡独处与社交时间"],suggestions:["从共同兴趣开始聊天","分享彼此的读书心得","计划一次文化之旅"]},conversationSimulation:{conversation:[{speaker:"user1",message:"你好！看到你也喜欢旅行，最近去过哪里？"},{speaker:"user2",message:"你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？"},{speaker:"user1",message:"云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。"},{speaker:"user2",message:"哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。"},{speaker:"user1",message:"是的！我特别想去布达拉宫，还想尝试一下藏式瑜伽，听说在高原上练瑜伽是完全不同的体验。"},{speaker:"user2",message:"这个想法太棒了！我也练瑜伽，在大自然中练习确实会有不一样的感受。我们可以一起规划路线吗？"},{speaker:"user1",message:"当然可以！我已经收集了一些攻略，我们可以分享一下彼此的想法。"},{speaker:"user2",message:"太好了！我觉得我们会有很多共同话题，期待更深入的交流。"}],analysis:{conversationFlow:92,valueAlignment:88,communicationMatch:90,overallCompatibility:85,commonTopics:["旅行","瑜伽","文化探索","自然风光"],potentialConflicts:["时间安排差异","旅行预算考虑"]}},status:"pending"};(0,l.useEffect)(()=>{X()},[]);let X=async()=>{try{console.log("Loading match data for ID:",E.id),console.log("Full params object:",E);let e=await fetch("/api/matches/".concat(E.id));if(e.ok){let s=await e.json();$(s.match)}else console.error("Failed to load match data"),$(Q)}catch(e){console.error("Error loading match data:",e),$(Q)}finally{I(!1)}},G=async(e,s)=>{console.log("Feedback:",{type:e,rating:s||H}),V(!0)},K=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",Y=async()=>{if(!(null==F?void 0:F.id)){console.error("No match ID available"),alert("匹配ID不存在，请刷新页面重试");return}try{console.log("Sending like request for match:",F.id),console.log("Match object:",F);let e=await fetch("/api/matches/".concat(F.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!0})});console.log("Response status:",e.status);let s=await e.json();console.log("Response data:",s),e.ok?(console.log("Successfully liked match:",F.id),z.push("/dashboard")):(console.error("Failed to like match:",s),alert("操作失败，请重试"))}catch(e){console.error("Error liking match:",e),alert("网络错误，请重试")}},ee=async()=>{if(null==F?void 0:F.id)try{console.log("Sending pass request for match:",F.id);let e=await fetch("/api/matches/".concat(F.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!1})});console.log("Response status:",e.status);let s=await e.json();console.log("Response data:",s),e.ok?(console.log("Successfully passed match:",F.id),z.push("/dashboard")):(console.error("Failed to pass match:",s),alert("操作失败，请重试"))}catch(e){console.error("Error passing match:",e),alert("网络错误，请重试")}};if(B)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{children:"加载匹配详情..."})]})});if(!F)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"未找到匹配信息"}),(0,t.jsx)(f(),{href:"/dashboard",children:(0,t.jsx)(r.$,{children:"返回首页"})})]})});let es=(null==(e=F.conversationSimulation)?void 0:e.conversation)||[],ea=(null==(s=F.conversationSimulation)?void 0:s.analysis)||{},et=M?es:es.slice(0,Z+1);return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50",children:[(0,t.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)(f(),{href:"/dashboard",className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(d.A,{className:"w-5 h-5"}),"返回"]}),(0,t.jsx)("h1",{className:"text-lg font-semibold",children:"匹配详情"}),(0,t.jsx)("div",{className:"w-16"})," "]})})}),(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,t.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl",children:F.otherUser.avatar}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:F.otherUser.name}),(0,t.jsxs)("p",{className:"text-pink-100",children:[F.otherUser.age,"岁 \xb7 ",F.otherUser.location]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-3xl font-bold",children:[F.compatibilityScore,"%"]}),(0,t.jsx)("div",{className:"text-pink-100",children:(T=F.compatibilityScore)>=80?"高度匹配":T>=60?"中等匹配":"低度匹配"})]})]})}),(0,t.jsxs)(i.Wu,{className:"p-6",children:[(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:F.otherUser.bio}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"兴趣爱好"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:F.otherUser.interests.map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",className:"text-sm",children:e},s))})]}),"pending"===F.status&&(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(r.$,{variant:"outline",size:"lg",onClick:ee,className:"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600",children:[(0,t.jsx)(o.A,{className:"w-5 h-5"}),"跳过"]}),(0,t.jsxs)(r.$,{size:"lg",onClick:Y,className:"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700",children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),"喜欢"]})]}),"mutual_like"===F.status&&(0,t.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-red-50 border border-pink-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-pink-700",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 fill-current"}),(0,t.jsx)("span",{className:"font-medium",children:"你们互相喜欢！"})]}),(0,t.jsx)("p",{className:"text-center text-sm text-pink-600 mt-2",children:"可以开始联系对方了 \uD83D\uDC95"})]}),"passed"===F.status&&(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-gray-600",children:[(0,t.jsx)(o.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"font-medium",children:"已跳过此匹配"})]})}),"liked"===F.status&&(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-blue-700",children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"font-medium",children:"你已表示喜欢"})]}),(0,t.jsx)("p",{className:"text-center text-sm text-blue-600 mt-2",children:"等待对方回应..."})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD16"}),"红娘深度分析"]}),(0,t.jsx)(i.BT,{children:"基于双方资料的智能匹配分析"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"匹配解析"}),(0,t.jsx)("div",{className:"prose prose-sm max-w-none text-gray-700",children:(0,t.jsx)(v.oz,{components:{h1:e=>{let{children:s}=e;return(0,t.jsx)("h1",{className:"text-lg font-bold mb-2",children:s})},h2:e=>{let{children:s}=e;return(0,t.jsx)("h2",{className:"text-base font-semibold mb-2",children:s})},h3:e=>{let{children:s}=e;return(0,t.jsx)("h3",{className:"text-sm font-medium mb-1",children:s})},p:e=>{let{children:s}=e;return(0,t.jsx)("p",{className:"mb-3 leading-relaxed",children:s})},ul:e=>{let{children:s}=e;return(0,t.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1",children:s})},ol:e=>{let{children:s}=e;return(0,t.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1",children:s})},li:e=>{let{children:s}=e;return(0,t.jsx)("li",{className:"text-sm",children:s})},strong:e=>{let{children:s}=e;return(0,t.jsx)("strong",{className:"font-semibold text-gray-800",children:s})},em:e=>{let{children:s}=e;return(0,t.jsx)("em",{className:"italic text-gray-600",children:s})}},children:(null==(a=F.aiAnalysis)?void 0:a.explanation)||"暂无详细分析"})})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3 text-green-600",children:"匹配优势"}),(0,t.jsx)("ul",{className:"space-y-2",children:(null==(N=F.aiAnalysis)||null==(g=N.strengths)?void 0:g.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),e]},s)))||(0,t.jsx)("li",{className:"text-sm text-gray-500",children:"暂无数据"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3 text-yellow-600",children:"注意事项"}),(0,t.jsx)("ul",{className:"space-y-2",children:(null==(y=F.aiAnalysis)||null==(b=y.challenges)?void 0:b.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),e]},s)))||(0,t.jsx)("li",{className:"text-sm text-gray-500",children:"暂无数据"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3 text-blue-600",children:"建议话题"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:(null==(k=F.aiAnalysis)||null==(w=k.suggestions)?void 0:w.map((e,s)=>(0,t.jsx)(c.E,{variant:"outline",className:"text-blue-600 border-blue-200",children:e},s)))||(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"暂无建议话题"})})]})]})]}),(null==(A=F.aiAnalysis)?void 0:A.datePlan)&&(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDC95"}),"专属约会计划"]}),(0,t.jsx)(i.BT,{children:"AI 为你们量身定制的约会建议"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2 text-pink-600",children:"约会主题"}),(0,t.jsx)("p",{className:"text-gray-700",children:F.aiAnalysis.datePlan.theme}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:F.aiAnalysis.datePlan.concept})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-3 text-blue-600",children:"约会安排"}),(0,t.jsx)("div",{className:"space-y-3",children:null==(C=F.aiAnalysis.datePlan.timeline)?void 0:C.map((e,s)=>(0,t.jsxs)("div",{className:"flex gap-4 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-blue-600 min-w-[80px]",children:e.time}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.activity}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.location}),(0,t.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:e.reason})]})]},s))})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2 text-green-600",children:"推荐地点"}),(0,t.jsx)("ul",{className:"space-y-1",children:null==(D=F.aiAnalysis.datePlan.recommendations)||null==(S=D.locations)?void 0:S.map((e,s)=>(0,t.jsxs)("li",{className:"text-sm text-gray-700 flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),e]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2 text-purple-600",children:"贴心提醒"}),(0,t.jsx)("ul",{className:"space-y-1",children:null==(P=F.aiAnalysis.datePlan.recommendations)||null==(R=P.tips)?void 0:R.map((e,s)=>(0,t.jsxs)("li",{className:"text-sm text-gray-700 flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-1.5 h-1.5 bg-purple-500 rounded-full"}),e]},s))})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"预算建议："}),F.aiAnalysis.datePlan.budget]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"约会时长："}),F.aiAnalysis.datePlan.duration]})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCAC"}),"对话模拟"]}),(0,t.jsx)(i.BT,{children:"预测你们第一次聊天的场景"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[!_&&0===Z&&(0,t.jsxs)(r.$,{onClick:()=>{var e;(null==F||null==(e=F.conversationSimulation)?void 0:e.conversation)&&(W(!0),U(0),O(!1),L.current=setInterval(()=>{U(e=>{let s=e+1;return s>=F.conversationSimulation.conversation.length?(W(!1),O(!0),L.current&&clearInterval(L.current),e):s})},2e3))},variant:"outline",size:"sm",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"播放对话"]}),_&&(0,t.jsxs)(r.$,{onClick:()=>{W(!1),L.current&&clearInterval(L.current)},variant:"outline",size:"sm",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"暂停"]}),Z>0&&!M&&(0,t.jsx)(r.$,{onClick:()=>{var e,s;W(!1),O(!0),L.current&&clearInterval(L.current),U((null==F||null==(s=F.conversationSimulation)||null==(e=s.conversation)?void 0:e.length)||0)},variant:"outline",size:"sm",children:"显示全部"})]})]})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto",children:[et.map((e,s)=>{var a;let l=e.speaker===(null==(a=F.otherUser)?void 0:a.name);return(0,t.jsx)("div",{className:"flex ".concat(l?"justify-start":"justify-end"," ").concat(s===Z&&_?"animate-pulse":""),children:(0,t.jsxs)("div",{className:"max-w-[70%] p-3 rounded-lg transition-all duration-500 ".concat(l?"bg-white border shadow-sm text-gray-800":"bg-gradient-to-r from-blue-500 to-blue-600 text-white"," ").concat(s<=Z?"opacity-100 transform translate-y-0":"opacity-0 transform translate-y-4"),children:[(0,t.jsx)("div",{className:"text-xs mb-1 ".concat(l?"text-gray-500":"text-blue-100"),children:e.speaker}),(0,t.jsx)("div",{className:"text-sm leading-relaxed",children:e.message})]})},s)}),_&&Z<es.length&&(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),M&&(0,t.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,t.jsx)("h4",{className:"font-medium",children:"对话分析"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"对话流畅度"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(K(ea.conversationFlow||0)),children:[ea.conversationFlow||0,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"价值观契合"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(K(ea.valueAlignment||0)),children:[ea.valueAlignment||0,"%"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"沟通匹配度"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(K(ea.communicationMatch||0)),children:[ea.communicationMatch||0,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"整体兼容性"}),(0,t.jsxs)("span",{className:"text-sm font-medium ".concat(K(ea.overallCompatibility||0)),children:[ea.overallCompatibility||0,"%"]})]})]})]}),(null==ea?void 0:ea.commonTopics)&&ea.commonTopics.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"text-sm font-medium mb-2",children:"共同话题"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:ea.commonTopics.map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},s))})]})]}),M&&(0,t.jsxs)("div",{className:"mt-6 pt-6 border-t",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"这个对话模拟准确吗？"}),J?(0,t.jsx)("div",{className:"text-green-600 text-sm bg-green-50 p-3 rounded-md",children:"✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。"}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"准确度评分："}),(0,t.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(e=>(0,t.jsx)("button",{onClick:()=>q(e),className:"p-1 transition-colors ".concat(H&&H>=e?"text-yellow-500":"text-gray-300 hover:text-yellow-400"),children:(0,t.jsx)(h.A,{className:"w-4 h-4 fill-current"})},e))})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>G("accurate"),className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),"很准确"]}),(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>G("inaccurate"),className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),"不太准确"]})]})]})]})]})]})]})]})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>r,aR:()=>i});var t=a(5155),l=a(2115),n=a(9434);let r=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});r.displayName="Card";let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...l})});i.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...l})});o.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),l=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[514,874,496,441,684,358],()=>s(5010)),_N_E=e.O()}]);