(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[865],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155),i=a(2115),l=a(9708),r=a(2085),n=a(9434);let c=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,s)=>{let{className:a,variant:i,size:r,asChild:d=!1,...o}=e,m=d?l.DX:"button";return(0,t.jsx)(m,{className:(0,n.cn)(c({variant:i,size:r,className:a})),ref:s,...o})});d.displayName="Button"},1162:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>T});var t=a(5155),i=a(2115),l=a(5695),r=a(6695),n=a(285),c=a(6126),d=a(888),o=a(9434);let m=d.bL,x=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(d.B8,{ref:s,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i})});x.displayName=d.B8.displayName;let h=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(d.l9,{ref:s,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i})});h.displayName=d.l9.displayName;let u=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(d.UC,{ref:s,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i})});u.displayName=d.UC.displayName;var f=a(3470),p=a(4139);let g=f.bL,j=f.l9,N=f.ZL;f.bm;let v=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(f.hJ,{ref:s,className:(0,o.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...i})});v.displayName=f.hJ.displayName;let y=i.forwardRef((e,s)=>{let{className:a,children:i,...l}=e;return(0,t.jsxs)(N,{children:[(0,t.jsx)(v,{}),(0,t.jsxs)(f.UC,{ref:s,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[i,(0,t.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});y.displayName=f.UC.displayName;let b=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};b.displayName="DialogHeader";let w=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(f.hE,{ref:s,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...i})});w.displayName=f.hE.displayName,i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(f.VY,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",a),...i})}).displayName=f.VY.displayName;var k=a(3219),C=a(1975),R=a(9489),E=a(9169),I=a(6835);function S(e){let{matrix:s,onCandidateDecision:a}=e,[l,r]=(0,i.useState)({}),[n,c]=(0,i.useState)({}),d=async(e,s)=>{c(s=>({...s,[e]:!0}));try{await a(e,s),r(a=>({...a,[e]:s}))}catch(e){console.error("决策失败:",e)}finally{c(s=>({...s,[e]:!1}))}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"您的专属推荐"}),(0,t.jsx)("p",{className:"text-gray-600",children:"AI红娘为您精心挑选的5位候选人"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["生成时间: ",new Date(s.generatedAt).toLocaleString("zh-CN")]})]}),(0,t.jsx)(A,{topMatch:s.topMatch,decision:l[s.topMatch.candidate.candidateId],loading:n[s.topMatch.candidate.candidateId],onDecision:d}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"w-5 h-5 text-yellow-500"}),"其他潜力候选人"]}),(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:s.potentialMatches.map((e,s)=>(0,t.jsx)(D,{match:e,rank:s+2,decision:l[e.candidate.candidateId],loading:n[e.candidate.candidateId],onDecision:d},e.candidate.candidateId))})]})]})}function A(e){let{topMatch:s,decision:a,loading:l,onDecision:d}=e,[o,m]=(0,i.useState)(!1),{candidate:x,relationshipInsight:h,conversationSimulation:u,datePlan:f}=s;return(0,t.jsxs)(r.Zp,{className:"border-2 border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50",children:[(0,t.jsxs)(r.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-2",children:(0,t.jsxs)(c.E,{className:"bg-pink-500 text-white px-3 py-1",children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-1"}),"首席推荐"]})}),(0,t.jsx)(r.ZB,{className:"text-2xl",children:x.personalitySummary}),(0,t.jsxs)("div",{className:"flex justify-center items-center gap-2",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-pink-600",children:x.compatibilityScore}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"匹配度"})]})]}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"关系亮点"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:x.highlights.map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},s))})]}),(0,t.jsxs)(g,{open:o,onOpenChange:m,children:[(0,t.jsx)(j,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"outline",className:"w-full",children:"查看完整分析报告"})}),(0,t.jsxs)(y,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsx)(b,{children:(0,t.jsx)(w,{children:"首席推荐 - 完整分析报告"})}),(0,t.jsx)(M,{candidate:x,relationshipInsight:h,conversationSimulation:u,datePlan:f})]})]}),(0,t.jsx)(z,{candidateId:x.candidateId,decision:a,loading:l,onDecision:d})]})]})}function D(e){let{match:s,rank:a,decision:i,loading:l,onDecision:n}=e,{candidate:d}=s;return(0,t.jsxs)(r.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(c.E,{variant:"outline",className:"mb-2",children:["#",a]}),(0,t.jsx)(r.ZB,{className:"text-lg",children:d.personalitySummary})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-xl font-bold text-blue-600",children:d.compatibilityScore}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"匹配度"})]})]})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h5",{className:"text-sm font-medium text-gray-700",children:"亮点"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:d.highlights.slice(0,3).map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},s))})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[s.compatibilityReason.slice(0,100),"..."]}),(0,t.jsx)(z,{candidateId:d.candidateId,decision:i,loading:l,onDecision:n})]})]})}function z(e){let{candidateId:s,decision:a,loading:i,onDecision:l}=e;return a?(0,t.jsx)("div",{className:"text-center py-2",children:(0,t.jsx)(c.E,{variant:"liked"===a?"default":"secondary",children:"liked"===a?"已喜欢 ❤️":"已跳过 ⏭️"})}):(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1 border-red-200 hover:bg-red-50",onClick:()=>l(s,"skipped"),disabled:i,children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-1"}),"跳过"]}),(0,t.jsxs)(n.$,{size:"sm",className:"flex-1 bg-pink-500 hover:bg-pink-600",onClick:()=>l(s,"liked"),disabled:i,children:[(0,t.jsx)(C.A,{className:"w-4 h-4 mr-1"}),"喜欢"]})]})}function M(e){let{candidate:s,relationshipInsight:a,conversationSimulation:i,datePlan:l}=e;return(0,t.jsxs)(m,{defaultValue:"analysis",className:"w-full",children:[(0,t.jsxs)(x,{className:"grid w-full grid-cols-4",children:[(0,t.jsx)(h,{value:"analysis",children:"关系分析"}),(0,t.jsx)(h,{value:"conversation",children:"模拟对话"}),(0,t.jsx)(h,{value:"date",children:"约会计划"}),(0,t.jsx)(h,{value:"profile",children:"详细资料"})]}),(0,t.jsxs)(u,{value:"analysis",className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"关系优势"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:a.strengths.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"潜在挑战"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:a.challenges.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"相处建议"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:a.suggestions.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]})]}),(0,t.jsx)(u,{value:"conversation",className:"space-y-4",children:(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"w-4 h-4"}),i.scenario]}),(0,t.jsx)("div",{className:"space-y-3",children:i.messages.map((e,s)=>(0,t.jsxs)("div",{className:"p-3 rounded-lg ".concat("user"===e.speaker?"bg-blue-100 ml-8":"bg-white mr-8"),children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:["user"===e.speaker?"你":"对方",e.emotion&&" (".concat(e.emotion,")")]}),(0,t.jsx)("div",{className:"text-sm",children:e.content})]},s))}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-yellow-50 rounded-lg",children:[(0,t.jsx)("h5",{className:"text-sm font-medium mb-1",children:"对话分析"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:i.analysis})]})]})}),(0,t.jsx)(u,{value:"date",className:"space-y-4",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"w-4 h-4"}),l.title]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:l.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h5",{className:"font-medium mb-1 flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"w-3 h-3"}),"地点"]}),(0,t.jsx)("p",{children:l.location})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"时长"}),(0,t.jsx)("p",{children:l.duration})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"预算"}),(0,t.jsx)("p",{children:l.budget})]})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"活动安排"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:l.activities.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]}),(0,t.jsxs)("div",{className:"mt-3 p-3 bg-white rounded-lg",children:[(0,t.jsx)("h5",{className:"text-sm font-medium mb-1",children:"推荐理由"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:l.reasoning})]})]})}),(0,t.jsx)(u,{value:"profile",className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"兼容性分数"}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[s.compatibilityScore,"/100"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"人格摘要"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:s.personalitySummary})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"匹配推理"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:s.reasoning})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"关系亮点"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.highlights.map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",children:e},s))})]}),s.challenges&&s.challenges.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium mb-1",children:"潜在挑战"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:s.challenges.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]})]})})]})}var Z=a(1644),$=a(5847),B=a(7235);function T(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),{toast:a}=function(){let[e,s]=(0,i.useState)([]);return{toast:(0,i.useCallback)(e=>{console.log("Toast: ".concat(e.title),e.description),s(s=>[...s,e]),setTimeout(()=>{s(e=>e.slice(1))},3e3)},[]),toasts:e}}(),r=e.requestId,[c,d]=(0,i.useState)(null),[o,m]=(0,i.useState)("loading"),[x,h]=(0,i.useState)(""),[u,f]=(0,i.useState)(0);(0,i.useEffect)(()=>{if(!r)return;let e=async()=>{try{let e=await fetch("/api/matches/matrix?requestId=".concat(r)),s=await e.json();m(s.status),h(s.message||""),"completed"===s.status&&s.matrix?d(s.matrix):"failed"===s.status&&a({title:"生成失败",description:s.message||"匹配矩阵生成失败，请重试",variant:"destructive"})}catch(e){console.error("获取结果失败:",e),m("failed"),h("网络错误，请检查连接")}};e();let s=setInterval(()=>{("processing"===o||"loading"===o)&&(e(),f(e=>e+1))},3e3);return u>40&&(clearInterval(s),m("failed"),h("生成超时，请重试")),()=>clearInterval(s)},[r,o,u,a]);let p=async(e,s)=>{try{let t=await fetch("/api/matches/matrix/".concat(r,"/candidates/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({decision:s})}),i=await t.json();if(i.success)a({title:"liked"===s?"已喜欢":"已跳过",description:i.message,variant:(i.mutualMatch,"default")}),i.mutualMatch&&a({title:"\uD83C\uDF89 恭喜！",description:"你们互相喜欢了！可以开始聊天了。",variant:"default"});else throw Error(i.message||"操作失败")}catch(e){console.error("决策失败:",e),a({title:"操作失败",description:e instanceof Error?e.message:"请重试",variant:"destructive"})}},g=async()=>{try{let e=await fetch("/api/matches/matrix",{method:"POST"}),a=await e.json();if(a.success)s.push("/matches/matrix/".concat(a.requestId));else throw Error(a.message||"重新生成失败")}catch(e){console.error("重新生成失败:",e),a({title:"重新生成失败",description:e instanceof Error?e.message:"请重试",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)(n.$,{variant:"ghost",onClick:()=>s.push("/dashboard"),className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"w-4 h-4"}),"返回主页"]}),"completed"===o&&(0,t.jsxs)(n.$,{variant:"outline",onClick:g,className:"flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"w-4 h-4"}),"重新生成"]})]}),"loading"===o||"processing"===o?(0,t.jsx)(P,{message:x,pollCount:u}):"failed"===o?(0,t.jsx)(_,{message:x,onRetry:g}):c?(0,t.jsx)(S,{matrix:c,onCandidateDecision:p}):(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-gray-500",children:"没有找到匹配数据"})})]})}function P(e){let{message:s,pollCount:a}=e;return(0,t.jsx)(r.Zp,{className:"max-w-md mx-auto",children:(0,t.jsxs)(r.Wu,{className:"text-center py-12",children:[(0,t.jsx)(B.A,{className:"w-12 h-12 animate-spin mx-auto mb-4 text-pink-500"}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"AI红娘正在工作中..."}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:s||"正在分析您的资料并寻找最佳匹配"}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["预计还需要 ",Math.max(0,120-3*a)," 秒"]}),(0,t.jsx)("div",{className:"mt-4 w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-pink-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(100,3*a/120*100),"%")}})})]})})}function _(e){let{message:s,onRetry:a}=e;return(0,t.jsx)(r.Zp,{className:"max-w-md mx-auto",children:(0,t.jsxs)(r.Wu,{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-red-500 text-6xl mb-4",children:"\uD83D\uDE14"}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"生成失败"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:s||"匹配矩阵生成失败，请重试"}),(0,t.jsx)(n.$,{onClick:a,className:"w-full",children:"重新生成"})]})})}},5619:(e,s,a)=>{Promise.resolve().then(a.bind(a,1162))},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>n});var t=a(5155);a(2115);var i=a(2085),l=a(9434);let r=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:a,...i}=e;return(0,t.jsx)("div",{className:(0,l.cn)(r({variant:a}),s),...i})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>r,aR:()=>n});var t=a(5155),i=a(2115),l=a(9434);let r=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i})});r.displayName="Card";let n=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...i})});n.displayName="CardHeader";let c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...i})});c.displayName="CardTitle";let d=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",a),...i})});d.displayName="CardDescription";let o=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",a),...i})});o.displayName="CardContent",i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",a),...i})}).displayName="CardFooter"},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>l});var t=a(2596),i=a(9688);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,i.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[514,672,441,684,358],()=>s(5619)),_N_E=e.O()}]);