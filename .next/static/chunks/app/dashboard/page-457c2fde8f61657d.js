(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(5155),a=s(2115),l=s(9708),n=s(2085),i=s(9434);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:c=!1,...o}=e,m=c?l.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(d({variant:a,size:n,className:s})),ref:t,...o})});c.displayName="Button"},1975:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(7140).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2643:(e,t,s)=>{"use strict";s.d(t,{U:()=>a});var r=s(3579);let a=()=>(0,r.createClientComponentClient)()},4139:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(7140).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4266:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(7140).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},5724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(5155),a=s(2115),l=s(2643),n=s(6695),i=s(285),d=s(6126),c=s(4266),o=s(4139),m=s(1975),x=s(6874),h=s.n(x);function u(e){var t,s;let{match:l,onLike:x,onPass:u}=e,[g,f]=(0,a.useState)(!1),p=l.otherUser||l.user2,b=l.compatibilityScore||0;return(0,r.jsxs)(n.Zp,{className:"w-full hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50",children:[(0,r.jsxs)(n.aR,{className:"pb-4 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-pink-100 to-blue-100 rounded-full -translate-y-12 translate-x-12 md:-translate-y-16 md:translate-x-16 opacity-50"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br from-pink-500 to-blue-500 rounded-full flex items-center justify-center text-white text-lg md:text-xl font-bold shadow-lg",children:null==p||null==(t=p.name)?void 0:t.charAt(0)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(n.ZB,{className:"text-lg md:text-xl font-bold text-gray-800",children:null==p?void 0:p.name}),(0,r.jsxs)(n.BT,{className:"text-gray-600 font-medium text-sm md:text-base",children:[null==p?void 0:p.age,"岁 \xb7 ",null==p?void 0:p.location]})]})]}),(0,r.jsxs)("div",{className:"text-left sm:text-right self-start sm:self-auto",children:[(0,r.jsxs)("div",{className:"text-2xl md:text-3xl font-bold ".concat(b>=80?"text-green-600":b>=60?"text-yellow-600":"text-red-600"," drop-shadow-sm"),children:[b,"%"]}),(0,r.jsx)("div",{className:"text-xs md:text-sm text-gray-500 font-medium",children:b>=80?"高度匹配":b>=60?"中等匹配":"低度匹配"})]})]})]}),(0,r.jsxs)(n.Wu,{className:"space-y-5 relative",children:[(null==p?void 0:p.bio)&&(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border-l-4 border-gradient-to-b from-pink-400 to-blue-400",children:(0,r.jsxs)("p",{className:"text-gray-700 leading-relaxed italic",children:['"',p.bio,'"']})}),(null==p?void 0:p.interests)&&p.interests.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold mb-3 text-gray-800 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg",children:"\uD83C\uDFAF"}),"兴趣爱好"]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[p.interests.slice(0,5).map((e,t)=>(0,r.jsx)(d.E,{variant:"secondary",className:"text-xs bg-gradient-to-r from-pink-100 to-blue-100 text-gray-700 border-0 hover:from-pink-200 hover:to-blue-200 transition-colors",children:e},t)),p.interests.length>5&&(0,r.jsxs)(d.E,{variant:"outline",className:"text-xs border-gray-300 text-gray-600",children:["+",p.interests.length-5]})]})]}),(null==(s=l.aiAnalysis)?void 0:s.explanation)&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("span",{className:"text-lg",children:"\uD83E\uDD16"}),(0,r.jsx)("h4",{className:"font-semibold text-gray-800",children:"AI 匹配分析"})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>f(!g),className:"w-full text-left justify-start p-0 h-auto font-normal text-gray-600 hover:text-gray-800",children:g?"收起分析":"展开查看详细分析"}),g&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-white/70 rounded-md text-sm text-gray-700 leading-relaxed",children:l.aiAnalysis.explanation})]}),(0,r.jsx)(h(),{href:"/match/".concat(l.id),className:"block",children:(0,r.jsxs)(i.$,{variant:"outline",size:"lg",className:"w-full flex items-center gap-2 bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 border-purple-200 hover:border-purple-300 transition-all duration-300 h-10 md:h-12 text-sm md:text-base",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 md:w-5 md:h-5 text-purple-600"}),(0,r.jsx)("span",{className:"text-purple-700 font-medium",children:"查看完整AI分析"})]})}),(0,r.jsxs)("div",{className:"flex gap-2 md:gap-3 pt-2",children:[(0,r.jsxs)(i.$,{variant:"outline",size:"lg",onClick:()=>u(l.id),className:"flex-1 flex items-center justify-center gap-1 md:gap-2 text-gray-600 hover:text-red-600 hover:border-red-300 hover:bg-red-50 transition-all duration-300 h-10 md:h-12 text-sm md:text-base",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 md:w-5 md:h-5"}),"跳过"]}),(0,r.jsxs)(i.$,{size:"lg",onClick:()=>x(l.id),className:"flex-1 flex items-center justify-center gap-1 md:gap-2 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 shadow-lg hover:shadow-xl transition-all duration-300 h-10 md:h-12 text-sm md:text-base",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 md:w-5 md:h-5"}),"喜欢"]})]})]})]})}var g=s(7140);let f=(0,g.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),p=(0,g.A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var b=s(5695);let j=(0,g.A)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),v=(0,g.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function N(e){let{isOpen:t,onClose:s,hoursUntilReset:a,nextResetTime:l}=e;return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)(n.Zp,{className:"w-full max-w-md mx-auto bg-white shadow-2xl",children:[(0,r.jsx)(n.aR,{className:"text-center pb-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(j,{className:"w-8 h-8 text-orange-600"})}),(0,r.jsx)(n.ZB,{className:"text-xl font-bold text-gray-900 mb-2",children:"今日匹配额度已用完"}),(0,r.jsx)(n.BT,{className:"text-gray-600",children:"为了控制AI分析成本，每24小时限制3次匹配"})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(o.A,{className:"w-4 h-4"})})]})}),(0,r.jsxs)(n.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-orange-600"}),(0,r.jsx)("span",{className:"font-medium text-gray-800",children:"匹配额度"})]}),(0,r.jsx)(d.E,{variant:"secondary",className:"bg-orange-100 text-orange-800",children:"3 / 3 已用完"})]}),(0,r.jsx)("div",{className:"w-full bg-orange-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full w-full"})})]}),(0,r.jsx)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(v,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-800",children:"额度重置时间"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["大约 ",(0,r.jsxs)("span",{className:"font-medium text-blue-600",children:[a," 小时"]})," 后"]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:new Date(l).toLocaleString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"为什么有限制？"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• AI深度分析需要消耗大量计算资源"}),(0,r.jsx)("li",{children:"• 限制使用可以控制运营成本"}),(0,r.jsx)("li",{children:"• 确保每次匹配都是高质量的分析"}),(0,r.jsx)("li",{children:"• 未来会推出付费额度功能"})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(i.$,{variant:"outline",className:"flex-1",onClick:s,children:"我知道了"}),(0,r.jsx)(i.$,{className:"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",onClick:s,children:"查看现有匹配"})]})]})]})}):null}function y(){let[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)([]),[o,x]=(0,a.useState)(!0),[g,j]=(0,a.useState)(!1),[v,y]=(0,a.useState)(0),[w,k]=(0,a.useState)("discover"),[A,C]=(0,a.useState)(!1),[D,E]=(0,a.useState)(null),I=(0,b.useRouter)(),z=(0,l.U)();(0,a.useEffect)(()=>{R()},[]);let R=async()=>{let{data:{user:e}}=await z.auth.getUser();if(!e)return void I.push("/auth/login");await S()},S=async()=>{x(!0);try{let e=await fetch("/api/matches");if(e.ok){let s=(await e.json()).matches||[],r=s.filter(e=>"pending"===e.status),a=s.filter(e=>"mutual_like"===e.status);t(r),c(a)}else console.error("Failed to load matches"),t([]),c([])}catch(e){console.error("Error loading matches:",e),t([]),c([])}finally{x(!1)}},$=async()=>{j(!0),y(0);try{let e=setInterval(()=>{y(t=>t>=90?(clearInterval(e),90):t+10*Math.random())},300),t=await fetch("/api/matches/matrix",{method:"POST"});if(clearInterval(e),y(100),t.ok){let e=await t.json();e.success&&e.requestId?setTimeout(()=>{I.push("/matches/matrix/".concat(e.requestId))},500):(alert("生成失败，请重试"),j(!1),y(0))}else{let e=await t.json();if("DAILY_LIMIT_EXCEEDED"===e.error){let t=e.limitInfo;E(t),C(!0)}else alert(e.message||"生成候选人矩阵失败，请重试");j(!1),y(0)}}catch(e){console.error("Error generating candidate matrix:",e),alert("网络错误，请重试"),j(!1),y(0)}},T=async()=>{j(!0),y(0);try{let e=setInterval(()=>{y(t=>t>=90?(clearInterval(e),90):t+15*Math.random())},200),t=await fetch("/api/matches?generate_daily=true");if(clearInterval(e),y(100),t.ok){let e=await t.json();if(e.matches&&e.matches.length>0){let t=e.matches[e.matches.length-1];setTimeout(()=>{I.push("/match/".concat(t.id))},500)}else alert("暂时没有新的匹配对象，请稍后再试"),j(!1),y(0)}else{let e=await t.json();if("DAILY_LIMIT_EXCEEDED"===e.error){let t=e.limitInfo;E(t),C(!0)}else alert(e.message||"生成匹配失败，请重试");j(!1),y(0)}}catch(e){console.error("Error generating new match:",e),alert("网络错误，请重试"),j(!1),y(0)}},Z=async e=>{try{console.log("Sending like request for match:",e);let t=await fetch("/api/matches/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!0})});console.log("Response status:",t.status);let s=await t.json();console.log("Response data:",s),t.ok?(console.log("Successfully liked match:",e),await S(),alert("已发送喜欢！如果对方也喜欢你，你们就能开始聊天了。")):(console.error("Failed to like match:",s),alert("操作失败，请重试"))}catch(e){console.error("Error liking match:",e),alert("网络错误，请重试")}},B=async e=>{try{console.log("Sending pass request for match:",e);let t=await fetch("/api/matches/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({liked:!1})});console.log("Response status:",t.status);let s=await t.json();console.log("Response data:",s),t.ok?(console.log("Successfully passed match:",e),await S()):(console.error("Failed to pass match:",s),alert("操作失败，请重试"))}catch(e){console.error("Error passing match:",e),alert("网络错误，请重试")}},F=async()=>{await z.auth.signOut(),I.push("/auth/login")};return o?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{children:"加载中..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"hidden md:flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent",children:"✨ 寡佬AI"}),(0,r.jsxs)("nav",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:()=>k("discover"),className:"px-3 py-2 rounded-md text-sm font-medium ".concat("discover"===w?"bg-blue-100 text-blue-700":"text-gray-500 hover:text-gray-700"),children:"发现"}),(0,r.jsxs)("button",{onClick:()=>k("mutual"),className:"px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 ".concat("mutual"===w?"bg-blue-100 text-blue-700":"text-gray-500 hover:text-gray-700"),children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),"互相喜欢",s.length>0&&(0,r.jsx)(d.E,{variant:"secondary",className:"text-xs",children:s.length})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(h(),{href:"/profile",children:(0,r.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(f,{className:"w-4 h-4 mr-2"}),"设置"]})}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:F,children:"退出"})]})]}),(0,r.jsxs)("div",{className:"md:hidden",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent",children:"✨ 寡佬AI"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h(),{href:"/profile",children:(0,r.jsx)(i.$,{variant:"outline",size:"sm",children:(0,r.jsx)(f,{className:"w-4 h-4"})})}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:F,children:"退出"})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-100 -mx-4 px-4",children:(0,r.jsxs)("nav",{className:"flex",children:[(0,r.jsx)("button",{onClick:()=>k("discover"),className:"flex-1 px-3 py-3 text-sm font-medium text-center border-b-2 transition-colors ".concat("discover"===w?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"发现"}),(0,r.jsxs)("button",{onClick:()=>k("mutual"),className:"flex-1 px-3 py-3 text-sm font-medium text-center border-b-2 transition-colors flex items-center justify-center gap-2 ".concat("mutual"===w?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"互相喜欢"}),s.length>0&&(0,r.jsx)(d.E,{variant:"secondary",className:"text-xs ml-1",children:s.length})]})]})})]})]})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8",children:["discover"===w&&(0,r.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,r.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl md:text-4xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent mb-3 md:mb-4",children:"\uD83C\uDFAF 发现你的灵魂伴侣"}),(0,r.jsx)("p",{className:"text-gray-600 text-base md:text-lg max-w-2xl mx-auto px-4",children:"基于AI深度分析，为您精心匹配最合适的对象。每次匹配都是一次心动的可能。"})]}),(0,r.jsx)("div",{className:"max-w-md mx-auto space-y-4",children:g?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"text-lg font-medium text-gray-700",children:"AI正在为您寻找完美匹配..."}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-blue-500 h-3 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(v,"%")}})}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[v<30&&"分析您的个人资料...",v>=30&&v<60&&"寻找潜在匹配...",v>=60&&v<90&&"计算兼容性分数...",v>=90&&"生成深度分析报告..."]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.$,{onClick:$,size:"lg",className:"w-full h-14 text-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)(m.A,{className:"w-6 h-6 mr-2"}),"\uD83C\uDFAF 生成候选人矩阵 (V2.0)"]}),(0,r.jsxs)(i.$,{onClick:T,variant:"outline",size:"lg",className:"w-full h-12 text-base border-2 border-blue-200 hover:bg-blue-50",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 mr-2"}),"单一深度匹配 (V1.0)"]})]})})]}),"discover"===w&&(0,r.jsx)("div",{children:0===e.length?(0,r.jsx)(n.Zp,{className:"text-center py-12",children:(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)(n.ZB,{className:"mb-2",children:"暂无新的推荐"}),(0,r.jsx)(n.BT,{children:'点击上方"开始匹配"按钮，让AI为您寻找完美匹配！'})]})}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-6 md:mb-8 text-center",children:[(0,r.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-gray-800 mb-2",children:"\uD83D\uDCCB 待处理的匹配"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm md:text-base",children:"这些是为您推荐的匹配对象，快来看看吧！"})]}),(0,r.jsx)("div",{className:"grid gap-4 md:gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,r.jsx)(u,{match:e,onLike:Z,onPass:B},e.id))})]})}),"mutual"===w&&(0,r.jsx)("div",{children:0===s.length?(0,r.jsx)(n.Zp,{className:"text-center py-12",children:(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)(n.ZB,{className:"mb-2",children:"还没有互相喜欢的对象"}),(0,r.jsx)(n.BT,{children:"继续在发现页面寻找匹配，找到心仪的人吧！"}),(0,r.jsx)(i.$,{className:"mt-4",onClick:()=>k("discover"),children:"去发现"})]})}):(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-6 md:mb-8 text-center",children:[(0,r.jsx)("h3",{className:"text-xl md:text-2xl font-bold bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent mb-3",children:"\uD83D\uDC95 互相喜欢"}),(0,r.jsx)("p",{className:"text-gray-600 text-base md:text-lg",children:"你们互相喜欢，可以联系对方了！"})]}),(0,r.jsx)("div",{className:"grid gap-4 md:gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3",children:s.map(e=>{var t,s,a;return(0,r.jsxs)(n.Zp,{className:"w-full border-pink-200 bg-gradient-to-br from-pink-50 to-red-50",children:[(0,r.jsx)(n.aR,{className:"pb-4",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center text-white text-lg md:text-xl font-bold shadow-lg",children:null==(s=e.otherUser)||null==(t=s.name)?void 0:t.charAt(0)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(n.ZB,{className:"text-lg md:text-xl font-bold text-gray-800",children:e.otherUser.name}),(0,r.jsxs)(n.BT,{className:"text-gray-600 font-medium text-sm md:text-base",children:[e.otherUser.age,"岁 \xb7 ",e.otherUser.location]})]})]}),(0,r.jsx)(d.E,{className:"bg-gradient-to-r from-pink-500 to-red-500 text-white text-xs md:text-sm self-start sm:self-auto",children:"\uD83D\uDC95 互相喜欢"})]})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(null==(a=e.otherUser)?void 0:a.bio)&&(0,r.jsx)("div",{className:"bg-white/70 rounded-lg p-3 border-l-4 border-pink-400",children:(0,r.jsxs)("p",{className:"text-gray-700 leading-relaxed italic",children:['"',e.otherUser.bio,'"']})}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200",children:[(0,r.jsxs)("h4",{className:"font-semibold mb-2 text-green-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg",children:"\uD83D\uDCE7"}),"联系方式"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,r.jsxs)("p",{className:"font-medium",children:["邮箱：",e.otherUser.email]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"你们互相喜欢，现在可以联系对方了！"})]})]}),(0,r.jsx)(h(),{href:"/match/".concat(e.id),className:"block",children:(0,r.jsxs)(i.$,{variant:"outline",className:"w-full flex items-center gap-2 bg-white/70 hover:bg-white border-pink-200 hover:border-pink-300 transition-all duration-300",children:[(0,r.jsx)(p,{className:"w-4 h-4 text-pink-600"}),(0,r.jsx)("span",{className:"text-pink-700 font-medium",children:"回顾匹配分析"})]})})]})]},e.id)})})]})})]}),D&&(0,r.jsx)(N,{isOpen:A,onClose:()=>C(!1),hoursUntilReset:D.hoursUntilReset,nextResetTime:D.nextResetTime})]})}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(5155);s(2115);var a=s(2085),l=s(9434);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...a})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=s(5155),a=s(2115),l=s(9434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},7140:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var n=(e,t)=>{let s=(0,r.forwardRef)((s,n)=>{let{color:i="currentColor",size:d=24,strokeWidth:c=2,absoluteStrokeWidth:o,children:m,...x}=s;return(0,r.createElement)("svg",{ref:n,...a,width:d,height:d,stroke:i,strokeWidth:o?24*Number(c)/Number(d):c,className:"lucide lucide-".concat(l(e)),...x},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...(Array.isArray(m)?m:[m])||[]])});return s.displayName="".concat(e),s}},8615:(e,t,s)=>{Promise.resolve().then(s.bind(s,5724))},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(2596),a=s(9688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[514,874,579,441,684,358],()=>t(8615)),_N_E=e.O()}]);