(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155),a=t(2115),n=t(9708),i=t(2085),d=t(9434);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:o=!1,...c}=e,u=o?n.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(l({variant:a,size:i,className:t})),ref:r,...c})});o.displayName="Button"},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},2643:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(3579);let a=()=>(0,s.createClientComponentClient)()},4177:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(5155),a=t(2115),n=t(2643),i=t(285),d=t(2523),l=t(6695),o=t(6874),c=t.n(o),u=t(5695);function f(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[f,p]=(0,a.useState)(!1),[m,h]=(0,a.useState)(""),x=(0,u.useRouter)(),g=(0,n.U)(),b=async r=>{r.preventDefault(),p(!0),h("");try{let{data:r,error:s}=await g.auth.signInWithPassword({email:e,password:t});if(s)h(s.message);else if(r.user)try{let e=await fetch("/api/auth/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:r.user.id})});if(e.ok){let{isProfileComplete:r}=await e.json();r?x.push("/dashboard"):x.push("/profile?welcome=true")}else x.push("/dashboard")}catch(e){console.error("Sync error:",e),x.push("/dashboard")}}catch(e){h("An unexpected error occurred")}finally{p(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)(l.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"登录"}),(0,s.jsx)(l.BT,{children:"登录到您的寡佬AI账户"})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[m&&(0,s.jsx)("div",{className:"text-red-500 text-sm",children:m}),(0,s.jsx)("div",{children:(0,s.jsx)(d.p,{type:"email",placeholder:"邮箱",value:e,onChange:e=>r(e.target.value),required:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(d.p,{type:"password",placeholder:"密码",value:t,onChange:e=>o(e.target.value),required:!0})}),(0,s.jsx)(i.$,{type:"submit",className:"w-full",disabled:f,children:f?"登录中...":"登录"})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["还没有账户？"," ",(0,s.jsx)(c(),{href:"/auth/register",className:"text-blue-600 hover:underline",children:"注册"})]})})]})]})})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>d});var s=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});l.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},8127:(e,r,t)=>{Promise.resolve().then(t.bind(t,4177))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[514,874,579,441,684,358],()=>r(8127)),_N_E=e.O()}]);