(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{88:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(5155),a=t(2115),l=t(285),i=t(2523),n=t(3655),o=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var d=t(2085),c=t(9434);let u=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(o,{ref:r,className:(0,c.cn)(u(),t),...a})});m.displayName=o.displayName;let h=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...a})});h.displayName="Textarea";var p=t(6695),f=t(2643),x=t(5695),g=t(4266);function b(){let[e,r]=(0,a.useState)({name:"",age:0,gender:"",location:"",bio:"",interests:[],selfDescription:"",lookingFor:"",relationshipGoals:""}),[t,n]=(0,a.useState)(!1),[o,d]=(0,a.useState)(""),[c,u]=(0,a.useState)(""),[b,v]=(0,a.useState)(""),[j,y]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),F=(0,x.useRouter)(),A=(0,f.U)();(0,a.useEffect)(()=>{y("true"===new URLSearchParams(window.location.search).get("welcome")),D()},[]);let D=async()=>{let{data:{user:e}}=await A.auth.getUser();if(!e)return void F.push("/auth/login");try{let e=await fetch("/api/profile");if(e.ok){let{user:t,profile:s}=await e.json();t&&r(e=>({...e,name:t.name||"",age:t.age||0,gender:t.gender||"",location:t.location||"",bio:t.bio||"",interests:t.interests||[]})),s&&r(e=>({...e,selfDescription:s.selfDescription||"",lookingFor:s.lookingFor||"",relationshipGoals:s.relationshipGoals||""}))}}catch(e){console.error("Error loading profile:",e)}},R=()=>{b.trim()&&!e.interests.includes(b.trim())&&(r(e=>({...e,interests:[...e.interests,b.trim()]})),v(""))},S=e=>{r(r=>({...r,interests:r.interests.filter(r=>r!==e)}))},G=async()=>{N(!0),d("");try{let t=await fetch("/api/profile/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,age:e.age,gender:e.gender,location:e.location,interests:e.interests})});if(t.ok){let e=await t.json();r(r=>({...r,bio:e.bio||r.bio,selfDescription:e.selfDescription||r.selfDescription,lookingFor:e.lookingFor||r.lookingFor,relationshipGoals:e.relationshipGoals||r.relationshipGoals})),u("AI已为您生成个人资料示例，您可以根据需要进行修改！")}else d("AI生成失败，请重试")}catch(e){console.error("Error generating AI profile:",e),d("AI生成失败，请重试")}finally{N(!1)}},I=async r=>{r.preventDefault(),n(!0),d(""),u("");try{let r=await fetch("/api/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(r.ok){u("资料保存成功！");let e=new URLSearchParams(window.location.search);"true"===e.get("welcome")?setTimeout(()=>{F.push("/dashboard")},1500):C(!0)}else{let e=await r.json();d(e.error||"保存失败，请重试")}}catch(e){d("保存失败，请重试")}finally{n(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 py-8",children:[(0,s.jsx)("div",{className:"max-w-2xl mx-auto px-4",children:(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:j?"欢迎加入寡佬AI！":"个人资料"}),(0,s.jsx)(p.BT,{children:j?"请完善您的个人信息，让AI为您找到最合适的伴侣":"完善您的个人信息，让AI更好地为您匹配"}),j&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"\uD83C\uDF89 注册成功！完善资料后即可开始您的智能匹配之旅。"})})]}),(0,s.jsx)(p.Wu,{children:(0,s.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[o&&(0,s.jsx)("div",{className:"text-red-500 text-sm",children:o}),c&&(0,s.jsx)("div",{className:"text-green-500 text-sm",children:c}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"name",children:"姓名"}),(0,s.jsx)(i.p,{id:"name",value:e.name,onChange:e=>r(r=>({...r,name:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"age",children:"年龄"}),(0,s.jsx)(i.p,{id:"age",type:"number",value:e.age||"",onChange:e=>r(r=>({...r,age:parseInt(e.target.value)||0})),required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"gender",children:"性别"}),(0,s.jsxs)("select",{id:"gender",value:e.gender,onChange:e=>r(r=>({...r,gender:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm",required:!0,children:[(0,s.jsx)("option",{value:"",children:"请选择"}),(0,s.jsx)("option",{value:"male",children:"男"}),(0,s.jsx)("option",{value:"female",children:"女"}),(0,s.jsx)("option",{value:"other",children:"其他"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"location",children:"所在地"}),(0,s.jsx)(i.p,{id:"location",value:e.location,onChange:e=>r(r=>({...r,location:e.target.value})),required:!0})]})]}),!e.bio&&!e.selfDescription&&!e.lookingFor&&!e.relationshipGoals&&(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-blue-600"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:"AI智能生成"})]})}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"让AI根据您的基本信息生成个人资料示例，您可以在此基础上进行修改。"}),(0,s.jsx)(l.$,{type:"button",onClick:G,disabled:w||!e.name||!e.age||!e.gender,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:w?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"w-4 h-4 mr-2 animate-spin"}),"AI生成中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"一键生成个人资料"]})}),(!e.name||!e.age||!e.gender)&&(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"请先填写姓名、年龄和性别信息"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"bio",children:"个人简介"}),(0,s.jsx)(h,{id:"bio",value:e.bio,onChange:e=>r(r=>({...r,bio:e.target.value})),placeholder:"简单介绍一下自己...",rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{children:"兴趣爱好"}),(0,s.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,s.jsx)(i.p,{value:b,onChange:e=>v(e.target.value),placeholder:"添加兴趣爱好",onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),R())}),(0,s.jsx)(l.$,{type:"button",onClick:R,children:"添加"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.interests.map((e,r)=>(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center gap-1",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>S(e),className:"text-blue-600 hover:text-blue-800",children:"\xd7"})]},r))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"selfDescription",children:"自我描述"}),(0,s.jsx)(h,{id:"selfDescription",value:e.selfDescription,onChange:e=>r(r=>({...r,selfDescription:e.target.value})),placeholder:"详细描述一下自己的性格、价值观等...",rows:4})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"lookingFor",children:"寻找对象"}),(0,s.jsx)(h,{id:"lookingFor",value:e.lookingFor,onChange:e=>r(r=>({...r,lookingFor:e.target.value})),placeholder:"描述您理想的伴侣...",rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(m,{htmlFor:"relationshipGoals",children:"感情目标"}),(0,s.jsx)(h,{id:"relationshipGoals",value:e.relationshipGoals,onChange:e=>r(r=>({...r,relationshipGoals:e.target.value})),placeholder:"您希望建立什么样的关系？",rows:2})]}),(0,s.jsx)(l.$,{type:"submit",className:"w-full",disabled:t,children:t?"保存中...":"保存资料"})]})})]})}),k&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)(p.Zp,{className:"w-full max-w-md mx-auto bg-white shadow-2xl",children:[(0,s.jsxs)(p.aR,{className:"text-center pb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(g.A,{className:"w-8 h-8 text-green-600"})}),(0,s.jsx)(p.ZB,{className:"text-xl font-bold text-gray-900 mb-2",children:"资料保存成功！"}),(0,s.jsx)(p.BT,{className:"text-gray-600",children:"您的个人资料已更新，现在可以开始寻找匹配了"})]}),(0,s.jsx)(p.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(l.$,{variant:"outline",className:"flex-1",onClick:()=>C(!1),children:"继续编辑"}),(0,s.jsx)(l.$,{className:"flex-1 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700",onClick:()=>{C(!1),F.push("/dashboard")},children:"开始匹配"})]})})]})})]})}},285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(5155),a=t(2115),l=t(9708),i=t(2085),n=t(9434);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,s.jsx)(u,{className:(0,n.cn)(o({variant:a,size:i,className:t})),ref:r,...c})});d.displayName="Button"},2064:(e,r,t)=>{Promise.resolve().then(t.bind(t,88))},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155),a=t(2115),l=t(9434);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},2643:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(3579);let a=()=>(0,s.createClientComponentClient)()},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>n});var s=t(2115),a=t(7650),l=t(9708),i=t(5155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,l.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?t:r,{...l,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},4266:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(7140).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(5155),a=t(2115),l=t(9434);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});n.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},7140:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var i=(e,r)=>{let t=(0,s.forwardRef)((t,i)=>{let{color:n="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:c,children:u,...m}=t;return(0,s.createElement)("svg",{ref:i,...a,width:o,height:o,stroke:n,strokeWidth:c?24*Number(d)/Number(o):d,className:"lucide lucide-".concat(l(e)),...m},[...r.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...(Array.isArray(u)?u:[u])||[]])});return t.displayName="".concat(e),t}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(2596),a=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[514,579,441,684,358],()=>r(2064)),_N_E=e.O()}]);