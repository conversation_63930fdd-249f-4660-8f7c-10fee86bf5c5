(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[496],{792:(e,t,n)=>{"use strict";n.d(t,{oz:()=>nL});var r={};n.r(r),n.d(r,{boolean:()=>g,booleanish:()=>y,commaOrSpaceSeparated:()=>w,commaSeparated:()=>b,number:()=>x,overloadedBoolean:()=>v,spaceSeparated:()=>k});var i={};n.r(i),n.d(i,{attentionMarkers:()=>tT,contentInitial:()=>tb,disable:()=>tA,document:()=>tk,flow:()=>tS,flowInitial:()=>tw,insideSpan:()=>tE,string:()=>tC,text:()=>tI});let l=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,o=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a={};function u(e,t){return((t||a).jsx?o:l).test(e)}let s=/[ \t\n\f\r]/g;function c(e){return""===e.replace(s,"")}class f{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function p(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new f(n,r,t)}function h(e){return e.toLowerCase()}f.prototype.normal={},f.prototype.property={},f.prototype.space=void 0;class d{constructor(e,t){this.attribute=t,this.property=e}}d.prototype.attribute="",d.prototype.booleanish=!1,d.prototype.boolean=!1,d.prototype.commaOrSpaceSeparated=!1,d.prototype.commaSeparated=!1,d.prototype.defined=!1,d.prototype.mustUseProperty=!1,d.prototype.number=!1,d.prototype.overloadedBoolean=!1,d.prototype.property="",d.prototype.spaceSeparated=!1,d.prototype.space=void 0;let m=0,g=S(),y=S(),v=S(),x=S(),k=S(),b=S(),w=S();function S(){return 2**++m}let C=Object.keys(r);class I extends d{constructor(e,t,n,i){let l=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++l<C.length;){let e=C[l];!function(e,t,n){n&&(e[t]=n)}(this,C[l],(n&r[e])===r[e])}}}function E(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let l=new I(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[h(r)]=r,n[h(l.attribute)]=r}return new f(t,n,e.space)}I.prototype.defined=!0;let T=E({properties:{ariaActiveDescendant:null,ariaAtomic:y,ariaAutoComplete:null,ariaBusy:y,ariaChecked:y,ariaColCount:x,ariaColIndex:x,ariaColSpan:x,ariaControls:k,ariaCurrent:null,ariaDescribedBy:k,ariaDetails:null,ariaDisabled:y,ariaDropEffect:k,ariaErrorMessage:null,ariaExpanded:y,ariaFlowTo:k,ariaGrabbed:y,ariaHasPopup:null,ariaHidden:y,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:k,ariaLevel:x,ariaLive:null,ariaModal:y,ariaMultiLine:y,ariaMultiSelectable:y,ariaOrientation:null,ariaOwns:k,ariaPlaceholder:null,ariaPosInSet:x,ariaPressed:y,ariaReadOnly:y,ariaRelevant:null,ariaRequired:y,ariaRoleDescription:k,ariaRowCount:x,ariaRowIndex:x,ariaRowSpan:x,ariaSelected:y,ariaSetSize:x,ariaSort:null,ariaValueMax:x,ariaValueMin:x,ariaValueNow:x,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function A(e,t){return t in e?e[t]:t}function P(e,t){return A(e,t.toLowerCase())}let L=E({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:b,acceptCharset:k,accessKey:k,action:null,allow:null,allowFullScreen:g,allowPaymentRequest:g,allowUserMedia:g,alt:null,as:null,async:g,autoCapitalize:null,autoComplete:k,autoFocus:g,autoPlay:g,blocking:k,capture:null,charSet:null,checked:g,cite:null,className:k,cols:x,colSpan:null,content:null,contentEditable:y,controls:g,controlsList:k,coords:x|b,crossOrigin:null,data:null,dateTime:null,decoding:null,default:g,defer:g,dir:null,dirName:null,disabled:g,download:v,draggable:y,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:g,formTarget:null,headers:k,height:x,hidden:v,high:x,href:null,hrefLang:null,htmlFor:k,httpEquiv:k,id:null,imageSizes:null,imageSrcSet:null,inert:g,inputMode:null,integrity:null,is:null,isMap:g,itemId:null,itemProp:k,itemRef:k,itemScope:g,itemType:k,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:g,low:x,manifest:null,max:null,maxLength:x,media:null,method:null,min:null,minLength:x,multiple:g,muted:g,name:null,nonce:null,noModule:g,noValidate:g,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:g,optimum:x,pattern:null,ping:k,placeholder:null,playsInline:g,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:g,referrerPolicy:null,rel:k,required:g,reversed:g,rows:x,rowSpan:x,sandbox:k,scope:null,scoped:g,seamless:g,selected:g,shadowRootClonable:g,shadowRootDelegatesFocus:g,shadowRootMode:null,shape:null,size:x,sizes:null,slot:null,span:x,spellCheck:y,src:null,srcDoc:null,srcLang:null,srcSet:null,start:x,step:null,style:null,tabIndex:x,target:null,title:null,translate:null,type:null,typeMustMatch:g,useMap:null,value:y,width:x,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:k,axis:null,background:null,bgColor:null,border:x,borderColor:null,bottomMargin:x,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:g,declare:g,event:null,face:null,frame:null,frameBorder:null,hSpace:x,leftMargin:x,link:null,longDesc:null,lowSrc:null,marginHeight:x,marginWidth:x,noResize:g,noHref:g,noShade:g,noWrap:g,object:null,profile:null,prompt:null,rev:null,rightMargin:x,rules:null,scheme:null,scrolling:y,standby:null,summary:null,text:null,topMargin:x,valueType:null,version:null,vAlign:null,vLink:null,vSpace:x,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:g,disableRemotePlayback:g,prefix:null,property:null,results:x,security:null,unselectable:null},space:"html",transform:P}),D=E({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:w,accentHeight:x,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:x,amplitude:x,arabicForm:null,ascent:x,attributeName:null,attributeType:null,azimuth:x,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:x,by:null,calcMode:null,capHeight:x,className:k,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:x,diffuseConstant:x,direction:null,display:null,dur:null,divisor:x,dominantBaseline:null,download:g,dx:null,dy:null,edgeMode:null,editable:null,elevation:x,enableBackground:null,end:null,event:null,exponent:x,externalResourcesRequired:null,fill:null,fillOpacity:x,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:b,g2:b,glyphName:b,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:x,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:x,horizOriginX:x,horizOriginY:x,id:null,ideographic:x,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:x,k:x,k1:x,k2:x,k3:x,k4:x,kernelMatrix:w,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:x,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:x,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:x,overlineThickness:x,paintOrder:null,panose1:null,path:null,pathLength:x,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:k,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:x,pointsAtY:x,pointsAtZ:x,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:w,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:w,rev:w,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:w,requiredFeatures:w,requiredFonts:w,requiredFormats:w,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:x,specularExponent:x,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:x,strikethroughThickness:x,string:null,stroke:null,strokeDashArray:w,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:x,strokeOpacity:x,strokeWidth:null,style:null,surfaceScale:x,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:w,tabIndex:x,tableValues:null,target:null,targetX:x,targetY:x,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:w,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:x,underlineThickness:x,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:x,values:null,vAlphabetic:x,vMathematical:x,vectorEffect:null,vHanging:x,vIdeographic:x,version:null,vertAdvY:x,vertOriginX:x,vertOriginY:x,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:x,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:A}),O=E({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),z=E({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:P}),M=E({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),N=p([T,L,O,z,M],"html"),F=p([T,D,O,z,M],"svg"),_=/[A-Z]/g,R=/-[a-z]/g,j=/^data[-\w.:]+$/i;function B(e){return"-"+e.toLowerCase()}function H(e){return e.charAt(1).toUpperCase()}let U={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var V=n(3724);let q=Y("end"),W=Y("start");function Y(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function K(e){return e&&"object"==typeof e?"position"in e||"type"in e?Q(e.position):"start"in e||"end"in e?Q(e):"line"in e||"column"in e?$(e):"":""}function $(e){return X(e&&e.line)+":"+X(e&&e.column)}function Q(e){return $(e&&e.start)+"-"+$(e&&e.end)}function X(e){return e&&"number"==typeof e?e:1}class Z extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},l=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(l=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let o=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=o?o.line:void 0,this.name=K(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=l&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}Z.prototype.file="",Z.prototype.name="",Z.prototype.reason="",Z.prototype.message="",Z.prototype.stack="",Z.prototype.column=void 0,Z.prototype.line=void 0,Z.prototype.ancestors=void 0,Z.prototype.cause=void 0,Z.prototype.fatal=void 0,Z.prototype.place=void 0,Z.prototype.ruleId=void 0,Z.prototype.source=void 0;let J={}.hasOwnProperty,G=new Map,ee=/[A-Z]/g,et=new Set(["table","tbody","thead","tfoot","tr"]),en=new Set(["td","th"]),er="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ei(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=F),e.ancestors.push(t);let l=eu(e,t.tagName,!1),o=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&J.call(t.properties,r)){let l=function(e,t,n){let r=function(e,t){let n=h(t),r=t,i=d;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&j.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(R,H);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!R.test(e)){let n=e.replace(_,B);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=I}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return V(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new Z("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=er+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)J.call(e,t)&&(n[function(e){let t=e.replace(ee,ec);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?U[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(l){let[r,o]=l;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&en.has(t.tagName)?n=o:i[r]=o}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),a=ea(e,t);return et.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&c(e.value):c(e))})),el(e,o,l,t),eo(o,a),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}es(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(e.schema=F),e.ancestors.push(t);let l=null===t.name?e.Fragment:eu(e,t.name,!0),o=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let i=t.expression;i.type;let l=i.properties[0];l.type,Object.assign(n,e.evaluater.evaluateExpression(l.argument))}else es(e,t.position);else{let i,l=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else es(e,t.position);else i=null===r.value||r.value;n[l]=i}return n}(e,t),a=ea(e,t);return el(e,o,l,t),eo(o,a),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);es(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return eo(r,ea(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function el(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function eo(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function ea(e,t){let n=[],r=-1,i=e.passKeys?new Map:G;for(;++r<t.children.length;){let l,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=i.get(e)||0;l=e+"-"+t,i.set(e,t+1)}}let a=ei(e,o,l);void 0!==a&&n.push(a)}return n}function eu(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),i=-1;for(;++i<n.length;){let t=u(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=u(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return J.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);es(e)}function es(e,t){let n=new Z("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=er+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ec(e){return"-"+e.toLowerCase()}let ef={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ep=n(5155);n(2115);let eh={};function ed(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return em(e.children,t,n)}return Array.isArray(e)?em(e,t,n):""}function em(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=ed(e[i],t,n);return r.join("")}function eg(e,t,n,r){let i,l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function ey(e,t){return e.length>0?(eg(e,e.length,0,t),e):t}class ev{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ex(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ex(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ex(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ex(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ex(this.left,t.reverse())}}}function ex(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ek(e){let t,n,r,i,l,o,a,u={},s=-1,c=new ev(e);for(;++s<c.length;){for(;s in u;)s=u[s];if(t=c.get(s),s&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(s-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(u,function(e,t){let n,r,i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],u=i._tokenizer;!u&&(u=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(u._contentTypeTextTrailing=!0));let s=u.events,c=[],f={},p=-1,h=i,d=0,m=0,g=[0];for(;h;){for(;e.get(++o)[1]!==h;);a.push(o),!h._tokenizer&&(n=l.sliceStream(h),h.next||n.push(null),r&&u.defineSkip(h.start),h._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=!0),u.write(n),h._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=void 0)),r=h,h=h.next}for(h=i;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(u.events=[],h?(h._tokenizer=void 0,h.previous=void 0):g.pop(),p=g.length;p--;){let t=s.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[d+c[p][0]]=d+c[p][1],d+=c[p][1]-c[p][0]-1;return f}(c,s)),s=u[s],a=!0);else if(t[1]._container){for(r=s,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(l=c.slice(n,s)).unshift(t),c.splice(n,s-n+1,l))}}return eg(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}let eb={}.hasOwnProperty,ew=eM(/[A-Za-z]/),eS=eM(/[\dA-Za-z]/),eC=eM(/[#-'*+\--9=?A-Z^-~]/);function eI(e){return null!==e&&(e<32||127===e)}let eE=eM(/\d/),eT=eM(/[\dA-Fa-f]/),eA=eM(/[!-/:-@[-`{-~]/);function eP(e){return null!==e&&e<-2}function eL(e){return null!==e&&(e<0||32===e)}function eD(e){return -2===e||-1===e||32===e}let eO=eM(/\p{P}|\p{S}/u),ez=eM(/\s/);function eM(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function eN(e,t,n,r){let i=r?r-1:Number.POSITIVE_INFINITY,l=0;return function(r){return eD(r)?(e.enter(n),function r(o){return eD(o)&&l++<i?(e.consume(o),r):(e.exit(n),t(o))}(r)):t(r)}}let eF={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eN(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eP(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},e_={tokenize:function(e){let t,n,r,i=this,l=[],o=0;return a;function a(t){if(o<l.length){let n=l[o];return i.containerState=n[1],e.attempt(n[0].continuation,u,s)(t)}return s(t)}function u(e){if(o++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,l=r;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){n=i.events[l][1].end;break}g(o);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return eg(i.events,l+1,0,i.events.slice(r)),i.events.length=a,s(e)}return a(e)}function s(n){if(o===l.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return d(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(eR,c,f)(n)}function c(e){return t&&y(),g(o),p(e)}function f(e){return i.parser.lazy[i.now().line]=o!==l.length,r=i.now().offset,d(e)}function p(t){return i.containerState={},e.attempt(eR,h,d)(t)}function h(e){return o++,l.push([i.currentConstruct,i.containerState]),p(e)}function d(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eP(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,l){let a=i.sliceStream(e);if(l&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,l=t.events.length;for(;l--;)if(t.events[l][1].start.offset<r&&(!t.events[l][1].end||t.events[l][1].end.offset>r))return;let a=i.events.length,u=a;for(;u--;)if("exit"===i.events[u][0]&&"chunkFlow"===i.events[u][1].type){if(e){n=i.events[u][1].end;break}e=!0}for(g(o),l=a;l<i.events.length;)i.events[l][1].end={...n},l++;eg(i.events,u+1,0,i.events.slice(a)),i.events.length=l}}function g(t){let n=l.length;for(;n-- >t;){let t=l[n];i.containerState=t[1],t[0].exit.call(i,e)}l.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},eR={tokenize:function(e,t,n){return eN(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},ej={partial:!0,tokenize:function(e,t,n){return function(t){return eD(t)?eN(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eP(e)?t(e):n(e)}}},eB={resolve:function(e){return ek(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):eP(t)?e.check(eH,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eH={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eN(e,i,"linePrefix")};function i(i){if(null===i||eP(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},eU={tokenize:function(e){let t=this,n=e.attempt(ej,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,eN(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eB,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},eV={resolveAll:eK()},eq=eY("string"),eW=eY("text");function eY(e){return{resolveAll:eK("text"===e?e$:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return u(e)?i(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return u(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function u(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eK(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function e$(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,u=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)u++,a--;if(a)break;a=-1}else if(-2===e)r=!0,u++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(u=0),u){let l={type:n===e.length||r||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-u,offset:i.end.offset-u},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let eQ={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){var o;return e.enter("thematicBreak"),r=o=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),eD(n)?eN(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||eP(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},eX={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(ej,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,eN(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eD(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eJ,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,eN(e,e.attempt(eX,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:eE(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(eQ,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return eE(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(ej,r.interrupt?n:u,e.attempt(eZ,c,s))}function u(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function s(t){return eD(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eZ={partial:!0,tokenize:function(e,t,n){let r=this;return eN(e,function(e){let i=r.events[r.events.length-1];return!eD(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eJ={partial:!0,tokenize:function(e,t,n){let r=this;return eN(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eG={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eD(t)?eN(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eG,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return eD(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function e1(e,t,n,r,i,l,o,a,u){let s=u||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),f):null===t||32===t||41===t||eI(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),d(t))};function f(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||eP(t)?n(t):(e.consume(t),92===t?h:p)}function h(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function d(i){return!c&&(null===i||41===i||eL(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<s&&40===i?(e.consume(i),c++,d):41===i?(e.consume(i),c--,d):null===i||32===i||40===i||eI(i)?n(i):(e.consume(i),92===i?m:d)}function m(t){return 40===t||41===t||92===t?(e.consume(t),d):d(t)}}function e0(e,t,n,r,i,l){let o,a=this,u=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),s};function s(f){return u>999||null===f||91===f||93===f&&!o||94===f&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(f):93===f?(e.exit(l),e.enter(i),e.consume(f),e.exit(i),e.exit(r),t):eP(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||eP(t)||u++>999?(e.exit("chunkString"),s(t)):(e.consume(t),o||(o=!eD(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}}function e2(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),u(n))}function u(t){return t===o?(e.exit(l),a(o)):null===t?n(t):eP(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eN(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(t))}function s(t){return t===o||null===t||eP(t)?(e.exit("chunkString"),u(t)):(e.consume(t),92===t?c:s)}function c(t){return t===o||92===t?(e.consume(t),s):s(t)}}function e4(e,t){let n;return function r(i){return eP(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):eD(i)?eN(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}function e3(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let e5={partial:!0,tokenize:function(e,t,n){return function(t){return eL(t)?e4(e,r)(t):n(t)};function r(t){return e2(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return eD(t)?eN(e,l,"whitespace")(t):l(t)}function l(e){return null===e||eP(e)?t(e):n(e)}}},e6={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),eN(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):eP(n)?e.attempt(e9,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eP(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},e9={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):eP(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):eN(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):eP(e)?i(e):n(e)}}},e7={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var o;let a,u=i.events.length;for(;u--;)if("lineEnding"!==i.events[u][1].type&&"linePrefix"!==i.events[u][1].type&&"content"!==i.events[u][1].type){a="paragraph"===i.events[u][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eD(n)?eN(e,l,"lineSuffix")(n):l(n))}(o)):n(t)};function l(r){return null===r||eP(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},e8=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],te=["pre","script","style","textarea"],tt={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(ej,t,n)}}},tn={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eP(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tr={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ti={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),eD(t)?eN(e,u,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),eD(i)?eN(e,s,"whitespace")(i):s(i)):n(i)}(t)):n(t)}function s(r){return null===r||eP(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var l=t;let s=i.events[i.events.length-1];return o=s&&"linePrefix"===s[1].type?s[2].sliceSerialize(s[1],!0).length:0,r=l,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),eD(i)?eN(e,u,"whitespace")(i):u(i))}(l)};function u(l){return null===l||eP(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(tr,c,d)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eP(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(i)):eD(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),eN(e,s,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function s(t){return null===t||eP(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eP(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(l,d,f)(t)}function f(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return o>0&&eD(t)?eN(e,h,"linePrefix",o+1)(t):h(t)}function h(t){return null===t||eP(t)?e.check(tr,c,d)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eP(n)?(e.exit("codeFlowValue"),h(n)):(e.consume(n),t)}(t))}function d(n){return e.exit("codeFenced"),t(n)}}},tl=document.createElement("i");function to(e){let t="&"+e+";";tl.innerHTML=t;let n=tl.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let ta={name:"characterReference",tokenize:function(e,t,n){let r,i,l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),r=31,i=eS,s(t))}function u(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=eT,s):(e.enter("characterReferenceValue"),r=7,i=eE,s(t))}function s(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==eS||to(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),s):n(a)}}},tu={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eA(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},ts={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),eN(e,t,"linePrefix")}}};function tc(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}let tf={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eg(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,l,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=o);let u={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},s={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return l=ey(l=[["enter",u,t],["enter",s,t]],e.slice(r+1,r+a+3)),l=ey(l,[["enter",c,t]]),l=ey(l,tc(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),l=ey(l,[["exit",c,t],e[i-2],e[i-1],["exit",s,t]]),l=ey(l,e.slice(i+1)),l=ey(l,[["exit",u,t]]),eg(e,r,e.length,l),e},tokenize:function(e,t,n){let r,i,l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes(e3(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(tp,s,i?s:c)(t):91===t?e.attempt(th,s,i?u:c)(t):i?s(t):c(t)}function u(t){return e.attempt(td,s,c)(t)}function s(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},tp={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return eL(t)?e4(e,i)(t):i(t)}function i(t){return 41===t?s(t):e1(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return eL(t)?e4(e,a)(t):s(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?e2(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):s(t)}function u(t){return eL(t)?e4(e,s)(t):s(t)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},th={tokenize:function(e,t,n){let r=this;return function(t){return e0.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes(e3(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},td={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tm={name:"labelStartImage",resolveAll:tf.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tg(e){return null===e||eL(e)||ez(e)?1:eO(e)?2:void 0}let ty={name:"attention",resolveAll:function(e,t){let n,r,i,l,o,a,u,s,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let f={...e[n][1].end},p={...e[c][1].start};tv(f,-a),tv(p,a),l={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...l.start},end:{...o.end}},e[n][1].end={...l.start},e[c][1].start={...o.end},u=[],e[n][1].end.offset-e[n][1].start.offset&&(u=ey(u,[["enter",e[n][1],t],["exit",e[n][1],t]])),u=ey(u,[["enter",r,t],["enter",l,t],["exit",l,t],["enter",i,t]]),u=ey(u,tc(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),u=ey(u,[["exit",i,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(s=2,u=ey(u,[["enter",e[c][1],t],["exit",e[c][1],t]])):s=0,eg(e,n-1,c-n+3,u),c=n+u.length-s-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=tg(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let u=e.exit("attentionSequence"),s=tg(a),c=!s||2===s&&l||r.includes(a),f=!l||2===l&&s||r.includes(i);return u._open=!!(42===n?c:c&&(l||!f)),u._close=!!(42===n?f:f&&(s||!c)),t(a)}(o)}}};function tv(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tx={name:"labelStartLink",resolveAll:tf.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tk={42:eX,43:eX,45:eX,48:eX,49:eX,50:eX,51:eX,52:eX,53:eX,54:eX,55:eX,56:eX,57:eX,62:eG},tb={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,e0.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function l(t){return(r=e3(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return eL(t)?e4(e,a)(t):a(t)}function a(t){return e1(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function u(t){return e.attempt(e5,s,s)(t)}function s(t){return eD(t)?eN(e,c,"whitespace")(t):c(t)}function c(l){return null===l||eP(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},tw={[-2]:e6,[-1]:e6,32:e6},tS={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,l=3;return"whitespace"===e[3][1].type&&(l+=2),i-2>l&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(l===i-1||i-4>l&&"whitespace"===e[i-2][1].type)&&(i-=l+1===i?2:4),i>l&&(n={type:"atxHeadingText",start:e[l][1].start,end:e[i][1].end},r={type:"chunkText",start:e[l][1].start,end:e[i][1].end,contentType:"text"},eg(e,l,i-l+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var l;return e.enter("atxHeading"),l=i,e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||eL(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eP(r)?(e.exit("atxHeading"),t(r)):eD(r)?eN(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||eL(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(l)}}},42:eQ,45:[e7,eQ],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a,u=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),s};function s(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,h):63===o?(e.consume(o),r=3,u.interrupt?t:O):ew(o)?(e.consume(o),l=String.fromCharCode(o),d):n(o)}function c(i){return 45===i?(e.consume(i),r=2,f):91===i?(e.consume(i),r=5,o=0,p):ew(i)?(e.consume(i),r=4,u.interrupt?t:O):n(i)}function f(r){return 45===r?(e.consume(r),u.interrupt?t:O):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?u.interrupt?t:C:p:n(r)}function h(t){return ew(t)?(e.consume(t),l=String.fromCharCode(t),d):n(t)}function d(o){if(null===o||47===o||62===o||eL(o)){let a=47===o,s=l.toLowerCase();return!a&&!i&&te.includes(s)?(r=1,u.interrupt?t(o):C(o)):e8.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),m):u.interrupt?t(o):C(o):(r=7,u.interrupt&&!u.parser.lazy[u.now().line]?n(o):i?function t(n){return eD(n)?(e.consume(n),t):w(n)}(o):g(o))}return 45===o||eS(o)?(e.consume(o),l+=String.fromCharCode(o),d):n(o)}function m(r){return 62===r?(e.consume(r),u.interrupt?t:C):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||ew(t)?(e.consume(t),y):eD(t)?(e.consume(t),g):w(t)}function y(t){return 45===t||46===t||58===t||95===t||eS(t)?(e.consume(t),y):v(t)}function v(t){return 61===t?(e.consume(t),x):eD(t)?(e.consume(t),v):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,k):eD(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||eL(n)?v(n):(e.consume(n),t)}(t)}function k(t){return t===a?(e.consume(t),a=null,b):null===t||eP(t)?n(t):(e.consume(t),k)}function b(e){return 47===e||62===e||eD(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||eP(t)?C(t):eD(t)?(e.consume(t),S):n(t)}function C(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),P):62===t&&4===r?(e.consume(t),z):63===t&&3===r?(e.consume(t),O):93===t&&5===r?(e.consume(t),D):eP(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(tt,M,I)(t)):null===t||eP(t)?(e.exit("htmlFlowData"),I(t)):(e.consume(t),C)}function I(t){return e.check(tn,E,M)(t)}function E(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),T}function T(t){return null===t||eP(t)?I(t):(e.enter("htmlFlowData"),C(t))}function A(t){return 45===t?(e.consume(t),O):C(t)}function P(t){return 47===t?(e.consume(t),l="",L):C(t)}function L(t){if(62===t){let n=l.toLowerCase();return te.includes(n)?(e.consume(t),z):C(t)}return ew(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),L):C(t)}function D(t){return 93===t?(e.consume(t),O):C(t)}function O(t){return 62===t?(e.consume(t),z):45===t&&2===r?(e.consume(t),O):C(t)}function z(t){return null===t||eP(t)?(e.exit("htmlFlowData"),M(t)):(e.consume(t),z)}function M(n){return e.exit("htmlFlow"),t(n)}}},61:e7,95:eQ,96:ti,126:ti},tC={38:ta,92:tu},tI={[-5]:ts,[-4]:ts,[-3]:ts,33:tm,38:ta,42:ty,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return ew(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||eS(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||eS(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eI(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),u):eC(t)?(e.consume(t),a):n(t)}function u(i){return eS(i)?function i(l){return 46===l?(e.consume(l),r=0,u):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||eS(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),u):47===t?(e.consume(t),k):63===t?(e.consume(t),v):ew(t)?(e.consume(t),w):n(t)}function u(t){return 45===t?(e.consume(t),s):91===t?(e.consume(t),i=0,h):ew(t)?(e.consume(t),y):n(t)}function s(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):eP(t)?(l=c,L(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?P(e):45===e?f(e):c(e)}function h(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?d:h):n(t)}function d(t){return null===t?n(t):93===t?(e.consume(t),m):eP(t)?(l=d,L(t)):(e.consume(t),d)}function m(t){return 93===t?(e.consume(t),g):d(t)}function g(t){return 62===t?P(t):93===t?(e.consume(t),g):d(t)}function y(t){return null===t||62===t?P(t):eP(t)?(l=y,L(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),x):eP(t)?(l=v,L(t)):(e.consume(t),v)}function x(e){return 62===e?P(e):v(e)}function k(t){return ew(t)?(e.consume(t),b):n(t)}function b(t){return 45===t||eS(t)?(e.consume(t),b):function t(n){return eP(n)?(l=t,L(n)):eD(n)?(e.consume(n),t):P(n)}(t)}function w(t){return 45===t||eS(t)?(e.consume(t),w):47===t||62===t||eL(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),P):58===t||95===t||ew(t)?(e.consume(t),C):eP(t)?(l=S,L(t)):eD(t)?(e.consume(t),S):P(t)}function C(t){return 45===t||46===t||58===t||95===t||eS(t)?(e.consume(t),C):function t(n){return 61===n?(e.consume(n),I):eP(n)?(l=t,L(n)):eD(n)?(e.consume(n),t):S(n)}(t)}function I(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,E):eP(t)?(l=I,L(t)):eD(t)?(e.consume(t),I):(e.consume(t),T)}function E(t){return t===r?(e.consume(t),r=void 0,A):null===t?n(t):eP(t)?(l=E,L(t)):(e.consume(t),E)}function T(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||eL(t)?S(t):(e.consume(t),T)}function A(e){return 47===e||62===e||eL(e)?S(e):n(e)}function P(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function L(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return eD(t)?eN(e,O,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):O(t)}function O(t){return e.enter("htmlTextData"),l(t)}}}],91:tx,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eP(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tu],93:tf,95:ty,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(u){return null===u?n(u):32===u?(e.enter("space"),e.consume(u),e.exit("space"),o):96===u?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(u)):eP(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(u))}function a(t){return null===t||32===t||96===t||eP(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},tE={null:[ty,eV]},tT={null:[42,95]},tA={null:[]},tP=/[\0\t\n\r]/g;function tL(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tD=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tO(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tL(n.slice(t?2:1),t?16:10)}return to(n)||e}let tz={}.hasOwnProperty;function tM(e){return{line:e.line,column:e.column,offset:e.offset}}function tN(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+K({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+K({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+K({start:t.start,end:t.end})+") is still open")}function tF(e){let t=this;t.parser=function(n){var r,l;let o,a,u,s;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(l=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:s,autolinkEmail:s,atxHeading:r(d),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:s,characterReference:s,codeFenced:r(h),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(h,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:s,data:s,codeFlowValue:s,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:s,htmlText:r(g,i),htmlTextData:s,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(v,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(v),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(d),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tL(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=to(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=tM(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e3(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(f),hardBreakTrailing:o(f),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tD,tO),n.identifier=e3(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tM(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(s.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e3(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tz.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:l,exit:a,buffer:i,resume:u,data:n},s=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?s.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,u=-1,s=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?u++:u--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||u||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!u&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===u&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=s,n}(e,s.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tz.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||tN).call(o,void 0,e[0])}for(r.position={start:tM(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tM(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){l.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tM(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tN).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+K({start:e.start,end:e.end})+"): it’s not open");n.position.end=tM(e.end)}function u(){var e;return ed(this.stack.pop(),"boolean"!=typeof eh.includeImageAlt||eh.includeImageAlt,"boolean"!=typeof eh.includeHtml||eh.includeHtml)}function s(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tM(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tM(e.end)}function f(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function d(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(l)(function(e){for(;!ek(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let r,i=(eb.call(e,n)?e[n]:void 0)||(e[n]={}),l=t[n];if(l)for(r in l){eb.call(i,r)||(i[r]=[]);let e=l[r];!function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eg(e,0,0,r)}(i[r],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}([i,...(e||{}).extensions||[]]),content:n(eF),defined:[],document:n(e_),flow:n(eU),lazy:{},string:n(eq),text:n(eW)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},l=[],o=[],a=[],u={attempt:d(function(e,t){m(e,t.from)}),check:d(h),consume:function(e){eP(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),s.events.push(["enter",n,s]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=p(),s.events.push(["exit",t,s]),t},interrupt:d(h,{interrupt:!0})},s={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l,o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(f(e),t)},sliceStream:f,write:function(e){return(o=ey(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),s.events=tc(l,s.events,s),s.events)}},c=t.tokenize.call(s,u);return t.resolveAll&&l.push(t),s;function f(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(o,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function h(e,t){t.restore()}function d(e,t){return function(n,i,l){var o;let c,f,h,d;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,f=0,0===e.length)?l:y(e[f])}function y(e){return function(n){return(d=function(){let e=p(),t=s.previous,n=s.currentConstruct,i=s.events.length,l=Array.from(a);return{from:i,restore:function(){r=e,s.previous=t,s.currentConstruct=n,s.events.length=i,a=l,g()}}}(),h=e,e.partial||(s.currentConstruct=e),e.name&&s.parser.constructs.disable.null.includes(e.name))?x(n):e.tokenize.call(t?Object.assign(Object.create(s),t):s,u,v,x)(n)}}function v(t){return e(h,d),i}function x(e){return(d.restore(),++f<c.length)?y(c[f]):l}}}function m(e,t){e.resolveAll&&!l.includes(e)&&l.push(e),e.resolve&&eg(s.events,t,s.events.length-t,e.resolve(s.events.slice(t),s)),e.resolveTo&&(s.events=e.resolveTo(s.events,s))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(l).document().write((a=1,u="",s=!0,function(e,t,n){let r,i,l,c,f,p=[];for(e=u+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,u="",s&&(65279===e.charCodeAt(0)&&l++,s=void 0);l<e.length;){if(tP.lastIndex=l,c=(r=tP.exec(e))&&void 0!==r.index?r.index:e.length,f=e.charCodeAt(c),!r){u=e.slice(l);break}if(10===f&&l===c&&o)p.push(-3),o=void 0;else switch(o&&(p.push(-5),o=void 0),l<c&&(p.push(e.slice(l,c)),a+=c-l),f){case 0:p.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),p.push(-2);a++<i;)p.push(-1);break;case 10:p.push(-4),a=1;break;default:o=!0,a=1}l=c+1}return n&&(o&&p.push(-5),u&&p.push(u),p.push(null)),p})(n,r,!0))))}}let t_="object"==typeof self?self:globalThis,tR=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[l,o]=t[i];switch(l){case 0:case -1:return n(o,i);case 1:{let e=n([],i);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),i);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new t_[e](t),i)}case 8:return n(BigInt(o),i);case"BigInt":return n(Object(BigInt(o)),i);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new t_[l](o),i)};return r},tj=e=>tR(new Map,e)(0),{toString:tB}={},{keys:tH}=Object,tU=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tB.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tV=([e,t])=>0===e&&("function"===t||"symbol"===t),tq=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,a]=tU(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],u=i([o,n],r);for(let t of tH(r))(e||!tV(tU(r[t])))&&n.push([l(t),l(r[t])]);return u}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(tV(tU(n))||tV(tU(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!tV(tU(n)))&&t.push(l(n));return n}}let{message:u}=r;return i([o,{name:a,message:u}],r)};return l},tW=(e,{json:t,lossy:n}={})=>{let r=[];return tq(!(t||n),!!t,new Map,r)(e),r},tY="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tj(tW(e,t)):structuredClone(e):(e,t)=>tj(tW(e,t));function tK(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let l=e.charCodeAt(n),o="";if(37===l&&eS(e.charCodeAt(n+1))&&eS(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){let t=e.charCodeAt(n+1);l<56320&&t>56319&&t<57344?(o=String.fromCharCode(l,t),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function t$(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tQ(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let tX=function(e){var t,n;if(null==e)return tJ;if("function"==typeof e)return tZ(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=tX(e[n]);return tZ(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,tZ(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,tZ(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function tZ(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function tJ(){return!0}let tG=[];function t1(e,t,n,r){let i,l,o,a;"function"==typeof t&&"function"!=typeof n?(l=void 0,o=t,i=n):(l=t,o=n,i=r);var u=l,s=function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return o(e,r,n)},c=i;"function"==typeof u&&"function"!=typeof s?(c=s,s=u):a=u;let f=tX(a),p=c?-1:1;(function e(t,n,r){let i=t&&"object"==typeof t?t:{};if("string"==typeof i.type){let e="string"==typeof i.tagName?i.tagName:"string"==typeof i.name?i.name:void 0;Object.defineProperty(l,"name",{value:"node ("+t.type+(e?"<"+e+">":"")+")"})}return l;function l(){var i;let l,o,a,h=tG;if((!u||f(t,n,r[r.length-1]||void 0))&&!1===(h=Array.isArray(i=s(t,r))?i:"number"==typeof i?[!0,i]:null==i?tG:[i])[0])return h;if("children"in t&&t.children&&t.children&&"skip"!==h[0])for(o=(c?t.children.length:-1)+p,a=r.concat(t);o>-1&&o<t.children.length;){if(!1===(l=e(t.children[o],o,a)())[0])return l;o="number"==typeof l[1]?l[1]:o+p}return h}})(e,void 0,[])()}function t0(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function t2(e){let t=e.spread;return null==t?e.children.length>1:t}function t4(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let t3={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=tK(i.toLowerCase()),o=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(i,a);let u={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,u);let s={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(t,s),e.applyData(t,s)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t0(e,t);let i={src:tK(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:tK(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t0(e,t);let i={href:tK(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:tK(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=t2(n[r])}return t}(n):t2(t),l={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let u=r[r.length-1];u&&(i||"element"!==u.type||"p"!==u.tagName)&&o.push({type:"text",value:"\n"});let s={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,s),e.applyData(t,s)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=W(t.children[1]),o=q(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,u=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let s={type:"element",tagName:i,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),u.push(s)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,s),e.applyData(t,s)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(t4(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(t4(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:t5,yaml:t5,definition:t5,footnoteDefinition:t5};function t5(){}let t6={}.hasOwnProperty,t9={};function t7(e,t){e.position&&(t.position=function(e){let t=W(e),n=q(e);if(t&&n)return{start:t,end:n}}(e))}function t8(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,tY(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ne(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nt(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nn(e,t){let n=function(e,t){let n=t||t9,r=new Map,i=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=nt(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=nt(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:t8,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...t3,...n.handlers},one:function(e,t){let n=e.type,r=l.handlers[n];if(t6.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tY(n);return r.children=l.all(e),r}return tY(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t6.call(n,"hProperties")||t6.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(l,e,t)},options:n,patch:t7,wrap:ne};return t1(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||t$,r=e.options.footnoteBackLabel||tQ,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],u=-1;for(;++u<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[u]);if(!i)continue;let l=e.all(i),o=String(i.identifier).toUpperCase(),s=tK(o.toLowerCase()),c=0,f=[],p=e.footnoteCounts.get(o);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(u,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+s+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(u,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let h=l[l.length-1];if(h&&"element"===h.type&&"p"===h.tagName){let e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...f)}else l.push(...f);let d={type:"element",tagName:"li",properties:{id:t+"fn-"+s},children:e.wrap(l,!0)};e.patch(i,d),a.push(d)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...tY(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&l.children.push({type:"text",value:"\n"},i),l}function nr(e,t){return e&&"run"in e?async function(n,r){let i=nn(n,{file:r,...t});await e.run(i,r)}:function(n,r){return nn(n,{file:r,...e||t})}}function ni(e){if(e)throw e}var nl=n(3360);function no(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let na={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');nu(e);let r=0,i=-1,l=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else i<0&&(n=!0,i=l+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let o=-1,a=t.length-1;for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else o<0&&(n=!0,o=l+1),a>-1&&(e.codePointAt(l)===t.codePointAt(a--)?a<0&&(i=l):(a=-1,i=o));return r===i?i=o:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(nu(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;nu(e);let n=e.length,r=-1,i=0,l=-1,o=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?l<0?l=n:1!==o&&(o=1):l>-1&&(o=-1)}return l<0||r<0||0===o||1===o&&l===r-1&&l===i+1?"":e.slice(l,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)nu(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){nu(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",l=0,o=-1,a=0,u=-1;for(;++u<=e.length;){if(u<e.length)n=e.codePointAt(u);else if(47===n)break;else n=47;if(47===n){if(o===u-1||1===a);else if(o!==u-1&&2===a){if(i.length<2||2!==l||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",l=0):l=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),o=u,a=0;continue}}else if(i.length>0){i="",l=0,o=u,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",l=2)}else i.length>0?i+="/"+e.slice(o+1,u):i=e.slice(o+1,u),l=u-o-1;o=u,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function nu(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let ns={cwd:function(){return"/"}};function nc(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nf=["history","path","basename","stem","extname","dirname"];class np{constructor(e){let t,n;t=e?nc(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":ns.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nf.length;){let e=nf[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nf.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?na.basename(this.path):void 0}set basename(e){nd(e,"basename"),nh(e,"basename"),this.path=na.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?na.dirname(this.path):void 0}set dirname(e){nm(this.basename,"dirname"),this.path=na.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?na.extname(this.path):void 0}set extname(e){if(nh(e,"extname"),nm(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=na.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nc(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!nc(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),nd(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?na.basename(this.path,this.extname):void 0}set stem(e){nd(e,"stem"),nh(e,"stem"),this.path=na.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new Z(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nh(e,t){if(e&&e.includes(na.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+na.sep+"`")}function nd(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nm(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let ng=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},ny={}.hasOwnProperty;class nv extends ng{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(l,...o){let a=e[++n],u=-1;if(l)return void r(l);for(;++u<t.length;)(null===o[u]||void 0===o[u])&&(o[u]=t[u]);t=o,a?(function(e,t){let n;return function(...t){let l,o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nv,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(nl(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nw("data",this.frozen),this.namespace[e]=t,this):ny.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nw("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=nI(e),n=this.parser||this.Parser;return nk("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nk("process",this.parser||this.Parser),nb("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let l=nI(e),o=n.parse(l);function a(e,n){e||!n?i(e):r?r(n):t(void 0,n)}n.run(o,l,function(e,t,r){var i,l;if(e||!t||!r)return a(e);let o=n.stringify(t,r);"string"==typeof(i=o)||(l=i)&&"object"==typeof l&&"byteLength"in l&&"byteOffset"in l?r.value=o:r.result=o,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nk("processSync",this.parser||this.Parser),nb("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,ni(e),t=r}),nC("processSync","process",n),t}run(e,t,n){nS(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,l){let o=nI(t);r.run(e,o,function(t,r,o){let a=r||e;t?l(t):i?i(a):n(void 0,a,o)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){ni(e),n=t,r=!0}),nC("runSync","run",r),n}stringify(e,t){this.freeze();let n=nI(t),r=this.compiler||this.Compiler;return nb("stringify",r),nS(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nw("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?l(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=nl(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)o(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;o(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...l]=t,o=n[i][1];no(o)&&no(r)&&(r=nl(!0,o,r)),n[i]=[e,r,...l]}}}}let nx=new nv().freeze();function nk(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nb(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nw(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nS(e){if(!no(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nC(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nI(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new np(e)}let nE=[],nT={allowDangerousHtml:!0},nA=/^(https?|ircs?|mailto|xmpp)$/i,nP=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nL(e){let t=function(e){let t=e.rehypePlugins||nE,n=e.remarkPlugins||nE,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nT}:nT;return nx().use(tF).use(n).use(nr,r).use(t)}(e),n=function(e){let t=e.children||"",n=new np;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,l=t.disallowedElements,o=t.skipHtml,a=t.unwrapDisallowed,u=t.urlTransform||nD;for(let e of nP)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return t1(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return o?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ef)if(Object.hasOwn(ef,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ef[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=u(String(n||""),t,e))}}if("element"===e.type){let o=n?!n.includes(e.tagName):!!l&&l.includes(e.tagName);if(!o&&r&&"number"==typeof t&&(o=!r(e,t,i)),o&&i&&"number"==typeof t)return a&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,l,o;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let u=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=u,r=t.jsxDEV,a=function(e,t,i,l){let o=Array.isArray(i.children),a=W(e);return r(t,i,l,o,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,l=t.jsx,o=t.jsxs,a=function(e,t,n,r){let i=Array.isArray(n.children)?o:l;return r?i(t,n,r):i(t,n)}}let s={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:u,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?F:N,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=ei(s,e,void 0);return c&&"string"!=typeof c?c:s.create(e,s.Fragment,{children:c||void 0},void 0)}(e,{Fragment:ep.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ep.jsx,jsxs:ep.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nD(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nA.test(e.slice(0,t))?e:""}},1300:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},u=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var s;return(void 0===t&&(t={}),!(s=e)||i.test(s)||n.test(s))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,u):e.replace(l,u)).replace(r,a))}},1525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]])},1644:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1975:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},3219:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},u=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,s,c,f=arguments[0],p=1,h=arguments.length,d=!1;for("boolean"==typeof f&&(d=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<h;++p)if(t=arguments[p],null!=t)for(n in t)r=u(f,n),f!==(i=u(t,n))&&(d&&i&&(o(i)||(s=l(i)))?(s?(s=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(d,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},3724:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(7924)),i=n(1300);function l(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}l.default=l,e.exports=l},4139:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,u=/^\s+|\s+$/g;function s(e){return e?e.replace(u,""):""}e.exports=function(e,u){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];u=u||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function h(){var e={line:c,column:f};return function(t){return t.position=new d(e),y(r),t}}function d(e){this.start=e,this.end={line:c,column:f},this.source=u.source}d.prototype.content=e;var m=[];function g(t){var n=Error(u.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=u.source,n.line=c,n.column=f,n.source=e,u.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function v(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=h();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}y(r);var k,b=[];for(v(b);k=function(){var e=h(),n=y(i);if(n){if(x(),!y(l))return g("property missing ':'");var r=y(o),u=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return y(a),u}}();)!1!==k&&(b.push(k),v(b));return b}},6891:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]])},7140:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(2115),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var o=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{color:a="currentColor",size:u=24,strokeWidth:s=2,absoluteStrokeWidth:c,children:f,...p}=n;return(0,r.createElement)("svg",{ref:o,...i,width:u,height:u,stroke:a,strokeWidth:c?24*Number(s)/Number(u):s,className:"lucide lucide-".concat(l(e)),...p},[...t.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...(Array.isArray(f)?f:[f])||[]])});return n.displayName="".concat(e),n}},7924:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),l="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;l?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(6301))},8533:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},8632:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(7140).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]])}}]);