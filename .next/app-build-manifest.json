{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/matches/matrix/[requestId]/candidate/[candidateId]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/matches/matrix/[requestId]/candidate/[candidateId]/page.js"], "/api/matches/matrix/[requestId]/candidates/[candidateId]/detail/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/matches/matrix/[requestId]/candidates/[candidateId]/detail/route.js"], "/api/matches/matrix/[requestId]/candidates/[candidateId]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/matches/matrix/[requestId]/candidates/[candidateId]/route.js"], "/matches/matrix/[requestId]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/matches/matrix/[requestId]/page.js"], "/api/matches/matrix/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/matches/matrix/route.js"]}}